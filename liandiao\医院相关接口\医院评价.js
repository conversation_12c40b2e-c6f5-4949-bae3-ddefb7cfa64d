import { getHospitalEvaluations } from '../../services/hospitalService';

const params = {
    hospitalId: 1
};

getHospitalEvaluations(params)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('医院评价:', res.data);
        } else {
            wx.showToast({
                title: res.message || '获取医院评价失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('获取医院评价出错', err);
        wx.showToast({
            title: '获取医院评价出错，请重试',
            icon: 'none'
        });
    });