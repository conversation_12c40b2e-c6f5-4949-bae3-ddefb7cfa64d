// components/form/custom-button.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 按钮文本
    text: {
      type: String,
      value: '按钮'
    },
    // 按钮类型：primary, info, success, warning, danger, default
    type: {
      type: String,
      value: 'default'
    },
    // 按钮尺寸：large, normal, small, mini
    size: {
      type: String,
      value: 'normal'
    },
    // 是否为块级按钮
    block: {
      type: Boolean,
      value: false
    },
    // 是否为圆角按钮
    round: {
      type: Boolean,
      value: false
    },
    // 是否为朴素按钮
    plain: {
      type: Boolean,
      value: false
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    // 是否加载中
    loading: {
      type: Boolean,
      value: false
    },
    // 加载文本
    loadingText: {
      type: String,
      value: '加载中...'
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 点击按钮
    onClick: function() {
      if (!this.properties.disabled && !this.properties.loading) {
        this.triggerEvent('click');
      }
    }
  }
})