/* components/card/hospital-card.wxss */

/* 通用卡片样式 */
.hospital-card {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 紧凑模式 */
.hospital-card-compact {
  width: 100%;
  height: 240rpx;
  display: flex;
  flex-direction: column;
}

.hospital-image-compact {
  width: 100%;
  height: 180rpx;
}

.hospital-info-compact {
  padding: 8rpx 12rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.hospital-name-compact {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}

.hospital-category-compact {
  font-size: 22rpx;
  color: #666;
  margin-top: 4rpx;
}

/* 普通模式 */
.hospital-card-normal {
  display: flex;
  padding: 20rpx;
}

.hospital-image-normal {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.hospital-info-normal {
  margin-left: 20rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.hospital-name-normal {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.hospital-rating-normal {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.rating-stars {
  display: flex;
  align-items: center;
}

.star-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}

.rating-value {
  font-size: 24rpx;
  color: #ff9800;
  margin-left: 8rpx;
}

.rating-count {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}

.hospital-address-normal {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.hospital-actions-normal {
  display: flex;
  margin-top: auto;
}

.hospital-action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
}

.action-icon {
  width: 36rpx;
  height: 36rpx;
  margin-bottom: 4rpx;
}

.action-text {
  font-size: 22rpx;
  color: #666;
}

/* 详细模式 */
.hospital-card-detailed {
  display: flex;
  flex-direction: column;
}

.hospital-image-detailed {
  width: 100%;
  height: 300rpx;
}

.hospital-info-detailed {
  padding: 24rpx;
}

.hospital-header-detailed {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
}

.hospital-name-detailed {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.hospital-rating-detailed {
  display: flex;
  align-items: center;
}

.hospital-meta-detailed {
  margin-bottom: 20rpx;
}

.hospital-meta-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.meta-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.meta-text {
  font-size: 28rpx;
  color: #666;
}

.hospital-description-detailed {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 24rpx;
}

.hospital-services {
  margin-bottom: 24rpx;
}

.hospital-services-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
}

.hospital-services-list {
  display: flex;
  flex-wrap: wrap;
}

.hospital-service-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 48rpx;
  padding: 0 16rpx;
  background-color: #f5f5f5;
  color: #666;
  font-size: 24rpx;
  border-radius: 24rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

.hospital-actions-detailed {
  display: flex;
  justify-content: space-between;
}

.hospital-action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  margin: 0 10rpx;
}

.hospital-action-button:first-child {
  margin-left: 0;
}

.hospital-action-button:last-child {
  margin-right: 0;
}

.hospital-action-button.primary {
  background-color: #4eaaa8;
}

.hospital-action-button.primary .action-text {
  color: #fff;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-secondary {
  color: #666;
}