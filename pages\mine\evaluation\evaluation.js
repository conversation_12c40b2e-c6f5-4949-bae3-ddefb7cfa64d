// pages/mine/evaluation/evaluation.js
import shopService from '../../../services/shopService';
import evaluationService from '../../../services/evaluationService'; // 新增：救助站评价服务
import userService from '../../../services/userService';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    evaluations: [], // 评价列表
    
    loading: true,
    refreshing: false,
    
    // 分页参数
    page: 1,
    pageSize: 10,
    
    // 是否还有更多数据
    hasMore: true,
    
    // 总数据量
    total: 0,
    
    // 新增：评价类型控制
    currentTab: 0, // 0: 商店评价, 1: 救助站评价
    tabs: [
      { id: 0, name: '商店评价', type: 'shop' },
      { id: 1, name: '救助站评价', type: 'rescue' }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查登录状态
    if (!userService.isLoggedIn()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }, 1500);
      return;
    }

    // 新增：根据参数设置当前tab
    const { tab } = options;
    if (tab) {
      const tabIndex = parseInt(tab);
      if (tabIndex >= 0 && tabIndex < this.data.tabs.length) {
        this.setData({ currentTab: tabIndex });
      }
    }

    this.loadEvaluations();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    if (userService.isLoggedIn()) {
      this.loadEvaluations();
    }
  },

  /**
   * 新增：切换tab
   */
  switchTab(e) {
    const { index } = e.currentTarget.dataset;
    if (index !== this.data.currentTab) {
      this.setData({
        currentTab: index,
        // 重置数据
        evaluations: [],
        page: 1,
        hasMore: true,
        total: 0
      });
      this.loadEvaluations();
    }
  },

  /**
   * 修改：根据当前tab加载对应的评价数据
   */
  loadEvaluations(isRefresh = false) {
    if (isRefresh) {
      this.setData({
        page: 1,
        evaluations: [],
        hasMore: true,
        total: 0,
        refreshing: true
      });
    }

    const { page, pageSize, currentTab } = this.data;
    const currentTabType = this.data.tabs[currentTab].type;

    // 根据当前tab类型调用不同的接口
    if (currentTabType === 'shop') {
      this.loadShopEvaluations(page, pageSize, isRefresh);
    } else if (currentTabType === 'rescue') {
      this.loadRescueEvaluations(page, pageSize, isRefresh);
    }
  },

  /**
   * 原有：加载商店评价
   */
  loadShopEvaluations(page, pageSize, isRefresh) {
    // 调用商店评价接口，使用GET方法
    shopService.getShopEvaluations(page, pageSize).then(res => {
      console.log('商店评价数据:', res);
      this.handleEvaluationResponse(res, isRefresh, 'shop');
    }).catch(err => {
      console.error('加载商店评价失败:', err);
      this.handleApiError(err, '商店评价信息');
    });
  },

  /**
   * 新增：加载救助站评价 - 符合API文档要求
   * 接口：GET /users/rescuestation/view/evaluation
   */
  loadRescueEvaluations(page, pageSize, isRefresh) {
    // 调用救助站评价接口
    evaluationService.getUserEvaluations({ page, pageSize }).then(res => {
      console.log('救助站评价数据:', res);
      this.handleEvaluationResponse(res, isRefresh, 'rescue');
    }).catch(err => {
      console.error('加载救助站评价失败:', err);
      this.handleApiError(err, '救助站评价信息');
    });
  },

  /**
   * 新增：统一处理评价响应数据
   */
  handleEvaluationResponse(res, isRefresh, type) {
    if (res.code === 200) {
      const newEvaluations = res.data || [];
      const total = res.total || 0;
      const currentEvaluations = isRefresh ? [] : this.data.evaluations;
      
      // 处理数据结构，根据API文档返回字段进行映射
      const processedEvaluations = newEvaluations.map(item => ({
        ...item,
        // 处理头像路径
        avatar: this.processImagePath(item.avatar, type),
        // 确保评分在1-5范围内
        rating: Math.max(1, Math.min(5, item.rating || 5)),
        // 添加评价类型标识
        evaluationType: type
      }));
      
      const allEvaluations = [...currentEvaluations, ...processedEvaluations];
      const hasMore = allEvaluations.length < total;
      
      this.setData({
        evaluations: allEvaluations,
        page: this.data.page + 1,
        hasMore: hasMore,
        total: total,
        loading: false,
        refreshing: false
      });
    } else {
      this.handleLoadError(res.message || '获取评价信息失败');
    }
  },

  /**
   * 统一处理API错误
   */
  handleApiError(err, type) {
    let errorMessage = `获取${type}失败，请重试`;
    
    // 根据API文档的错误状态码处理
    if (err.statusCode === 403 || err.code === 403) {
      errorMessage = '未登录或登录已过期';
      setTimeout(() => {
        wx.navigateTo({ url: '/pages/login/login' });
      }, 1500);
    } else if (err.statusCode === 404 || err.code === 404) {
      errorMessage = '暂无评价记录';
    } else if (err.statusCode === 400 || err.code === 400) {
      errorMessage = '请求参数错误';
    } else if (err.message) {
      errorMessage = err.message;
    }
    
    this.handleLoadError(errorMessage);
  },

  /**
   * 修改：根据评价类型处理图片路径
   */
  processImagePath(imagePath, type) {
    if (!imagePath) return '/assets/images/default-avatar.png';
    
    // 如果是完整URL，直接返回
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return imagePath;
    }
    
    // 根据评价类型处理路径
    if (type === 'rescue') {
      // 救助站评价：相对路径需拼接 D:/images/ - 按照API文档要求
      if (imagePath.startsWith('D:/images/')) {
        return imagePath; // 已经是完整路径
      }
      return `D:/images/${imagePath}`;
    } else {
      // 商店评价：保持原有逻辑
      return `D:/images/${imagePath}`;
    }
  },

  /**
   * 处理加载错误
   */
  handleLoadError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });

    this.setData({
      loading: false,
      refreshing: false
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadEvaluations(true);
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    const { hasMore } = this.data;

    if (hasMore) {
      this.loadEvaluations();
    } else {
      wx.showToast({
        title: '没有更多数据了',
        icon: 'none'
      });
    }
  }
});