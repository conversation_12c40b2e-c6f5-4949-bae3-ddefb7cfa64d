// pages/mine/mine.js
import userService from '../../services/userService';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    isLoggedIn: false,
    loading: true,
    isUploadingAvatar: false,
    
    // 修改地址相关
    showAddressModal: false,
    tempAddress: '',
    tempUserType: '',
    userTypeOptions: [
      { value: '普通用户', label: '普通用户' },
      { value: '宠物医院', label: '宠物医院' },
      { value: '救助站', label: '救助站' },
      { value: '宠物商店', label: '宠物商店' }
    ],
    selectedUserTypeIndex: 0,
    isUpdatingAddress: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.loadUserInfo();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.loadUserInfo();
  },

  /**
   * 加载用户信息
   */
  loadUserInfo: function() {
    console.log('开始加载用户信息');
    
    this.setData({
      loading: true
    });

    // 检查本地是否有token
    const token = wx.getStorageSync('token');
    if (!token) {
      console.log('未找到token，用户未登录');
      this.setData({
        isLoggedIn: false,
        userInfo: null,
        loading: false
      });
      return;
    }

    // 尝试获取用户信息
    userService.getUserInfo()
      .then(userInfo => {
        console.log('获取用户信息成功:', userInfo);
        
        this.setData({
          userInfo: userInfo,
          isLoggedIn: true,
          loading: false
        });
        
        // 将用户信息保存到本地
        wx.setStorageSync('userInfo', userInfo);
      })
      .catch(err => {
        console.error('获取用户信息失败:', err);
        
        this.setData({
          isLoggedIn: false,
          userInfo: null,
          loading: false
        });
        
        // 如果获取用户信息失败，可能是token过期，清除本地存储
        wx.removeStorageSync('token');
        wx.removeStorageSync('userInfo');
      });
  },

  /**
   * 跳转到登录页面
   */
  login: function() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  /**
   * 跳转到登录页面 (兼容goToLogin方法名)
   */
  goToLogin: function() {
    this.login();
  },

  /**
   * 安全跳转页面（带错误处理）
   */
  safeNavigateTo: function(url, title = '该功能') {
    wx.navigateTo({
      url: url,
      fail: () => {
        wx.showToast({
          title: `${title}正在开发中`,
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  /**
   * 跳转到我的发布
   */
  toMyPosts: function() {
    this.safeNavigateTo('/pages/mine/posts/posts', '我的发布');
  },

  /**
   * 跳转到我的收藏
   */
  toMyFavorites: function() {
    this.safeNavigateTo('/pages/mine/favorites/favorites', '我的收藏');
  },

  /**
   * 跳转到我的订单
   */
  toMyOrders: function() {
    this.safeNavigateTo('/pages/mine/order/order', '我的订单');
  },

  /**
   * 跳转到我的预约
   */
  toMyReservations: function() {
    this.safeNavigateTo('/pages/mine/reservation/reservation', '我的预约');
  },

  /**
   * 跳转到我的评价
   */
  toMyEvaluations: function() {
    this.safeNavigateTo('/pages/mine/evaluation/evaluation', '我的评价');
  },

  /**
   * 跳转到设置页面
   */
  toSettings: function() {
    this.safeNavigateTo('/pages/mine/settings/settings', '设置');
  },

  /**
   * 显示修改地址弹窗
   */
  showEditAddress: function() {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 初始化表单数据
    const currentAddress = this.data.userInfo.address || '';
    const currentUserType = this.data.userInfo.usertype || '普通用户';
    
    // 找到当前用户类型在选项中的索引
    const userTypeIndex = this.data.userTypeOptions.findIndex(
      option => option.value === currentUserType
    );

    this.setData({
      showAddressModal: true,
      tempAddress: currentAddress,
      tempUserType: currentUserType,
      selectedUserTypeIndex: userTypeIndex >= 0 ? userTypeIndex : 0
    });
  },

  /**
   * 隐藏修改地址弹窗
   */
  hideAddressModal: function() {
    this.setData({
      showAddressModal: false,
      tempAddress: '',
      tempUserType: '',
      selectedUserTypeIndex: 0
    });
  },

  /**
   * 地址输入改变
   */
  onAddressInput: function(e) {
    this.setData({
      tempAddress: e.detail.value
    });
  },

  /**
   * 用户类型选择改变
   */
  onUserTypeChange: function(e) {
    const index = parseInt(e.detail.value);
    this.setData({
      selectedUserTypeIndex: index,
      tempUserType: this.data.userTypeOptions[index].value
    });
  },

  /**
   * 提交修改地址
   */
  submitAddressUpdate: function() {
    const { tempAddress, tempUserType } = this.data;

    // 验证输入
    if (!tempAddress || tempAddress.trim() === '') {
      wx.showToast({
        title: '请输入地址',
        icon: 'none'
      });
      return;
    }

    if (!tempUserType) {
      wx.showToast({
        title: '请选择用户类型',
        icon: 'none'
      });
      return;
    }

    // 防止重复提交
    if (this.data.isUpdatingAddress) {
      return;
    }

    this.setData({
      isUpdatingAddress: true
    });

    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    // 调用服务层方法
    userService.updateAddress(tempAddress.trim(), tempUserType)
      .then(result => {
        wx.hideLoading();
        
        console.log('地址修改成功:', result);
        
        // 显示成功提示
        wx.showToast({
          title: result.message || '修改成功',
          icon: 'success',
          duration: 2000
        });

        // 更新页面显示的用户信息
        const updatedUserInfo = { ...this.data.userInfo };
        updatedUserInfo.address = tempAddress.trim();
        updatedUserInfo.usertype = tempUserType;
        
        this.setData({
          userInfo: updatedUserInfo,
          showAddressModal: false,
          isUpdatingAddress: false
        });

        // 更新本地存储
        wx.setStorageSync('userInfo', updatedUserInfo);

        // 重新加载用户信息以确保数据同步
        setTimeout(() => {
          this.loadUserInfo();
        }, 1000);
      })
      .catch(err => {
        wx.hideLoading();
        
        console.error('地址修改失败:', err);
        
        this.setData({
          isUpdatingAddress: false
        });

        let errorMessage = '修改失败，请重试';
        if (err.message) {
          errorMessage = err.message;
        }

        wx.showModal({
          title: '修改失败',
          content: errorMessage,
          showCancel: false,
          confirmText: '知道了'
        });
      });
  },

  /**
   * 退出登录
   */
  logout: function() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      confirmText: '确定',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '退出中...',
            mask: true
          });

          // 使用userService的logout方法
          userService.logout()
            .then(() => {
              wx.hideLoading();
              
              // 更新页面状态
              this.setData({
                isLoggedIn: false,
                userInfo: null
              });
              
              wx.showToast({
                title: '已退出登录',
                icon: 'success',
                duration: 1500
              });

              // 延迟跳转到登录页
              setTimeout(() => {
                wx.reLaunch({
                  url: '/pages/login/login'
                });
              }, 1500);
            })
            .catch(err => {
              wx.hideLoading();
              console.error('退出登录失败:', err);
              
              // 即使退出失败，也强制清除本地状态
              this.setData({
                isLoggedIn: false,
                userInfo: null
              });
              
              wx.showToast({
                title: '已退出登录',
                icon: 'success',
                duration: 1500
              });

              setTimeout(() => {
                wx.reLaunch({
                  url: '/pages/login/login'
                });
              }, 1500);
            });
        }
      }
    });
  },

  /**
   * 删除账户
   */
  deleteAccount: function() {
    // 检查是否已登录
    if (!this.data.isLoggedIn || !this.data.userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '危险操作',
      content: '注销账户后将无法恢复，确定要继续吗？',
      confirmText: '继续',
      cancelText: '取消',
      confirmColor: '#FF6F61',
      success: (res) => {
        if (res.confirm) {
          // 二次确认
          wx.showModal({
            title: '最后确认',
            content: '请再次确认是否要注销账户？此操作不可撤销！',
            confirmText: '确认注销',
            cancelText: '取消',
            confirmColor: '#FF6F61',
            success: (res2) => {
              if (res2.confirm) {
                this.performDeleteAccount();
              }
            }
          });
        }
      }
    });
  },

  /**
   * 执行删除账户操作
   */
  performDeleteAccount: function() {
    wx.showLoading({
      title: '处理中...',
      mask: true
    });

    userService.deleteAccount()
      .then((res) => {
        wx.hideLoading();
        
        wx.showToast({
          title: res.message || '账户已注销',
          icon: 'success',
          duration: 2000
        });

        // 清除页面状态
        this.setData({
          isLoggedIn: false,
          userInfo: null
        });

        // 跳转到登录页
        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/login/login'
          });
        }, 2000);
      })
      .catch(err => {
        wx.hideLoading();
        console.error('删除账户失败:', err);
        
        let errorMessage = '注销失败，请重试';
        if (err.message) {
          errorMessage = err.message;
        }
        
        wx.showModal({
          title: '注销失败',
          content: errorMessage,
          showCancel: false,
          confirmText: '知道了'
        });
      });
  },

  /**
   * 点击头像处理
   */
  onAvatarTap: function() {
    if (!this.data.isLoggedIn) {
      this.login();
      return;
    }

    // 已登录用户，显示头像操作菜单
    wx.showActionSheet({
      itemList: ['更换头像', '查看个人信息'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 更换头像
          this.changeAvatar();
        } else if (res.tapIndex === 1) {
          // 查看个人信息
          this.safeNavigateTo('/pages/mine/profile/profile', '个人信息');
        }
      }
    });
  },

  /**
   * 更换头像
   */
  changeAvatar: function() {
    if (this.data.isUploadingAvatar) {
      wx.showToast({
        title: '正在上传中，请稍候',
        icon: 'none'
      });
      return;
    }

    wx.showActionSheet({
      itemList: ['从相册选择', '拍照'],
      success: (res) => {
        let sourceType;
        if (res.tapIndex === 0) {
          sourceType = ['album']; // 相册
        } else if (res.tapIndex === 1) {
          sourceType = ['camera']; // 拍照
        } else {
          return;
        }

        this.chooseAndUploadImage(sourceType);
      }
    });
  },

  /**
   * 选择并上传图片
   * @param {Array} sourceType - 图片来源类型
   */
  chooseAndUploadImage: function(sourceType) {
    wx.chooseImage({
      count: 1, // 只能选择1张
      sizeType: ['compressed'], // 压缩图
      sourceType: sourceType,
      success: (res) => {
        const filePath = res.tempFilePaths[0];
        if (filePath) {
          this.uploadAvatar(filePath);
        }
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 上传头像
   * @param {string} filePath - 图片文件路径
   */
  uploadAvatar: function(filePath) {
    console.log('开始上传头像:', filePath);

    // 设置上传状态
    this.setData({
      isUploadingAvatar: true
    });

    wx.showLoading({
      title: '上传中...',
      mask: true
    });

    // 调用 userService 的上传方法
    userService.uploadImage(filePath, '头像')
      .then((result) => {
        wx.hideLoading();
        
        console.log('头像上传成功:', result);
        
        // 显示成功提示
        wx.showToast({
          title: result.message || '头像更新成功',
          icon: 'success',
          duration: 2000
        });

        // 更新页面显示的用户信息
        const updatedUserInfo = { ...this.data.userInfo };
        updatedUserInfo.avatar = result.data.url;
        
        this.setData({
          userInfo: updatedUserInfo,
          isUploadingAvatar: false
        });

        // 重新加载用户信息以确保数据同步
        setTimeout(() => {
          this.loadUserInfo();
        }, 1000);
      })
      .catch((err) => {
        wx.hideLoading();
        
        console.error('头像上传失败:', err);
        
        this.setData({
          isUploadingAvatar: false
        });

        let errorMessage = '头像上传失败，请重试';
        if (err.message) {
          // 特殊处理Mock环境的成功情况
          if (err.message.includes('Mock环境：图片上传模拟成功')) {
            wx.showToast({
              title: 'Mock环境：头像更新成功',
              icon: 'success',
              duration: 2000
            });
            
            // Mock环境也要更新界面
            setTimeout(() => {
              this.loadUserInfo();
            }, 1000);
            return;
          }
          
          errorMessage = err.message;
        }

        wx.showModal({
          title: '上传失败',
          content: errorMessage,
          showCancel: false,
          confirmText: '知道了'
        });
      });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.loadUserInfo();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    // 个人中心页面通常不需要上拉加载更多
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '宠物之家 - 找到你的毛孩子',
      path: '/pages/home/<USER>'
    };
  }
});