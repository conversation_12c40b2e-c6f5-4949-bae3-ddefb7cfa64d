<!-- components/filter/filter-bar.wxml -->
<view class="filter-bar">
  <view class="filter-items">
    <!-- 筛选项列表 -->
    <view 
      class="filter-item {{expandedIndex === index ? 'active' : ''}} {{filters[index].value && filters[index].value.length ? 'has-value' : ''}}"
      wx:for="{{filters}}"
      wx:key="key"
      wx:if="{{!item.inMore}}"
      data-index="{{index}}"
      bindtap="onClickFilter"
    >
      <view class="filter-item-text">{{item.text}}</view>
      <view class="filter-item-arrow"></view>
    </view>
    
    <!-- 更多筛选按钮 -->
    <view 
      class="filter-item" 
      wx:if="{{showMore}}"
      bindtap="onClickMore"
    >
      <view class="filter-item-text">更多</view>
      <view class="filter-item-arrow"></view>
    </view>
  </view>
  
  <!-- 筛选项下拉面板 -->
  <view 
    class="filter-dropdown {{expandedIndex !== -1 ? 'show' : ''}}"
    catchtouchmove="preventScroll"
  >
    <view class="filter-dropdown-mask" bindtap="onClickFilter"></view>
    <view class="filter-dropdown-content" wx:if="{{expandedIndex !== -1}}">
      <!-- 选项列表 -->
      <scroll-view 
        class="filter-options" 
        scroll-y="{{true}}"
      >
        <view 
          class="filter-option {{tempValues[filters[expandedIndex].key] === item.value || (filters[expandedIndex].multiple && tempValues[filters[expandedIndex].key].indexOf(item.value) > -1) ? 'active' : ''}}"
          wx:for="{{filters[expandedIndex].options}}"
          wx:key="value"
          data-filter-index="{{expandedIndex}}"
          data-option-index="{{index}}"
          bindtap="onClickOption"
        >
          <view class="filter-option-text">{{item.text}}</view>
          <view class="filter-option-icon" wx:if="{{filters[expandedIndex].multiple && tempValues[filters[expandedIndex].key].indexOf(item.value) > -1}}">
            <view class="filter-option-icon-checked"></view>
          </view>
        </view>
      </scroll-view>
      
      <!-- 多选操作按钮 -->
      <view class="filter-dropdown-actions" wx:if="{{filters[expandedIndex].multiple}}">
        <view class="filter-dropdown-action reset" bindtap="onClickReset">重置</view>
        <view class="filter-dropdown-action confirm" bindtap="onClickConfirm">确定</view>
      </view>
    </view>
  </view>
  
  <!-- 更多筛选面板 -->
  <view 
    class="more-panel {{showMorePanel ? 'show' : ''}}"
    catchtouchmove="preventScroll"
  >
    <view class="more-panel-mask" bindtap="onCloseMorePanel"></view>
    <view class="more-panel-content">
      <view class="more-panel-header">
        <view class="more-panel-title">筛选</view>
        <view class="more-panel-close" bindtap="onCloseMorePanel">
          <image class="close-icon" src="/assets/images/close.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <scroll-view 
        class="more-panel-body" 
        scroll-y="{{true}}"
      >
        <view 
          class="more-filter-group"
          wx:for="{{filters}}"
          wx:key="key"
          wx:if="{{item.inMore}}"
        >
          <view class="more-filter-title">{{item.text}}</view>
          <view class="more-filter-options">
            <view 
              class="more-filter-option {{tempValues[item.key] === option.value || (item.multiple && tempValues[item.key].indexOf(option.value) > -1) ? 'active' : ''}}"
              wx:for="{{item.options}}"
              wx:key="value"
              wx:for-item="option"
              wx:for-index="optionIndex"
              data-filter-index="{{index}}"
              data-option-index="{{optionIndex}}"
              bindtap="onClickOption"
            >
              {{option.text}}
            </view>
          </view>
        </view>
      </scroll-view>
      
      <view class="more-panel-footer">
        <view class="more-panel-action reset" bindtap="onClickReset">重置</view>
        <view class="more-panel-action confirm" bindtap="onConfirmMorePanel">确定</view>
      </view>
    </view>
  </view>
</view>