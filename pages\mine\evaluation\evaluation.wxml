<view class="container">
  <!-- 新增：Tab导航 -->
  <view class="tab-bar">
    <view 
      wx:for="{{tabs}}" 
      wx:key="id" 
      class="tab-item {{currentTab === index ? 'active' : ''}}"
      data-index="{{index}}"
      bindtap="switchTab"
    >
      <text class="tab-text">{{item.name}}</text>
      <view wx:if="{{currentTab === index}}" class="tab-indicator"></view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 内容区域 -->
  <view wx:else class="content">
    <!-- 评价列表 -->
    <view class="evaluation-list">
      <view wx:if="{{evaluations.length === 0}}" class="empty-container">
        <image class="empty-image" src="/assets/images/empty.png" mode="aspectFit" />
        <!-- 修改：根据当前tab显示不同的空状态文案 -->
        <text class="empty-text">
          {{currentTab === 0 ? '暂无商店评价记录' : '暂无救助站评价记录'}}
        </text>
        <text class="empty-subtitle">
          {{currentTab === 0 ? '还没有对商店进行评价' : '还没有对救助站进行评价'}}
        </text>
      </view>
      <view wx:else>
        <!-- 统计信息 -->
        <view class="stats-header">
          <text class="stats-text">
            共 {{total}} 条{{currentTab === 0 ? '商店' : '救助站'}}评价
          </text>
        </view>
        
        <view wx:for="{{evaluations}}" wx:key="id" class="evaluation-item">
          <view class="evaluation-header">
            <view class="user-info">
              <image class="user-avatar" src="{{item.avatar}}" mode="aspectFill" />
              <view class="user-details">
                <text class="username">{{item.username}}</text>
                <view class="rating">
                  <view class="stars-container">
                    <text wx:for="{{[1,2,3,4,5]}}" wx:for-item="star" wx:key="star" 
                          class="star {{star <= item.rating ? 'filled' : ''}}">★</text>
                  </view>
                  <text class="rating-text">{{item.rating}}分</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="evaluation-content">
            <text class="evaluation-text">{{item.content}}</text>
          </view>
        </view>
        
        <!-- 加载更多提示 -->
        <view wx:if="{{hasMore}}" class="load-more-container">
          <text class="load-more-text">上拉加载更多</text>
        </view>
        <view wx:else class="no-more-container">
          <text class="no-more-text">没有更多数据了</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 下拉刷新提示 -->
  <view wx:if="{{refreshing}}" class="refresh-hint">
    <view class="refresh-icon"></view>
    <text>正在刷新...</text>
  </view>
</view>