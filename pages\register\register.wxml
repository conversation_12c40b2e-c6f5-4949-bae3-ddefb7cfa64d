<!-- pages/register/register.wxml -->
<view class="container">
  <view class="header">
    <image class="logo" src="/assets/images/logo.png" mode="aspectFit"></image>
    <view class="title">用户注册</view>
    <view class="subtitle">创建你的宠物之家账号</view>
  </view>
  
  <view class="form-container">
    <!-- 全局错误信息 -->
    <view class="global-error" wx:if="{{errorMessage}}">
      <image class="error-icon" src="/assets/images/error-icon.png" mode="aspectFit"></image>
      <text>{{errorMessage}}</text>
    </view>
    
    <!-- 用户名 -->
    <view class="form-group">
      <text class="label">用户名<text class="required">*</text></text>
      <view class="input-wrapper">
        <input 
          class="input {{fieldErrors.username ? 'input-error' : ''}}" 
          type="text" 
          placeholder="请输入用户名" 
          bindinput="usernameInput" 
          value="{{username}}" 
          maxlength="20"
        />
      </view>
      <view class="field-error" wx:if="{{fieldErrors.username}}">{{fieldErrors.username}}</view>
    </view>
    
    <!-- 账号 -->
    <view class="form-group">
      <text class="label">账号<text class="required">*</text></text>
      <view class="input-wrapper">
        <input 
          class="input {{fieldErrors.account ? 'input-error' : ''}}" 
          type="text" 
          placeholder="请输入手机号、邮箱或账号" 
          bindinput="accountInput" 
          value="{{account}}" 
        />
      </view>
      <view class="field-error" wx:if="{{fieldErrors.account}}">{{fieldErrors.account}}</view>
    </view>
    
    <!-- 密码 -->
    <view class="form-group">
      <text class="label">密码<text class="required">*</text></text>
      <view class="input-wrapper">
        <input 
          class="input {{fieldErrors.password ? 'input-error' : ''}}" 
          type="{{passwordVisible ? 'text' : 'password'}}" 
          placeholder="请输入密码" 
          bindinput="passwordInput" 
          value="{{password}}" 
          maxlength="20"
        />
        <view class="eye-icon" bindtap="togglePasswordVisible">
          <image src="{{passwordVisible ? '/assets/images/eye-open.png' : '/assets/images/eye-close.png'}}" mode="aspectFit"></image>
        </view>
      </view>
      <view class="field-error" wx:if="{{fieldErrors.password}}">{{fieldErrors.password}}</view>
    </view>
    
    <!-- 确认密码 -->
    <view class="form-group">
      <text class="label">确认密码<text class="required">*</text></text>
      <view class="input-wrapper">
        <input 
          class="input {{fieldErrors.confirmPassword ? 'input-error' : ''}}" 
          type="{{confirmPasswordVisible ? 'text' : 'password'}}" 
          placeholder="请再次输入密码" 
          bindinput="confirmPasswordInput" 
          value="{{confirmPassword}}" 
          maxlength="20"
        />
        <view class="eye-icon" bindtap="toggleConfirmPasswordVisible">
          <image src="{{confirmPasswordVisible ? '/assets/images/eye-open.png' : '/assets/images/eye-close.png'}}" mode="aspectFit"></image>
        </view>
      </view>
      <view class="field-error" wx:if="{{fieldErrors.confirmPassword}}">{{fieldErrors.confirmPassword}}</view>
    </view>
    
    <!-- 地址 -->
    <view class="form-group">
      <text class="label">地址<text class="required">*</text></text>
      <view class="input-wrapper">
        <input 
          class="input {{fieldErrors.address ? 'input-error' : ''}}" 
          type="text" 
          placeholder="请输入您的地址" 
          bindinput="addressInput" 
          value="{{address}}" 
        />
      </view>
      <view class="field-error" wx:if="{{fieldErrors.address}}">{{fieldErrors.address}}</view>
    </view>
    
    <!-- 用户类型 -->
    <view class="form-group">
      <text class="label">用户类型<text class="required">*</text></text>
      <picker bindchange="userTypeChange" value="{{userType}}" range="{{userTypes}}">
        <view class="picker">
          <text>{{userType}}</text>
          <view class="picker-arrow">
            <image src="/assets/images/arrow-down.png" mode="aspectFit"></image>
          </view>
        </view>
      </picker>
    </view>
    
    <!-- 注册按钮 -->
    <button 
      class="btn-register {{isSubmitting ? 'btn-submitting' : ''}}" 
      bindtap="register" 
      disabled="{{isSubmitting}}"
    >
      <view class="loading" wx:if="{{isSubmitting}}">
        <image class="loading-icon" src="/assets/images/loading.gif" mode="aspectFit"></image>
        注册中...
      </view>
      <text wx:else>立即注册</text>
    </button>
    
    <!-- 登录链接 -->
    <view class="login-link">
      已有账号？<text class="link" bindtap="goToLogin">立即登录</text>
    </view>
  </view>
  
  <!-- 底部说明 -->
  <view class="footer">
    <view class="tips">
      <text>• 注册即同意《用户协议》和《隐私政策》</text>
      <text>• 请填写真实有效的信息</text>
      <text>• 密码长度为6-20位字符</text>
    </view>
  </view>
</view>