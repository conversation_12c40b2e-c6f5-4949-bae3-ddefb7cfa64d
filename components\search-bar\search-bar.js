// components/search-bar/search-bar.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 占位文本
    placeholder: {
      type: String,
      value: '搜索'
    },
    // 搜索值
    value: {
      type: String,
      value: ''
    },
    // 输入框形状，可选值为 round 或 square
    shape: {
      type: String,
      value: 'round'
    },
    // 搜索框背景色
    background: {
      type: String,
      value: '#f5f5f5'
    },
    // 是否显示取消按钮
    showCancel: {
      type: Boolean,
      value: false
    },
    // 取消按钮文字
    cancelText: {
      type: String,
      value: '取消'
    },
    // 是否自动聚焦
    focus: {
      type: Boolean,
      value: false
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    innerValue: '',
    isFocus: false
  },

  /**
   * 数据监听器
   */
  observers: {
    'value': function(value) {
      this.setData({
        innerValue: value
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 输入事件
    onInput: function(e) {
      const { value } = e.detail;
      this.setData({
        innerValue: value
      });
      this.triggerEvent('input', { value });
    },
    
    // 确认搜索
    onConfirm: function(e) {
      const { value } = e.detail;
      this.triggerEvent('search', { value });
    },
    
    // 清除输入
    onClear: function() {
      this.setData({
        innerValue: ''
      });
      this.triggerEvent('clear');
      this.triggerEvent('input', { value: '' });
    },
    
    // 取消搜索
    onCancel: function() {
      this.setData({
        innerValue: ''
      });
      this.triggerEvent('cancel');
    },
    
    // 聚焦事件
    onFocus: function(e) {
      this.setData({
        isFocus: true
      });
      this.triggerEvent('focus', e.detail);
    },
    
    // 失焦事件
    onBlur: function(e) {
      this.setData({
        isFocus: false
      });
      this.triggerEvent('blur', e.detail);
    },
    
    // 点击搜索栏
    onClick: function() {
      if (this.properties.disabled) {
        this.triggerEvent('click');
      }
    }
  }
})