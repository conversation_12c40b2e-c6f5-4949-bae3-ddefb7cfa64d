// pages/shop/list/list.js
// 引入店铺服务
import shopService from '../../../services/shopService'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    petList: [],        // 宠物列表
    currentPage: 1,     // 当前页码
    pageSize: 10,       // 每页数量
    total: 0,           // 总数
    loading: false,     // 加载状态
    hasMore: true,      // 是否还有更多数据
    isEmpty: false      // 是否为空
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.checkLoginStatus()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时刷新数据
    this.refreshData()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.loadMoreData()
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '宠物列表',
      path: '/pages/shop/list/list'
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const token = wx.getStorageSync('token')
    if (!token) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/auth/login/login'
            })
          } else {
            wx.navigateBack()
          }
        }
      })
      return false
    }
    return true
  },

  /**
   * 刷新数据
   */
  refreshData() {
    this.setData({
      currentPage: 1,
      petList: [],
      hasMore: true,
      isEmpty: false
    })
    this.loadPetList()
  },

  /**
   * 加载更多数据
   */
  loadMoreData() {
    if (!this.data.hasMore || this.data.loading) return
    
    this.setData({
      currentPage: this.data.currentPage + 1
    })
    this.loadPetList()
  },

  /**
   * 加载宠物列表
   */
  async loadPetList() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const params = {
        page: this.data.currentPage,
        pageSize: this.data.pageSize
      }

      const result = await shopService.getPetList(params.page, params.pageSize)
      
      if (result.code === 200) {
        const newPetList = result.data || []
        
        // 处理图片路径
        const processedList = newPetList.map(pet => ({
          ...pet,
          photo: this.processImageUrl(pet.photo)
        }))

        this.setData({
          petList: this.data.currentPage === 1 ? processedList : [...this.data.petList, ...processedList],
          total: result.total || 0,
          hasMore: processedList.length === this.data.pageSize,
          isEmpty: this.data.currentPage === 1 && processedList.length === 0
        })
      } else {
        wx.showToast({
          title: result.message || '获取数据失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载宠物列表失败:', error)
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
      // 停止下拉刷新
      if (this.data.currentPage === 1) {
        wx.stopPullDownRefresh()
      }
    }
  },

  /**
   * 处理图片URL
   * 如果是相对路径则拼接 D:/images/，否则直接使用
   */
  processImageUrl(photo) {
    if (!photo) return ''
    
    // 如果已经是完整的URL（以http开头），直接返回
    if (photo.startsWith('http')) {
      return photo
    }
    
    // 如果是相对路径，拼接基础路径
    return `D:/images/${photo}`
  },

  /**
   * 点击宠物卡片
   */
  onPetCardTap(e) {
    const petId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/shop/pet-detail/pet-detail?id=${petId}`
    })
  },

  /**
   * 跳转到添加宠物页面
   */
  onAddPetTap() {
    wx.navigateTo({
      url: '/pages/shop/pet-manage/pet-manage'
    })
  },

  /**
   * 重试加载
   */
  onRetryTap() {
    this.refreshData()
  }
})