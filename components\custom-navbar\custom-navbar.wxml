<!-- components/custom-navbar/custom-navbar.wxml -->
<view 
  class="custom-navbar {{fixed ? 'fixed' : ''}}" 
  style="background-color: {{backgroundColor}}; padding-top: {{statusBarHeight}}px; height: {{navBarHeight}}px;"
>
  <view class="navbar-content" style="height: {{navBarHeight}}px;">
    <!-- 返回按钮 -->
    <view 
      wx:if="{{showBack}}" 
      class="navbar-back" 
      bindtap="navigateBack"
      style="top: {{(statusBarHeight + navBarHeight - 32) / 2}}px;"
    >
      <image 
        class="back-icon" 
        src="/assets/images/{{capsuleColor === 'white' ? 'back-white.png' : 'back-black.png'}}" 
        mode="aspectFit"
      ></image>
    </view>
    
    <!-- 标题 -->
    <view 
      class="navbar-title" 
      style="color: {{titleColor}}; top: {{(statusBarHeight + navBarHeight - 32) / 2}}px;"
    >
      {{title}}
    </view>
  </view>
</view>

<!-- 占位元素，防止内容被导航栏遮挡 -->
<view 
  wx:if="{{fixed}}" 
  style="height: {{statusBarHeight + navBarHeight}}px;"
></view>