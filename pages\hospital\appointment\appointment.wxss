/* pages/hospital/appointment/appointment.wxss */

.appointment-container {
  min-height: 100vh;
  background: #f8f9fa;
}

/* ==================== 加载屏幕 ==================== */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  border-top: 6rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  opacity: 0.9;
}

/* ==================== 医院信息卡片 ==================== */
.hospital-info-card {
  position: relative;
  margin-bottom: 30rpx;
  overflow: hidden;
}

.hospital-bg {
  position: relative;
  height: 300rpx;
  overflow: hidden;
}

.hospital-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hospital-bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
}

.hospital-details {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 30rpx;
  border-radius: 30rpx 30rpx 0 0;
}

.hospital-main-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.hospital-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
  margin-right: 20rpx;
  border: 4rpx solid white;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.hospital-text {
  flex: 1;
}

.hospital-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
}

.hospital-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rating-star {
  width: 28rpx;
  height: 28rpx;
}

.rating-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.rating-label {
  font-size: 24rpx;
  color: #7f8c8d;
}

.hospital-meta-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.meta-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

.meta-text {
  font-size: 26rpx;
  color: #7f8c8d;
  flex: 1;
}

/* ==================== 表单容器 ==================== */
.form-container {
  padding: 0 30rpx 30rpx;
}

.form-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  flex: 1;
}

.section-line {
  flex: 1;
  height: 2rpx;
  background: linear-gradient(90deg, #667eea, transparent);
  margin-left: 20rpx;
}

/* ==================== 表单元素 ==================== */
.form-group {
  margin-bottom: 32rpx;
  position: relative;
}

.form-row {
  display: flex;
  gap: 20rpx;
}

.form-group-half {
  flex: 1;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #2c3e50;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.required {
  color: #e74c3c;
  margin-left: 4rpx;
}

/* 输入框样式 */
.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  height: 90rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #2c3e50;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  background: white;
  border-color: #667eea;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.form-input.error {
  border-color: #e74c3c;
  background: #fdf2f2;
}

.input-focus-line {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
  border-radius: 0 0 12rpx 12rpx;
}

.form-input:focus + .input-focus-line {
  transform: scaleX(1);
}

/* 选择器样式 */
.form-picker {
  height: 90rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
}

.form-picker:active {
  background: white;
  border-color: #667eea;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.form-picker.error {
  border-color: #e74c3c;
  background: #fdf2f2;
}

.form-picker.selected {
  background: white;
  border-color: #667eea;
}

.picker-text {
  font-size: 28rpx;
  color: #2c3e50;
  flex: 1;
}

.form-picker:not(.selected) .picker-text {
  color: #95a5a6;
}

.picker-arrow {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
  transition: transform 0.3s ease;
}

.form-picker:active .picker-arrow {
  transform: rotate(180deg);
}

/* 文本域样式 */
.textarea-wrapper {
  position: relative;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #2c3e50;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.form-textarea:focus {
  background: white;
  border-color: #667eea;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.textarea-counter {
  position: absolute;
  bottom: 16rpx;
  right: 20rpx;
  font-size: 24rpx;
  color: #95a5a6;
  background: rgba(255, 255, 255, 0.8);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.error-message {
  display: block;
  font-size: 24rpx;
  color: #e74c3c;
  margin-top: 8rpx;
  margin-left: 4rpx;
}

/* ==================== 预约须知 ==================== */
.tips-section {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 2rpx solid #dee2e6;
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.tips-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.tips-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #2c3e50;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.tips-item {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  font-size: 26rpx;
  color: #495057;
  line-height: 1.5;
}

.tips-dot {
  width: 8rpx;
  height: 8rpx;
  background: #667eea;
  border-radius: 50%;
  margin-top: 14rpx;
  flex-shrink: 0;
}

/* ==================== 提交按钮 ==================== */
.submit-section {
  padding: 0 30rpx 60rpx;
}

.submit-button {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  border-radius: 20rpx;
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.submit-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.submit-button.loading {
  background: #95a5a6;
  box-shadow: none;
}

.submit-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.submit-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.submit-text {
  color: white;
}

/* ==================== 成功页面 ==================== */
.success-page {
  padding: 60rpx 30rpx;
  text-align: center;
}

.success-header {
  margin-bottom: 60rpx;
}

.success-animation {
  position: relative;
  display: inline-block;
  margin-bottom: 40rpx;
}

.success-circle {
  width: 200rpx;
  height: 200rpx;
  background: linear-gradient(135deg, #67c23a, #85ce61);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 12rpx 30rpx rgba(103, 194, 58, 0.3);
  animation: success-bounce 0.8s ease-out;
}

@keyframes success-bounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.success-icon {
  width: 80rpx;
  height: 80rpx;
  filter: brightness(0) invert(1);
}

.success-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background: #67c23a;
  border-radius: 50%;
  animation: particle-float 2s ease-out infinite;
}

.particle-1 {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.particle-2 {
  top: 30%;
  right: 20%;
  animation-delay: 0.5s;
}

.particle-3 {
  bottom: 30%;
  left: 15%;
  animation-delay: 1s;
}

.particle-4 {
  bottom: 20%;
  right: 15%;
  animation-delay: 1.5s;
}

@keyframes particle-float {
  0% {
    opacity: 0;
    transform: translateY(0) scale(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-20rpx) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-40rpx) scale(0);
  }
}

.success-title {
  display: block;
  font-size: 42rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 16rpx;
}

.success-subtitle {
  display: block;
  font-size: 28rpx;
  color: #7f8c8d;
}

/* 成功页详情 */
.success-details {
  margin-bottom: 60rpx;
}

.detail-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #7f8c8d;
}

.detail-value {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
  text-align: right;
}

.detail-value.highlight {
  color: #667eea;
  font-weight: bold;
}

/* 成功页按钮 */
.success-actions {
  display: flex;
  gap: 20rpx;
}

.action-button {
  flex: 1;
  height: 90rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  transition: all 0.3s ease;
}

.action-button.secondary {
  background: #f8f9fa;
  color: #2c3e50;
  border: 2rpx solid #e9ecef;
}

.action-button.secondary:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.action-button.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.action-button.primary:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
}

.action-button.primary .action-icon {
  filter: brightness(0) invert(1);
}

/* ==================== 选择器弹窗 ==================== */
.picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  animation: fade-in 0.3s ease;
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  width: 100%;
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  max-height: 80vh;
  animation: slide-up 0.3s ease;
}

@keyframes slide-up {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  opacity: 0.6;
}

/* 选项网格 */
.option-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.option-item {
  flex: 1;
  min-width: calc(50% - 8rpx);
  height: 80rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
}

.option-item:active {
  transform: scale(0.98);
}

.option-item.selected {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: #667eea;
}

.option-text {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
}

.option-item.selected .option-text {
  color: white;
}

.option-check {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  color: white;
  font-size: 20rpx;
  font-weight: bold;
}

/* 日期选择器 */
.date-picker {
  height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.date-placeholder {
  color: #95a5a6;
}

.date-value {
  color: #2c3e50;
  font-weight: bold;
}

/* 时间网格 */
.time-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.time-item {
  width: calc(25% - 12rpx);
  height: 70rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.time-item:active {
  transform: scale(0.98);
}

.time-item.selected {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: #667eea;
}

.time-text {
  font-size: 26rpx;
  color: #2c3e50;
  font-weight: 500;
}

.time-item.selected .time-text {
  color: white;
}