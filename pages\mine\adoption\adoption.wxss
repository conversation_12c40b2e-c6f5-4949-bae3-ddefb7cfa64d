/* 全局容器 */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
  box-sizing: border-box; /* 确保 padding 不影响宽度 */
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6F61;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
  text-align: center; /* 居中对齐，增强可读性 */
}

.empty-action {
  width: 100%;
  display: flex;
  justify-content: center;
}

.goto-adopt-btn {
  background: linear-gradient(135deg, #FF6F61 0%, #FF8A75 100%);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  text-decoration: none;
  box-shadow: 0 4rpx 12rpx rgba(255, 111, 97, 0.3);
  transition: transform 0.2s, box-shadow 0.2s; /* 添加平滑过渡 */
}

.goto-adopt-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 111, 97, 0.2);
}

.goto-adopt-btn:focus {
  outline: none;
  box-shadow: 0 0 0 4rpx rgba(255, 111, 97, 0.4); /* 提升可访问性 */
}

/* 领养记录列表 */
.adoption-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.adoption-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  transition: transform 0.2s; /* 添加轻微缩放效果 */
}

.adoption-item:active {
  transform: scale(0.98); /* 点击时轻微缩小，增强交互感 */
}

/* 状态标签 */
.status-tag {
  position: absolute;
  top: 0;
  right: 0;
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  color: white;
  border-radius: 0 16rpx 0 16rpx;
  font-weight: bold;
  text-align: center;
  min-width: 80rpx; /* 防止文字过短时标签变形 */
}

.status-pending {
  background-color: #FFA726;
}

.status-approved {
  background-color: #4CAF50;
}

.status-completed {
  background-color: #2196F3;
}

.status-rejected {
  background-color: #F44336;
}

/* 申请信息头部 */
.adoption-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-top: 10rpx;
}

.adoption-number {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis; /* 防止长编号溢出 */
  white-space: nowrap;
  max-width: 50%; /* 限制宽度，适应小屏幕 */
}

.apply-time {
  font-size: 24rpx;
  color: #999;
}

/* 动物信息 */
.animal-info {
  display: flex;
  gap: 20rpx;
  margin-bottom: 25rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.animal-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
  object-fit: cover; /* 确保图片填充但不失真 */
}

.animal-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.animal-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis; /* 防止长名称溢出 */
  white-space: nowrap;
}

.animal-meta {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.animal-type {
  font-size: 26rpx;
  color: #666;
}

.animal-age {
  font-size: 24rpx;
  color: #999;
}

.rescue-info {
  margin-top: 8rpx;
}

.rescue-name {
  font-size: 24rpx;
  color: #FF6F61;
  background-color: rgba(255, 111, 97, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  display: inline-block; /* 防止过长文字影响布局 */
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 申请信息 */
.application-info {
  margin-bottom: 25rpx;
}

.info-row {
  display: flex;
  margin-bottom: 12rpx;
  align-items: flex-start;
}

.info-row.reject {
  background-color: #ffebee;
  padding: 10rpx;
  border-radius: 8rpx;
  margin-top: 10rpx;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  min-width: 140rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  word-break: break-word;
  max-width: 100%; /* 防止溢出 */
}

.info-value.reason {
  line-height: 1.5;
}

.info-row.reject .info-value {
  color: #d32f2f;
}

/* 操作按钮 */
.adoption-actions {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  min-width: 140rpx;
  height: 60rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s, transform 0.2s; /* 平滑过渡 */
}

.action-btn:disabled {
  background-color: #ccc !important; /* 禁用状态样式 */
  color: #fff;
  cursor: not-allowed;
  opacity: 0.6;
}

.detail-btn {
  background-color: #2196F3;
  color: white;
}

.detail-btn:active:not(:disabled) {
  background-color: #1976D2;
}

.cancel-btn {
  background-color: #F44336;
  color: white;
}

.cancel-btn:active:not(:disabled) {
  background-color: #D32F2F;
}

.contact-btn {
  background-color: #4CAF50;
  color: white;
}

.contact-btn:active:not(:disabled) {
  background-color: #388E3C;
}

.action-btn:focus {
  outline: none;
  box-shadow: 0 0 0 4rpx rgba(0, 0, 0, 0.2); /* 提升可访问性 */
}

/* 刷新提示 */
.refresh-hint {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #FF6F61;
  color: white;
  text-align: center;
  padding: 20rpx;
  font-size: 28rpx;
  z-index: 1000;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .adoption-item {
    padding: 24rpx;
  }

  .info-label {
    min-width: 120rpx; /* 小屏幕减小标签宽度 */
  }

  .action-btn {
    min-width: 120rpx; /* 小屏幕减小按钮宽度 */
    font-size: 24rpx;
  }

  .animal-name {
    font-size: 28rpx; /* 小屏幕减小字体 */
  }

  .adoption-number {
    max-width: 40%; /* 小屏幕进一步限制编号宽度 */
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .status-pending { background-color: #FF8C00; } /* 更鲜明的橙色 */
  .status-approved { background-color: #2E7D32; } /* 更深的绿色 */
  .status-completed { background-color: #1565C0; } /* 更深的蓝色 */
  .status-rejected { background-color: #C62828; } /* 更深的红色 */
}

/* 大字体适配 */
@media (prefers-reduced-motion: reduce) {
  .action-btn, .goto-adopt-btn, .adoption-item {
    transition: none; /* 禁用动画，适配无障碍 */
  }

  @keyframes slideDown {
    from { opacity: 0; }
    to { opacity: 1; }
  }
}