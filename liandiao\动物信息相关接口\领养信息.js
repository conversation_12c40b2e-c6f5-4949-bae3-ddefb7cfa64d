import { getAdoptInfo } from '../../services/animalService';

const params = {
    // 查询参数
};

getAdoptInfo(params)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('领养信息:', res.data);
        } else {
            wx.showToast({
                title: res.message || '获取领养信息失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('获取领养信息出错', err);
        wx.showToast({
            title: '获取领养信息出错，请重试',
            icon: 'none'
        });
    });