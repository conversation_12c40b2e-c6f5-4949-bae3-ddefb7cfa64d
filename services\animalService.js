/**
 * 动物相关接口服务 - 使用本地Mock数据版本
 */
import request from './request';
import { CONFIG } from './config';

/**
 * 获取流浪动物信息（普通用户）--已检查，严格按照接口文档
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码 (必需, >= 1)
 * @param {number} params.pageSize - 每页条数 (必需)
 * @param {string} params.address - 地址 (必需)
 * @returns {Promise} 动物信息列表
 * 接口: POST /users/common/animalinfo
 * 参数: page, pageSize, address (Body), Authorization (Header)
 * 返回: { code, message, total, data: [{ID, medicalRecord, birthDate, gender, breed, source, status, photo}] }
 */
function getAnimalList(params = {}) {
  console.log('🚀 获取流浪动物信息请求:', params);

  // 获取用户信息和token
  const userInfo = wx.getStorageSync(CONFIG.STORAGE_KEYS.USER_INFO);
  const token = wx.getStorageSync(CONFIG.STORAGE_KEYS.TOKEN);

  // 设置默认参数，严格按照接口文档
  const defaultParams = {
    page: CONFIG.PAGE_CONFIG.DEFAULT_PAGE,
    pageSize: CONFIG.PAGE_CONFIG.DEFAULT_PAGE_SIZE,
    address: userInfo?.address || '',
    ...params
  };

  // 参数验证
  if (!defaultParams.address || defaultParams.address.trim() === '') {
    return Promise.reject(new Error('地址参数不能为空'));
  }

  if (defaultParams.page < 1) {
    return Promise.reject(new Error('页码必须大于等于1'));
  }

  // 🎯 临时方案：直接使用本地Mock数据
  if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
    console.log('🎯 使用本地Mock数据');
    const mockData = getMockAnimalDataForAPI(defaultParams);
    return Promise.resolve({
      code: CONFIG.ERROR_CODES.SUCCESS,
      message: '获取成功',
      total: mockData.total,
      data: mockData.list
    });
  }

  // 准备请求选项
  const requestOptions = {
    useMock: CONFIG.USE_MOCK,
    hideError: true,
    showLoading: true
  };

  // 如果有token，添加Authorization头部
  if (token) {
    requestOptions.headers = {
      'Authorization': token
    };
  }

  // 原有的远程请求逻辑保留，作为备用
  return request.post(CONFIG.API_PATHS.ANIMAL_INFO, defaultParams, requestOptions).then(result => {
    console.log('✅ 动物信息响应:', result);

    // 检查响应格式
    if (result && result.code === CONFIG.ERROR_CODES.SUCCESS) {
      return {
        code: result.code,
        message: result.message || '获取成功',
        total: result.total || 0,
        data: result.data || []
      };
    } else {
      throw new Error(result?.message || '获取动物信息失败');
    }
  }).catch(error => {
    console.error('🔥 动物信息接口调用失败:', error);
    // 返回模拟数据作为降级方案
    const mockData = getMockAnimalDataForAPI(defaultParams);
    return {
      code: CONFIG.ERROR_CODES.SUCCESS,
      message: '接口失败，使用本地数据',
      total: mockData.total,
      data: mockData.list
    };
  });
}

/**
 * 获取动物详情（普通用户）
 * @param {string} animalId - 动物ID
 * @returns {Promise} 动物详情信息
 */
function getAnimalDetail(animalId) {
  // 🎯 直接使用本地Mock数据
  if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
    console.log('🎯 使用本地Mock详情数据');
    const mockData = getMockAnimalData();
    const animal = mockData.find(item => item.id == animalId) || mockData[0];
    return Promise.resolve({
      code: CONFIG.ERROR_CODES.SUCCESS,
      data: animal,
      message: '使用本地Mock数据'
    });
  }

  return request.post(`/users/common/animalinfo/${animalId}`, {}, {
    useMock: CONFIG.USE_MOCK,
    hideError: true
  }).catch(error => {
    console.error('🔥 动物详情接口调用失败:', error);
    const mockData = getMockAnimalData();
    const animal = mockData.find(item => item.id == animalId) || mockData[0];
    return {
      code: CONFIG.ERROR_CODES.SUCCESS,
      data: animal,
      message: '接口失败，使用本地数据'
    };
  });
}

/**
 * 查询动物百科--已检查，严格按照接口文档
 * @param {string} variety - 动物品种 (必需)
 * @returns {Promise} 百科信息
 * 接口: GET /users/common/animalguide/{variety}
 * 参数: variety (路径参数), Authorization (Header)
 * 返回: { code: 200, message: string, data: [{species, dietInfo, livingHabits}] }
 */
function getAnimalGuide(variety) {
  console.log('🚀 查询动物百科请求:', variety);

  return new Promise((resolve, reject) => {
    try {
      // 参数验证
      if (!variety || variety.trim() === '') {
        reject(new Error('动物品种参数不能为空'));
        return;
      }

      // 获取token
      const token = wx.getStorageSync(CONFIG.STORAGE_KEYS.TOKEN);

      // 🎯 直接使用本地Mock数据
      if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
        console.log('🎯 使用本地Mock百科数据');
        const mockData = getMockGuideDataForAPI(variety);
        resolve({
          code: CONFIG.ERROR_CODES.SUCCESS,
          message: '查询成功',
          data: mockData
        });
        return;
      }

      // 构造URL
      const guideUrl = CONFIG.buildAnimalGuideUrl(variety);
      console.log('🔗 动物百科URL:', guideUrl);

      // 准备请求选项
      const requestOptions = {
        useMock: CONFIG.USE_MOCK,
        hideError: true,
        showLoading: true
      };

      // 如果有token，添加Authorization头部
      if (token) {
        requestOptions.headers = {
          'Authorization': token
        };
      }

      request.get(guideUrl, {}, requestOptions).then(result => {
        console.log('✅ 动物百科响应:', result);

        // 使用配置的成功状态码
        if (result && result.code === CONFIG.ERROR_CODES.SUCCESS) {
          console.log('🎉 查询动物百科成功');

          resolve({
            code: CONFIG.ERROR_CODES.SUCCESS,
            message: result.message || '查询成功',
            data: result.data || []
          });
        } else {
          // API返回了错误状态
          const errorMsg = result?.message || '查询动物百科失败，请稍后重试';
          reject(new Error(errorMsg));
        }
      }).catch(error => {
        console.error('🔥 动物百科接口调用失败:', error);
        // 返回模拟数据作为降级方案
        const mockData = getMockGuideDataForAPI(variety);
        resolve({
          code: CONFIG.ERROR_CODES.SUCCESS,
          message: '接口失败，使用本地数据',
          data: mockData
        });
      });

    } catch (err) {
      console.error('❌ 查询动物百科参数准备失败:', err);
      reject(new Error('查询动物百科失败，请检查参数'));
    }
  });
}

/**
 * 获取动物信息列表（救助站）
 * @param {Object} params - 查询参数
 * @returns {Promise} 动物信息列表
 */
function getAnimalListForRescue(params = {}) {
  // 🎯 救助站接口也使用本地数据
  if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
    console.log('🎯 救助站使用本地Mock数据');
    return Promise.resolve({
      code: CONFIG.ERROR_CODES.SUCCESS,
      data: getMockAnimalData(),
      message: '使用本地Mock数据'
    });
  }

  return request.get('/users/rescuestation/view/animal', params, { useMock: CONFIG.USE_MOCK });
}

/**
 * 修改动物信息（救助站）
 * @param {Object} data - 动物信息
 * @returns {Promise} 修改结果
 */
function updateAnimal(data) {
  return request.put('/users/rescuestation/animals/update', data, { useMock: CONFIG.USE_MOCK });
}

/**
 * 上传流浪动物信息（救助站）
 * @param {Object} data - 动物信息
 * @returns {Promise} 上传结果
 */
function uploadAnimalInfo(data) {
  return request.post('/users/rescuestation/upload/animalinfor', data, { useMock: CONFIG.USE_MOCK });
}

/**
 * 上传动物照片
 * @param {string} filePath - 文件路径
 * @param {string} animalId - 动物ID
 * @returns {Promise} 上传结果
 */
function uploadAnimalPhoto(filePath, animalId) {
  return request.uploadFile(
    `/animals/photo/upload/${animalId}`,
    filePath,
    'photo',
    {},
    CONFIG.USE_MOCK
  );
}

/**
 * 获取符合接口文档的模拟动物数据 - 分页版本
 * @param {Object} params - 查询参数
 * @returns {Object} 分页的模拟动物数据
 */
function getMockAnimalDataForAPI(params = {}) {
  const { page = 1, pageSize = 10, address = '' } = params;

  // 完整的Mock数据列表，严格按照接口文档格式
  const allAnimals = [
    {
      ID: 1,
      medicalRecord: '无过往病史，已完成基础疫苗接种',
      birthDate: '2022-03-15',
      gender: '雄',
      breed: '萨摩耶',
      source: '朝阳公园救助',
      status: '未领养',
      photo: '/api/images/animals/photo1.jpg'
    },
    {
      ID: 2,
      medicalRecord: '健康状况良好，已绝育',
      birthDate: '2023-01-20',
      gender: '雌',
      breed: '英国短毛猫',
      source: '主人送养',
      status: '未领养',
      photo: '/api/images/animals/photo2.jpg'
    },
    {
      ID: 3,
      medicalRecord: '轻微皮肤病，已治愈',
      birthDate: '2021-08-10',
      gender: '雄',
      breed: '金毛寻回犬',
      source: '流浪救助',
      status: '未领养',
      photo: '/api/images/animals/photo3.jpg'
    },
    {
      ID: 4,
      medicalRecord: '健康，疫苗接种完整',
      birthDate: '2023-06-01',
      gender: '雄',
      breed: '中华田园猫',
      source: '社区救助',
      status: '未领养',
      photo: '/api/images/animals/photo4.jpg'
    },
    {
      ID: 5,
      medicalRecord: '精力充沛，需要大量运动',
      birthDate: '2022-11-05',
      gender: '雌',
      breed: '边境牧羊犬',
      source: '牧场转让',
      status: '未领养',
      photo: '/api/images/animals/photo5.jpg'
    },
    {
      ID: 6,
      medicalRecord: '温顺，适合家庭饲养',
      birthDate: '2020-04-12',
      gender: '雄',
      breed: '橘猫',
      source: '志愿者救助',
      status: '未领养',
      photo: '/api/images/animals/photo6.jpg'
    },
    {
      ID: 7,
      medicalRecord: '小巧可爱，健康状况良好',
      birthDate: '2023-02-28',
      gender: '雌',
      breed: '博美犬',
      source: '宠物店遗弃',
      status: '未领养',
      photo: '/api/images/animals/photo7.jpg'
    },
    {
      ID: 8,
      medicalRecord: '活泼好动，已完成体检',
      birthDate: '2021-12-03',
      gender: '雄',
      breed: '美国短毛猫',
      source: '小区救助',
      status: '未领养',
      photo: '/api/images/animals/photo8.jpg'
    },
    {
      ID: 9,
      medicalRecord: '性格稳定，训练有素',
      birthDate: '2022-07-18',
      gender: '雌',
      breed: '拉布拉多',
      source: '导盲犬训练中心',
      status: '未领养',
      photo: '/api/images/animals/photo9.jpg'
    },
    {
      ID: 10,
      medicalRecord: '年幼活泼，需要耐心照料',
      birthDate: '2023-09-10',
      gender: '雄',
      breed: '柯基犬',
      source: '繁殖场救助',
      status: '未领养',
      photo: '/api/images/animals/photo10.jpg'
    }
  ];

  // 根据地址过滤（简单模拟）
  let filteredAnimals = allAnimals;
  if (address && address.trim() !== '') {
    // 简单的地址匹配逻辑，实际应该更复杂
    filteredAnimals = allAnimals.filter(animal =>
      animal.source.includes(address.substring(0, 2)) ||
      Math.random() > 0.3 // 随机显示一些动物
    );
  }

  // 分页处理
  const total = filteredAnimals.length;
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const list = filteredAnimals.slice(startIndex, endIndex);

  return {
    total,
    list,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize)
  };
}

/**
 * 获取模拟动物数据 - 增强版（保持向后兼容）
 * @returns {Array} 模拟动物数据列表
 */
function getMockAnimalData() {
  return [
    {
      id: 1,
      name: '小白',
      type: '狗狗',
      breed: '萨摩耶',
      age: '2岁',
      gender: '公',
      image: '/assets/images/default-pet.png',
      status: '待领养',
      description: '性格温顺，喜欢和人玩耍，已完成疫苗接种。这只萨摩耶非常亲人，适合有爱心的家庭领养。',
      location: '北京市朝阳区',
      contact: '救助站联系电话：010-12345678',
      healthStatus: '健康',
      weight: '25kg',
      isNeutered: true,
      rescueTime: '2024-01-15',
      vaccinated: true,
      rescueStory: '在朝阳公园附近被发现，当时身体虚弱，经过救助站精心照料已完全康复。'
    },
    {
      id: 2,
      name: '咪咪',
      type: '猫咪',
      breed: '英国短毛猫',
      age: '1岁',
      gender: '母',
      image: '/assets/images/default-pet.png',
      status: '待领养',
      description: '安静可爱，适合家庭饲养，已绝育。性格温顺，不挑食，会使用猫砂。',
      location: '上海市浦东新区',
      contact: '救助站联系电话：021-87654321',
      healthStatus: '健康',
      weight: '4kg',
      isNeutered: true,
      rescueTime: '2024-02-01',
      vaccinated: true,
      rescueStory: '原主人因工作调动无法继续饲养，希望为她找到新的温暖家庭。'
    },
    {
      id: 3,
      name: '旺财',
      type: '狗狗',
      breed: '金毛寻回犬',
      age: '3岁',
      gender: '公',
      image: '/assets/images/default-pet.png',
      status: '待领养',
      description: '聪明活泼，训练有素，很适合有孩子的家庭。会基本指令，性格稳定。',
      location: '广州市天河区',
      contact: '救助站联系电话：020-98765432',
      healthStatus: '健康',
      weight: '30kg',
      isNeutered: true,
      rescueTime: '2024-01-20',
      vaccinated: true,
      rescueStory: '曾经是导盲犬训练候选，因性格太活泼不适合，但非常适合家庭饲养。'
    },
    {
      id: 4,
      name: '小黑',
      type: '猫咪',
      breed: '中华田园猫',
      age: '6个月',
      gender: '公',
      image: '/assets/images/default-pet.png',
      status: '待领养',
      description: '年幼可爱，亲人友善，正在学习使用猫砂。活泼好动，喜欢玩耍。',
      location: '深圳市南山区',
      contact: '救助站联系电话：0755-56789012',
      healthStatus: '健康',
      weight: '2.5kg',
      isNeutered: false,
      rescueTime: '2024-03-01',
      vaccinated: true,
      rescueStory: '在科技园附近被好心人发现，当时还很小，现在已经健康成长。'
    },
    {
      id: 5,
      name: '花花',
      type: '狗狗',
      breed: '边境牧羊犬',
      age: '2岁',
      gender: '母',
      image: '/assets/images/default-pet.png',
      status: '待领养',
      description: '聪明伶俐，精力充沛，需要有经验的主人。学习能力强，需要足够运动。',
      location: '成都市武侯区',
      contact: '救助站联系电话：028-87654321',
      healthStatus: '健康',
      weight: '18kg',
      isNeutered: true,
      rescueTime: '2024-02-15',
      vaccinated: true,
      rescueStory: '原是牧场工作犬，因牧场关闭需要重新寻找家庭，希望找到能给她足够运动的主人。'
    },
    {
      id: 6,
      name: '橘子',
      type: '猫咪',
      breed: '橘猫',
      age: '4岁',
      gender: '公',
      image: '/assets/images/default-pet.png',
      status: '待领养',
      description: '温顺胖乎乎，食量较大，喜欢晒太阳。性格慵懒，适合安静的家庭。',
      location: '杭州市西湖区',
      contact: '救助站联系电话：0571-12345678',
      healthStatus: '健康',
      weight: '6kg',
      isNeutered: true,
      rescueTime: '2024-01-10',
      vaccinated: true,
      rescueStory: '曾经是流浪猫，被志愿者定期喂食，后来主动来到救助站，现在很享受室内生活。'
    },
    {
      id: 7,
      name: '小雪',
      type: '狗狗',
      breed: '博美犬',
      age: '1岁',
      gender: '母',
      image: '/assets/images/default-pet.png',
      status: '待领养',
      description: '小巧可爱，活泼机灵，适合公寓饲养。毛发蓬松，需要定期梳理。',
      location: '武汉市江汉区',
      contact: '救助站联系电话：027-12345678',
      healthStatus: '健康',
      weight: '3kg',
      isNeutered: true,
      rescueTime: '2024-02-20',
      vaccinated: true,
      rescueStory: '在宠物店被遗弃，现在健康状况良好，期待找到爱她的家庭。'
    },
    {
      id: 8,
      name: '大橙',
      type: '猫咪',
      breed: '美国短毛猫',
      age: '3岁',
      gender: '公',
      image: '/assets/images/default-pet.png',
      status: '待领养',
      description: '健壮活泼，喜欢攀爬，需要较大的活动空间。性格友善，容易相处。',
      location: '西安市雁塔区',
      contact: '救助站联系电话：029-87654321',
      healthStatus: '健康',
      weight: '5.5kg',
      isNeutered: true,
      rescueTime: '2024-01-25',
      vaccinated: true,
      rescueStory: '从小区流浪猫繁育，被志愿者救助，现在完全适应室内生活。'
    }
  ];
}

/**
 * 获取符合接口文档的模拟百科数据
 * @param {string} variety - 动物品种
 * @returns {Array} 模拟百科数据数组
 */
function getMockGuideDataForAPI(variety) {
  // 品种映射到大类
  const varietyToSpecies = {
    '金毛': '狗',
    '萨摩耶': '狗',
    '拉布拉多': '狗',
    '边境牧羊犬': '狗',
    '博美犬': '狗',
    '柯基犬': '狗',
    '英国短毛猫': '猫',
    '美国短毛猫': '猫',
    '中华田园猫': '猫',
    '橘猫': '猫',
    '波斯猫': '猫',
    '布偶猫': '猫'
  };

  const species = varietyToSpecies[variety] || '猫'; // 默认为猫

  // 严格按照接口文档格式返回数组
  const guides = {
    '猫': [
      {
        species: '猫',
        dietInfo: '猫是肉食动物，需要高蛋白饮食。成年猫每天需要喂食2-3次，幼猫需要更频繁的喂食。避免喂食巧克力、洋葱、葡萄等有毒食物。',
        livingHabits: '猫喜欢清洁，会自己清理毛发。大多数猫是夜行性动物，白天多数时间在睡觉。它们喜欢高处，有很强的平衡能力。'
      }
    ],
    '狗': [
      {
        species: '狗',
        dietInfo: '狗是杂食动物，需要均衡营养。成年犬每天喂食1-2次，幼犬需要3-4次。避免喂食巧克力、葡萄、洋葱等有害食物。',
        livingHabits: '狗是群居动物，喜欢社交和与人互动。它们需要足够的运动量和训练来保持身心健康。大多数狗都有领地意识。'
      }
    ],
    '兔子': [
      {
        species: '兔子',
        dietInfo: '兔子主要吃草和蔬菜，需要大量纤维。成年兔每天需要优质干草、少量颗粒饲料和新鲜蔬菜。',
        livingHabits: '兔子是黄昏和清晨活动的动物，白天多休息。它们喜欢挖掘和啃咬，需要提供适当的玩具。'
      }
    ]
  };

  return guides[species] || guides['猫']; // 默认返回猫的信息
}

/**
 * 获取模拟百科数据 - 增强版（保持向后兼容）
 * @param {string} species - 动物种类
 * @returns {Object} 模拟百科数据
 */
function getMockGuideData(species) {
  const guides = {
    '猫': {
      id: 1,
      species: '猫',
      basicInfo: '猫是人类最受欢迎的宠物之一，性格独立且富有个性。成年猫平均寿命12-18年，体重通常在3-7公斤之间。',
      diet: '猫是肉食动物，需要高蛋白饮食。成年猫每天需要喂食2-3次，幼猫需要更频繁的喂食。避免喂食巧克力、洋葱、葡萄等有毒食物。',
      habits: '猫喜欢清洁，会自己清理毛发。大多数猫是夜行性动物，白天多数时间在睡觉。它们喜欢高处，有很强的平衡能力。',
      care: '定期梳毛可以减少毛球问题，清洁耳朵预防感染，修剪指甲保护家具。保持环境清洁，定期更换猫砂。',
      health: '定期疫苗接种是必须的，包括狂犬病疫苗。注意观察猫咪的精神状态、食欲和排泄情况，发现异常及时就医。',
      training: '猫咪可以训练使用猫砂盆、回应名字、简单的握手等动作。使用积极强化的方法，避免惩罚。'
    },
    '狗': {
      id: 2,
      species: '狗',
      basicInfo: '狗是人类最忠诚的朋友，聪明且容易训练。不同品种的狗体型差异很大，寿命通常10-15年。',
      diet: '狗是杂食动物，需要均衡营养。成年犬每天喂食1-2次，幼犬需要3-4次。避免喂食巧克力、葡萄、洋葱等有害食物。',
      habits: '狗是群居动物，喜欢社交和与人互动。它们需要足够的运动量和训练来保持身心健康。大多数狗都有领地意识。',
      care: '定期洗澡保持清洁，梳毛去除死毛，清洁牙齿预防口腔疾病。保证充足的运动和游戏时间。',
      health: '定期体检和疫苗接种是必须的，注意体重控制防止肥胖。观察狗狗的行为变化，及时发现健康问题。',
      training: '狗狗可以训练各种技能，从基本的坐、卧、来到复杂的技巧。使用正面强化方法，保持耐心和一致性。'
    },
    '兔子': {
      id: 3,
      species: '兔子',
      basicInfo: '兔子是温顺的草食动物，寿命通常8-12年。它们有很强的繁殖能力，需要适当的绝育。',
      diet: '兔子主要吃草和蔬菜，需要大量纤维。成年兔每天需要优质干草、少量颗粒饲料和新鲜蔬菜。',
      habits: '兔子是黄昏和清晨活动的动物，白天多休息。它们喜欢挖掘和啃咬，需要提供适当的玩具。',
      care: '定期梳毛，特别是换毛期。保持环境清洁干燥，提供足够的活动空间。',
      health: '注意观察兔子的食欲和排便情况，定期检查牙齿健康。避免突然改变饮食。',
      training: '兔子可以训练使用厕所，回应简单指令。需要耐心和温和的方法。'
    }
  };

  return guides[species] || guides['猫']; // 默认返回猫的信息
}

export default {
  getAnimalList,
  getAnimalDetail,
  getAnimalGuide,
  getAnimalListForRescue,
  updateAnimal,
  uploadAnimalInfo,
  uploadAnimalPhoto
};