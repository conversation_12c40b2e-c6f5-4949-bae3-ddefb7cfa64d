<!--pages/encyclopedia/detail/detail.wxml-->
<view class="container">
  
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-section">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在加载中...</text>
  </view>

  <!-- 动物详情 -->
  <view wx:elif="{{animalInfo}}" class="detail-section">
    
    <!-- 头部信息 -->
    <view class="detail-header">
      <view class="breed-info">
        <text class="breed-name">{{animalInfo.breed}}</text>
        <text class="breed-subtitle">动物百科详情</text>
      </view>
      <view class="header-icon">🐾</view>
    </view>

    <!-- 详细信息卡片 -->
    <view class="info-cards">
      
      <!-- 饮食信息卡片 -->
      <view class="info-card">
        <view class="card-header">
          <view class="card-icon">🍖</view>
          <text class="card-title">饮食习性</text>
        </view>
        <view class="card-content">
          <text class="card-text">{{animalInfo.dietInfo}}</text>
        </view>
      </view>

      <!-- 生活习性卡片 -->
      <view class="info-card">
        <view class="card-header">
          <view class="card-icon">🏠</view>
          <text class="card-title">生活习性</text>
        </view>
        <view class="card-content">
          <text class="card-text">{{animalInfo.livingHabits}}</text>
        </view>
      </view>

    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn secondary" bindtap="onBackToSearch">
        <text class="btn-icon">↶</text>
        <text class="btn-text">返回搜索</text>
      </button>
      <button class="action-btn primary" bindtap="onSearchOther">
        <text class="btn-icon">🔍</text>
        <text class="btn-text">搜索其他</text>
      </button>
    </view>
  </view>

  <!-- 错误状态 -->
  <view wx:else class="error-section">
    <view class="error-icon">⚠️</view>
    <text class="error-title">加载失败</text>
    <text class="error-desc">{{errorMessage}}</text>
    
    <view class="error-buttons">
      <button class="retry-btn" bindtap="onRetryLoad">重新加载</button>
      <button class="back-btn" bindtap="onBackToSearch">返回搜索</button>
    </view>
  </view>

</view>