<!--pages/rescue/adoption-manage/adoption-manage.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="main-content">
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-info">
        <text class="page-title">领养申请管理</text>
        <text class="station-name">{{stationInfo.name}}</text>
        <text class="request-count">共 {{requestTotal}} 条申请</text>
      </view>
      
      <!-- 状态筛选 -->
      <view class="filter-section">
        <view class="filter-btn" bindtap="showStatusFilter">
          <text class="filter-text">{{statusFilter === 'all' ? '全部状态' : statusFilter}}</text>
          <text class="filter-arrow">▼</text>
        </view>
      </view>
    </view>

    <!-- 申请列表 -->
    <view class="requests-container">
      <!-- 加载状态 -->
      <view wx:if="{{requestsLoading && adoptionRequests.length === 0}}" class="requests-loading">
        <view class="loading-spinner"></view>
        <text>加载申请中...</text>
      </view>
      
      <!-- 空状态 -->
      <view wx:elif="{{adoptionRequests.length === 0}}" class="empty-requests">
        <image class="empty-icon" src="/assets/images/empty-adoption.png" mode="aspectFit" />
        <text class="empty-title">暂无领养申请</text>
        <text class="empty-hint">{{statusFilter === 'all' ? '还没有收到任何领养申请' : '当前状态下没有申请'}}</text>
      </view>
      
      <!-- 申请列表 -->
      <view wx:else class="requests-list">
        <view 
          wx:for="{{adoptionRequests}}" 
          wx:key="id" 
          class="request-card"
        >
          <!-- 申请卡片头部 -->
          <view class="card-header">
            <view class="applicant-info">
              <text class="applicant-name">{{item.userName}}</text>
              <text class="apply-time">{{item.createTime}}</text>
            </view>
            
            <view class="status-badge status-{{item.status}}">
              <text class="status-text">{{item.status}}</text>
            </view>
          </view>
          
          <!-- 动物信息 -->
          <view class="animal-info" data-animal-id="{{item.animalId}}" bindtap="viewAnimalDetail">
            <image 
              class="animal-photo" 
              src="{{item.animalPhoto}}" 
              mode="aspectFill"
              lazy-load="{{true}}"
            />
            <view class="animal-details">
              <text class="animal-name">{{item.animalName}}</text>
              <text class="animal-type">{{item.animalType}} · {{item.animalBreed}}</text>
              <text class="animal-hint">点击查看详情</text>
            </view>
          </view>
          
          <!-- 申请人详细信息 -->
          <view class="applicant-details">
            <view class="detail-row">
              <text class="detail-label">联系方式：</text>
              <text class="detail-value">{{item.userPhone}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">职业：</text>
              <text class="detail-value">{{item.job}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">住房条件：</text>
              <text class="detail-value">{{item.housing}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">健康状况：</text>
              <text class="detail-value">{{item.healthCondition}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">空闲时间：</text>
              <text class="detail-value">{{item.freeTime}}</text>
            </view>
          </view>
          
          <!-- 申请理由 -->
          <view wx:if="{{item.applyReason && item.applyReason !== '申请理由待获取'}}" class="apply-reason">
            <text class="reason-label">申请理由：</text>
            <text class="reason-content">{{item.applyReason}}</text>
          </view>
          
          <!-- 操作按钮 -->
          <view class="card-actions">
            <view 
              class="action-btn contact-btn"
              data-phone="{{item.userPhone}}"
              bindtap="makePhoneCall"
            >
              <text class="btn-icon">📞</text>
              <text class="btn-text">联系</text>
            </view>
            
            <view 
              class="action-btn status-btn"
              data-request="{{item}}"
              bindtap="showUpdateStatusModal"
            >
              <text class="btn-icon">📝</text>
              <text class="btn-text">修改状态</text>
            </view>
          </view>
          
          <!-- 更新时间 -->
          <view wx:if="{{item.updateTime !== item.createTime && item.updateTime !== '待获取'}}" class="update-info">
            <text class="update-text">最后更新：{{item.updateTime}}</text>
          </view>
        </view>
      </view>
      
      <!-- 加载更多提示 -->
      <view wx:if="{{adoptionRequests.length > 0 && requestHasMore}}" class="load-more-hint">
        <text wx:if="{{requestsLoading}}">正在加载更多...</text>
        <text wx:else>上拉加载更多</text>
      </view>
      
      <!-- 没有更多 -->
      <view wx:if="{{adoptionRequests.length > 0 && !requestHasMore}}" class="no-more-hint">
        <text>已显示全部申请</text>
      </view>
    </view>
  </view>

  <!-- 状态筛选弹窗 -->
  <view wx:if="{{showStatusPicker}}" class="picker-modal">
    <view class="modal-overlay" bindtap="hideStatusFilter"></view>
    <view class="picker-content">
      <view class="picker-header">
        <text class="picker-title">选择状态</text>
        <view class="picker-close" bindtap="hideStatusFilter">✕</view>
      </view>
      
      <view class="picker-list">
        <view 
          wx:for="{{statusOptions}}" 
          wx:key="value" 
          class="picker-item {{statusFilter === item.value ? 'selected' : ''}}"
          data-status="{{item.value}}"
          bindtap="selectStatusFilter"
        >
          <text class="picker-label">{{item.label}}</text>
          <view wx:if="{{statusFilter === item.value}}" class="selected-icon">✓</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 状态修改弹窗 -->
  <view wx:if="{{showStatusModal}}" class="status-modal">
    <view class="modal-overlay" bindtap="hideStatusModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">修改申请状态</text>
        <view class="modal-close" bindtap="hideStatusModal">✕</view>
      </view>
      
      <view class="modal-body">
        <!-- 申请信息 -->
        <view wx:if="{{selectedRequest}}" class="request-summary">
          <text class="summary-title">申请信息</text>
          <text class="summary-item">申请人：{{selectedRequest.userName}}</text>
          <text class="summary-item">动物：{{selectedRequest.animalName}}</text>
          <text class="summary-item">当前状态：{{selectedRequest.status}}</text>
        </view>
        
        <!-- 状态选择 -->
        <view class="status-selector">
          <text class="selector-title">选择新状态</text>
          <view class="status-options">
            <view 
              wx:for="{{statusUpdateOptions}}" 
              wx:key="*this" 
              class="status-option {{newStatus === item ? 'selected' : ''}}"
              data-status="{{item}}"
              bindtap="selectNewStatus"
            >
              <text class="option-text">{{item}}</text>
              <view wx:if="{{newStatus === item}}" class="option-check">✓</view>
            </view>
          </view>
        </view>
      </view>
      
      <view class="modal-actions">
        <view class="action-btn cancel-btn" bindtap="hideStatusModal">取消</view>
        <view 
          class="action-btn confirm-btn {{updatingStatus ? 'loading' : ''}} {{newStatus === selectedRequest.status || !newStatus ? 'disabled' : ''}}" 
          bindtap="confirmUpdateStatus"
        >
          <view wx:if="{{updatingStatus}}" class="update-loading">
            <view class="loading-spinner-small"></view>
            <text>修改中...</text>
          </view>
          <text wx:else>确认修改</text>
        </view>
      </view>
    </view>
  </view>
</view>