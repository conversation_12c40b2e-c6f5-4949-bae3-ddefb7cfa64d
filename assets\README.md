# 项目资源文件结构

请在项目中创建以下资源文件结构，并添加相应的图片资源：

```
assets/
├── images/
│   ├── logo.png                  # 应用logo
│   ├── banner1.jpg               # 轮播图1
│   ├── banner2.jpg               # 轮播图2
│   ├── banner3.jpg               # 轮播图3
│   ├── pet1.jpg                  # 宠物图片1
│   ├── pet2.jpg                  # 宠物图片2
│   ├── pet3.jpg                  # 宠物图片3
│   ├── eye-open.png              # 密码可见图标
│   ├── eye-close.png             # 密码隐藏图标
│   ├── arrow-down.png            # 下拉箭头图标
│   ├── check.png                 # 勾选图标
│   ├── icon-adopt.png            # 领养功能图标
│   ├── icon-shop.png             # 商店功能图标
│   ├── icon-hospital.png         # 医院功能图标
│   ├── icon-rescue.png           # 救助站功能图标
│   └── icon-encyclopedia.png     # 百科功能图标
│
└── tabbar/                       # 底部选项卡图标
    ├── home.png                  # 首页图标（未选中）
    ├── home-active.png           # 首页图标（选中）
    ├── adopt.png                 # 领养图标（未选中）
    ├── adopt-active.png          # 领养图标（选中）
    ├── shop.png                  # 商店图标（未选中）
    ├── shop-active.png           # 商店图标（选中）
    ├── hospital.png              # 医院图标（未选中）
    ├── hospital-active.png       # 医院图标（选中）
    ├── mine.png                  # 我的图标（未选中）
    └── mine-active.png           # 我的图标（选中）
```

你可以自己准备这些图片，或者从网上下载适合的图标和图片。图片的尺寸应该符合小程序的要求，建议使用以下尺寸：

- logo: 200x200像素
- 轮播图: 750x350像素
- 宠物图片: 300x300像素
- 功能图标: 80x80像素
- tabbar图标: 50x50像素

所有图片应该使用无背景的PNG格式或者JPEG格式。