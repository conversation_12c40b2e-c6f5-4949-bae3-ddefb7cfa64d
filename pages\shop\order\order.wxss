.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

/* 加载状态样式 */
.loading-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6F61;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-icon-small {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #FF6F61;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

/* 空状态样式 */
.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.retry-btn {
  width: 200rpx;
  height: 70rpx;
  background: #FF6F61;
  color: white;
  font-size: 28rpx;
  border-radius: 35rpx;
  border: none;
  line-height: 70rpx;
}

.retry-btn:active {
  background: #e55a4e;
}

/* 刷新提示 */
.refresh-tip {
  text-align: center;
  padding: 20rpx;
  font-size: 26rpx;
  color: #666;
  background: rgba(255, 111, 97, 0.1);
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

/* 订单列表样式 */
.order-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.order-item {
  background: white;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.order-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-id {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.status-container {
  display: flex;
  align-items: center;
}

.order-status {
  font-size: 26rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

/* 状态颜色样式 */
.status-pending {
  background: #fff3e0;
  color: #f57c00;
}

.status-paid {
  background: #e8f5e8;
  color: #4caf50;
}

.status-completed {
  background: #e3f2fd;
  color: #2196f3;
}

.status-cancelled {
  background: #ffebee;
  color: #f44336;
}

/* 订单内容 */
.order-content {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.order-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.order-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.pet-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.pet-type, .order-time, .order-address {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.order-price {
  font-size: 28rpx;
  color: #FF6F61;
  font-weight: bold;
  margin-top: 5rpx;
}

.order-address {
  font-size: 22rpx;
  color: #999;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 订单操作按钮 */
.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15rpx;
  margin-top: 15rpx;
  padding-top: 15rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  height: 60rpx;
  padding: 0 24rpx;
  font-size: 26rpx;
  border-radius: 30rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120rpx;
}

.cancel-btn {
  background: #fff;
  color: #666;
  border: 1rpx solid #ddd;
}

.cancel-btn:active {
  background: #f5f5f5;
}

.pay-btn {
  background: #FF6F61;
  color: white;
}

.pay-btn:active {
  background: #e55a4e;
}

.info-btn {
  background: #f0f0f0;
  color: #999;
}

.success-btn {
  background: #e8f5e8;
  color: #4caf50;
}

/* 新增：修改订单状态按钮样式 */
.update-btn {
  background: #4caf50;
  color: white;
}

.update-btn:active {
  background: #45a049;
}

/* 加载更多样式 */
.load-more {
  text-align: center;
  padding: 30rpx 0;
  margin-top: 20rpx;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #666;
}

.load-more-text {
  font-size: 26rpx;
  color: #999;
}

.no-more {
  text-align: center;
  padding: 30rpx 0;
  margin-top: 20rpx;
  color: #999;
  font-size: 24rpx;
}

.total-info {
  text-align: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 20rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .container {
    padding: 15rpx;
  }
  
  .order-item {
    padding: 15rpx;
  }
  
  .order-content {
    gap: 15rpx;
  }
  
  .order-image {
    width: 100rpx;
    height: 100rpx;
  }
  
  .pet-name {
    font-size: 30rpx;
  }
  
  .order-actions {
    gap: 10rpx;
  }
  
  .action-btn {
    min-width: 100rpx;
    font-size: 24rpx;
  }
}

/* 长按效果 */
.order-item {
  -webkit-tap-highlight-color: transparent;
}

/* 滚动优化 */
.order-list {
  scroll-behavior: smooth;
}

/* 图片加载失败样式 */
.order-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #999;
}