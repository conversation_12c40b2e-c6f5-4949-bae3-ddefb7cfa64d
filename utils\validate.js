/**
 * 表单验证工具函数
 */

/**
 * 验证手机号
 * @param {string} phone - 手机号
 * @returns {boolean} 是否为有效的手机号
 */
function isValidPhone(phone) {
  return /^1[3-9]\d{9}$/.test(phone);
}

/**
 * 验证邮箱
 * @param {string} email - 邮箱
 * @returns {boolean} 是否为有效的邮箱
 */
function isValidEmail(email) {
  return /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/.test(email);
}

/**
 * 验证账号（手机号或邮箱）
 * @param {string} account - 账号
 * @returns {boolean} 是否为有效的账号
 */
function validateAccount(account) {
  if (!account || account.trim() === '') {
    return false;
  }
  
  // 如果是手机号或邮箱，则认为是有效的账号
  return isValidPhone(account) || isValidEmail(account) || account.length >= 4;
}

/**
 * 验证密码
 * @param {string} password - 密码
 * @param {Object} options - 验证选项
 * @param {number} options.minLength - 最小长度，默认为 6
 * @param {number} options.maxLength - 最大长度，默认为 20
 * @param {boolean} options.requireLetter - 是否要求包含字母，默认为 true
 * @param {boolean} options.requireNumber - 是否要求包含数字，默认为 true
 * @param {boolean} options.requireSpecial - 是否要求包含特殊字符，默认为 false
 * @returns {boolean} 是否为有效的密码
 */
function isValidPassword(password, options = {}) {
  const {
    minLength = 6,
    maxLength = 20,
    requireLetter = true,
    requireNumber = true,
    requireSpecial = false
  } = options;
  
  // 检查长度
  if (password.length < minLength || password.length > maxLength) {
    return false;
  }
  
  // 检查是否包含字母
  if (requireLetter && !/[a-zA-Z]/.test(password)) {
    return false;
  }
  
  // 检查是否包含数字
  if (requireNumber && !/\d/.test(password)) {
    return false;
  }
  
  // 检查是否包含特殊字符
  if (requireSpecial && !/[~!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    return false;
  }
  
  return true;
}

/**
 * 验证密码（简化版）
 * @param {string} password - 密码
 * @returns {boolean} 是否为有效的密码
 */
function validatePassword(password) {
  if (!password || password.trim() === '') {
    return false;
  }
  
  // 简单验证，只检查长度
  return password.length >= 6 && password.length <= 20;
}

/**
 * 验证用户名
 * @param {string} username - 用户名
 * @param {Object} options - 验证选项
 * @param {number} options.minLength - 最小长度，默认为 2
 * @param {number} options.maxLength - 最大长度，默认为 20
 * @returns {boolean} 是否为有效的用户名
 */
function isValidUsername(username, options = {}) {
  const { minLength = 2, maxLength = 20 } = options;
  
  // 检查长度
  if (username.length < minLength || username.length > maxLength) {
    return false;
  }
  
  return true;
}

/**
 * 验证身份证号
 * @param {string} idCard - 身份证号
 * @returns {boolean} 是否为有效的身份证号
 */
function isValidIdCard(idCard) {
  return /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(idCard);
}

/**
 * 验证非空
 * @param {string} value - 待验证的值
 * @returns {boolean} 是否非空
 */
function isNotEmpty(value) {
  return value !== undefined && value !== null && value.trim() !== '';
}

/**
 * 验证数字
 * @param {string|number} value - 待验证的值
 * @returns {boolean} 是否为有效的数字
 */
function isValidNumber(value) {
  return !isNaN(Number(value));
}

/**
 * 验证整数
 * @param {string|number} value - 待验证的值
 * @returns {boolean} 是否为有效的整数
 */
function isValidInteger(value) {
  return /^-?\d+$/.test(value);
}

/**
 * 验证正整数
 * @param {string|number} value - 待验证的值
 * @returns {boolean} 是否为有效的正整数
 */
function isValidPositiveInteger(value) {
  return /^[1-9]\d*$/.test(value);
}

/**
 * 验证URL
 * @param {string} url - 待验证的URL
 * @returns {boolean} 是否为有效的URL
 */
function isValidUrl(url) {
  return /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w.-]*)*\/?$/.test(url);
}

/**
 * 验证日期格式
 * @param {string} date - 待验证的日期字符串，格式如 YYYY-MM-DD
 * @returns {boolean} 是否为有效的日期
 */
function isValidDate(date) {
  return /^\d{4}-\d{2}-\d{2}$/.test(date) && !isNaN(new Date(date).getTime());
}

/**
 * 验证时间格式
 * @param {string} time - 待验证的时间字符串，格式如 HH:MM:SS
 * @returns {boolean} 是否为有效的时间
 */
function isValidTime(time) {
  return /^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/.test(time);
}

/**
 * 验证日期时间格式
 * @param {string} dateTime - 待验证的日期时间字符串，格式如 YYYY-MM-DD HH:MM:SS
 * @returns {boolean} 是否为有效的日期时间
 */
function isValidDateTime(dateTime) {
  return /^\d{4}-\d{2}-\d{2} ([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/.test(dateTime) && !isNaN(new Date(dateTime.replace(' ', 'T')).getTime());
}

export default {
  isValidPhone,
  isValidEmail,
  isValidPassword,
  isValidUsername,
  isValidIdCard,
  isNotEmpty,
  isValidNumber,
  isValidInteger,
  isValidPositiveInteger,
  isValidUrl,
  isValidDate,
  isValidTime,
  isValidDateTime,
  validateAccount,
  validatePassword
};