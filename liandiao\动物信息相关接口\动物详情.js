import { getAnimalDetail } from '../../services/animalService';

const animalId = 1;

getAnimalDetail(animalId)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('动物详情:', res.data);
        } else {
            wx.showToast({
                title: res.message || '获取动物详情失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('获取动物详情出错', err);
        wx.showToast({
            title: '获取动物详情出错，请重试',
            icon: 'none'
        });
    });