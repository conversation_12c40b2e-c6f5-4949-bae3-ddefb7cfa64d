import { evaluateShop } from '../../services/shopService';

const evaluationData = {
    shopId: 1,
    rating: 5,
    content: '服务很好'
};

evaluateShop(evaluationData)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            wx.showToast({
                title: '评价成功',
                icon: 'success'
            });
        } else {
            wx.showToast({
                title: res.message || '评价失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('评价商店出错', err);
        wx.showToast({
            title: '评价商店出错，请重试',
            icon: 'none'
        });
    });