/**app.wxss**/

/* 全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
  font-size: 28rpx;
  color: #333;
  background-color: #f8f8f8;
  box-sizing: border-box;
  min-height: 200rpx;
  -webkit-font-smoothing: antialiased;
}

/* 容器样式 */
.container {
  min-height: 100vh;
  padding: 30rpx;
  box-sizing: border-box;
}

/* 页面内容区域，用于有自定义导航栏的页面 */
.content {
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 边框 */
.border-bottom {
  border-bottom: 1rpx solid #eee;
}

.border-top {
  border-top: 1rpx solid #eee;
}

/* 间距 */
.margin-top-sm {
  margin-top: 10rpx;
}

.margin-top {
  margin-top: 20rpx;
}

.margin-top-lg {
  margin-top: 30rpx;
}

.margin-top-xl {
  margin-top: 40rpx;
}

.margin-bottom-sm {
  margin-bottom: 10rpx;
}

.margin-bottom {
  margin-bottom: 20rpx;
}

.margin-bottom-lg {
  margin-bottom: 30rpx;
}

.margin-bottom-xl {
  margin-bottom: 40rpx;
}

.padding-top-sm {
  padding-top: 10rpx;
}

.padding-top {
  padding-top: 20rpx;
}

.padding-top-lg {
  padding-top: 30rpx;
}

.padding-top-xl {
  padding-top: 40rpx;
}

.padding-bottom-sm {
  padding-bottom: 10rpx;
}

.padding-bottom {
  padding-bottom: 20rpx;
}

.padding-bottom-lg {
  padding-bottom: 30rpx;
}

.padding-bottom-xl {
  padding-bottom: 40rpx;
}

/* 文本样式 */
.text-small {
  font-size: 24rpx;
}

.text-normal {
  font-size: 28rpx;
}

.text-large {
  font-size: 32rpx;
}

.text-xl {
  font-size: 36rpx;
}

.text-xxl {
  font-size: 40rpx;
}

.text-bold {
  font-weight: bold;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: #4eaaa8;
}

.text-secondary {
  color: #666;
}

.text-grey {
  color: #999;
}

.text-red {
  color: #f56c6c;
}

.text-green {
  color: #67c23a;
}

.text-blue {
  color: #409eff;
}

.text-orange {
  color: #e6a23c;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 布局 */
.flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.align-center {
  align-items: center;
}

.align-stretch {
  align-items: stretch;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
  font-size: 28rpx;
  line-height: 2.2;
  border-radius: 8rpx;
  background-color: #fff;
  color: #333;
  border: 1rpx solid #ddd;
}

.btn-primary {
  background-color: #4eaaa8;
  color: #fff;
  border: none;
}

.btn-secondary {
  background-color: #67c23a;
  color: #fff;
  border: none;
}

.btn-danger {
  background-color: #f56c6c;
  color: #fff;
  border: none;
}

.btn-warning {
  background-color: #e6a23c;
  color: #fff;
  border: none;
}

.btn-info {
  background-color: #409eff;
  color: #fff;
  border: none;
}

.btn-outline {
  background-color: transparent;
  color: #4eaaa8;
  border: 1rpx solid #4eaaa8;
}

.btn-block {
  width: 100%;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  height: 88rpx;
}

.btn-disabled {
  opacity: 0.6;
}

.btn-sm {
  font-size: 24rpx;
  padding: 0 20rpx;
  line-height: 1.8;
}

.btn-lg {
  font-size: 32rpx;
  padding: 0 40rpx;
  line-height: 2.6;
}

/* 图标 */
.icon {
  width: 32rpx;
  height: 32rpx;
}

.icon-sm {
  width: 24rpx;
  height: 24rpx;
}

.icon-lg {
  width: 40rpx;
  height: 40rpx;
}

.icon-xl {
  width: 48rpx;
  height: 48rpx;
}

/* 标签 */
.tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4rpx 16rpx;
  font-size: 24rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
}

.tag-primary {
  background-color: rgba(78, 170, 168, 0.1);
  color: #4eaaa8;
}

.tag-success {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.tag-warning {
  background-color: rgba(230, 162, 60, 0.1);
  color: #e6a23c;
}

.tag-danger {
  background-color: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
}

.tag-info {
  background-color: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

/* 分割线 */
.divider {
  height: 1rpx;
  width: 100%;
  background-color: #eee;
  margin: 20rpx 0;
}

/* 徽标 */
.badge {
  position: relative;
}

.badge-content {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 8rpx;
  font-size: 22rpx;
  line-height: 32rpx;
  text-align: center;
  color: #fff;
  background-color: #f56c6c;
  border-radius: 16rpx;
  z-index: 10;
}

/* 加载中 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
}

.loading-text {
  color: #999;
  font-size: 24rpx;
  margin-left: 10rpx;
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

/* 表单元素 */
.input {
  width: 100%;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 24rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333;
}

.textarea {
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx 24rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333;
}