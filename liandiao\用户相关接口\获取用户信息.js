import { getUserInfo } from '../../services/userService';

getUserInfo()
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('用户信息:', res.data);
        } else {
            wx.showToast({
                title: res.message || '获取用户信息失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('获取用户信息出错', err);
        wx.showToast({
            title: '获取用户信息出错，请重试',
            icon: 'none'
        });
    });