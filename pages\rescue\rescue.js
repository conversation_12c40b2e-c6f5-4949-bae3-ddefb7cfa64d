// pages/rescue/rescue.js
import rescueService from '../../services/rescueService';

Page({
  data: {
    rescueStations: [],
    loading: true,
    refreshing: false,
    
    // 分页参数
    page: 1,
    pageSize: 10,
    hasMore: true,
    
    // 搜索参数
    searchQuery: '',
    showSearch: false,
    currentAddress: '武汉市' // 默认地址
  },

  onLoad(options) {
    this.getCurrentLocation();
    this.loadRescueStations();
  },

  onShow() {
    this.refreshData();
  },

  
  /**
   * 获取用户当前位置
   */
  getCurrentLocation() {
    wx.getLocation({
      type: 'wgs84',
      success: (res) => {
        // 可以调用逆地理编码获取地址
        this.reverseGeocoding(res.latitude, res.longitude);
      },
      fail: () => {
        // 位置获取失败，使用默认城市
        this.setData({ currentAddress: '武汉市' });
      }
    });
  },

  /**
   * 逆地理编码（示例）
   */
  reverseGeocoding(lat, lng) {
    // 这里可以调用腾讯地图或其他地图API获取地址
    // 暂时使用默认地址
    this.setData({ currentAddress: '武汉市' });
  },

  /**
   * 加载救助站列表
   */

  async loadRescueStations(isRefresh = false) {
    if (isRefresh) {
      this.setData({
        page: 1,
        rescueStations: [],
        hasMore: true,
        refreshing: true
      });
    }

    this.setData({ loading: !isRefresh });

    try {
      // 使用 getRescueStationList 方法替代 searchStations
      const result = await rescueService.getRescueStationList({
        page: this.data.page,
        pageSize: this.data.pageSize,
        address: this.data.searchQuery || this.data.currentAddress
      });

      if (result && result.code === 200) {
        const stations = result.data.map(station => ({
          ...station,
          // 处理图片路径
          photo: station.photo.startsWith('http') ? 
                station.photo : 
                `${getApp().globalData.baseUrl}${station.photo}`
        }));

        const currentStations = isRefresh ? [] : this.data.rescueStations;
        const newStations = [...currentStations, ...stations];


        this.setData({
          rescueStations: newStations,
          page: this.data.page + 1,
          hasMore: stations.length >= this.data.pageSize,
          loading: false,
          refreshing: false
        });
      } else {
        throw new Error(result.message || '获取救助站列表失败');
      }
    } catch (error) {
      console.error('获取救助站列表失败:', error);
      this.setData({
        loading: false,
        refreshing: false
      });

      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },
  /**
   * 刷新数据
   */
  refreshData() {
    this.loadRescueStations(true);
  },
  /**
   * 跳转到救助站详情
   */
  goToDetail(e) {
    const { id } = e.currentTarget.dataset;
    
    if (!id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: `/pages/rescue/detail/detail?id=${id}`
    });
  },

  /**
   * 拨打电话
   */
  makePhoneCall(e) {
    const { phone } = e.currentTarget.dataset;
    
    if (!phone) {
      wx.showToast({
        title: '暂无联系电话',
        icon: 'none'
      });
      return;
    }

    wx.makePhoneCall({
      phoneNumber: phone,
      fail: () => {
        wx.showToast({
          title: '拨号失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 导航到救助站
   */
  navigateToStation(e) {
    const { name, address } = e.currentTarget.dataset;
    
    if (!address) {
      wx.showToast({
        title: '暂无地址信息',
        icon: 'none'
      });
      return;
    }

    wx.openLocation({
      name: name,
      address: address,
      fail: () => {
        wx.showToast({
          title: '定位失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 显示搜索框
   */
  showSearchInput() {
    this.setData({ showSearch: true });
  },

  /**
   * 隐藏搜索框
   */
  hideSearchInput() {
    this.setData({ 
      showSearch: false,
      searchQuery: ''
    });
    this.refreshData();
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    this.setData({ searchQuery: e.detail.value });
  },

  /**
   * 执行搜索 - 改进版本
   */
  performSearch() {
    const { searchQuery } = this.data;
    
    if (!searchQuery.trim()) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      });
      return;
    }

    // 重置分页并开始搜索
    this.setData({
      page: 1,
      rescueStations: [],
      hasMore: true
    });
    
    this.loadRescueStations(true);
  },

  /**
   * 查看我的评价
   */
  viewMyEvaluations() {
    wx.navigateTo({
      url: '/pages/mine/evaluation/evaluation?tab=2'
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadRescueStations();
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '宠物救助站列表',
      path: '/pages/rescue/rescue'
    };
  }
});