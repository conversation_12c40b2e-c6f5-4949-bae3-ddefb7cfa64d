import { updateAddress } from '../../services/userService';

const addressData = {
    newAddress: '新地址信息'
};

updateAddress(addressData)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            wx.showToast({
                title: '地址更新成功',
                icon: 'success'
            });
        } else {
            wx.showToast({
                title: res.message || '地址更新失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('地址更新出错', err);
        wx.showToast({
            title: '地址更新出错，请重试',
            icon: 'none'
        });
    });