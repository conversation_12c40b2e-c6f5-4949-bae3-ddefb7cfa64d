<!-- components/card/shop-card.wxml -->
<view class="shop-card {{mode}}" bindtap="onTapCard">
  <!-- 紧凑模式 -->
  <block wx:if="{{mode === 'compact'}}">
    <view class="shop-card-compact">
      <image 
        class="shop-image-compact" 
        src="{{shop.imageUrl || defaultImage}}" 
        mode="aspectFill"
        binderror="onImageError"
      ></image>
      <view class="shop-info-compact">
        <view class="shop-name-compact text-ellipsis">{{shop.name || '未命名'}}</view>
        <view class="shop-category-compact text-ellipsis text-secondary">{{shop.category || '宠物商店'}}</view>
      </view>
    </view>
  </block>

  <!-- 普通模式 -->
  <block wx:elif="{{mode === 'normal'}}">
    <view class="shop-card-normal">
      <image 
        class="shop-image-normal" 
        src="{{shop.imageUrl || defaultImage}}" 
        mode="aspectFill"
        binderror="onImageError"
      ></image>
      <view class="shop-info-normal">
        <view class="shop-name-normal text-ellipsis">{{shop.name || '未命名'}}</view>
        <view class="shop-rating-normal">
          <view class="rating-stars">
            <block wx:for="{{5}}" wx:key="index">
              <image 
                class="star-icon" 
                src="{{index < shop.rating ? '/assets/images/star-filled.png' : '/assets/images/star.png'}}" 
                mode="aspectFit"
              ></image>
            </block>
          </view>
          <view class="rating-value">{{shop.rating || '0.0'}}</view>
          <view class="rating-count text-secondary">({{shop.ratingCount || 0}})</view>
        </view>
        <view class="shop-address-normal text-ellipsis text-secondary">{{shop.address || '暂无地址'}}</view>
        <view class="shop-actions-normal">
          <view 
            class="shop-action-item" 
            catchtap="onTapPhone"
          >
            <image 
              class="action-icon" 
              src="/assets/images/phone.png" 
              mode="aspectFit"
            ></image>
            <text class="action-text">电话</text>
          </view>
          <view 
            class="shop-action-item" 
            catchtap="onTapNavigation"
          >
            <image 
              class="action-icon" 
              src="/assets/images/location.png" 
              mode="aspectFit"
            ></image>
            <text class="action-text">导航</text>
          </view>
          <view 
            class="shop-action-item" 
            catchtap="onTapFavorite"
          >
            <image 
              class="action-icon" 
              src="{{shop.isFavorite ? '/assets/images/heart-filled.png' : '/assets/images/heart.png'}}" 
              mode="aspectFit"
            ></image>
            <text class="action-text">收藏</text>
          </view>
        </view>
      </view>
    </view>
  </block>

  <!-- 详细模式 -->
  <block wx:else>
    <view class="shop-card-detailed">
      <image 
        class="shop-image-detailed" 
        src="{{shop.imageUrl || defaultImage}}" 
        mode="aspectFill"
        binderror="onImageError"
      ></image>
      <view class="shop-info-detailed">
        <view class="shop-header-detailed">
          <view class="shop-name-detailed">{{shop.name || '未命名'}}</view>
          <view class="shop-rating-detailed">
            <block wx:for="{{5}}" wx:key="index">
              <image 
                class="star-icon" 
                src="{{index < shop.rating ? '/assets/images/star-filled.png' : '/assets/images/star.png'}}" 
                mode="aspectFit"
              ></image>
            </block>
            <view class="rating-value">{{shop.rating || '0.0'}}</view>
          </view>
        </view>
        <view class="shop-meta-detailed">
          <view class="shop-meta-item">
            <image class="meta-icon" src="/assets/images/tag.png" mode="aspectFit"></image>
            <text class="meta-text">{{shop.category || '宠物商店'}}</text>
          </view>
          <view class="shop-meta-item">
            <image class="meta-icon" src="/assets/images/location-pin.png" mode="aspectFit"></image>
            <text class="meta-text">{{shop.address || '暂无地址'}}</text>
          </view>
          <view class="shop-meta-item">
            <image class="meta-icon" src="/assets/images/clock.png" mode="aspectFit"></image>
            <text class="meta-text">{{shop.hours || '暂无营业时间'}}</text>
          </view>
          <view class="shop-meta-item">
            <image class="meta-icon" src="/assets/images/phone-alt.png" mode="aspectFit"></image>
            <text class="meta-text">{{shop.phone || '暂无电话'}}</text>
          </view>
        </view>
        <view class="shop-description-detailed">
          {{shop.description || '暂无描述'}}
        </view>
        <view class="shop-actions-detailed">
          <view 
            class="shop-action-button" 
            catchtap="onTapPhone"
          >
            <image 
              class="action-icon" 
              src="/assets/images/phone.png" 
              mode="aspectFit"
            ></image>
            <text class="action-text">电话咨询</text>
          </view>
          <view 
            class="shop-action-button" 
            catchtap="onTapNavigation"
          >
            <image 
              class="action-icon" 
              src="/assets/images/location.png" 
              mode="aspectFit"
            ></image>
            <text class="action-text">查看位置</text>
          </view>
        </view>
      </view>
    </view>
  </block>
</view>