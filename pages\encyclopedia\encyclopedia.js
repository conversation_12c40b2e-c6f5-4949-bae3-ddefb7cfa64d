// pages/encyclopedia/encyclopedia.js
import animalService from '../../services/animalService'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    searchValue: '', // 搜索输入值
    animalList: [], // 动物百科列表
    loading: false, // 加载状态
    isEmpty: false, // 是否为空状态
    errorMessage: '' // 错误信息
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 如果有传入的品种参数，直接搜索
    if (options.variety) {
      this.setData({
        searchValue: decodeURIComponent(options.variety)
      })
      this.handleSearch()
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    if (this.data.searchValue) {
      this.handleSearch()
    }
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '动物百科查询',
      path: '/pages/encyclopedia/encyclopedia'
    }
  },

  /**
   * 搜索输入框值变化
   */
  onSearchInput(e) {
    this.setData({
      searchValue: e.detail.value
    })
  },

  /**
   * 搜索按钮点击
   */
  onSearchTap() {
    this.handleSearch()
  },

  /**
   * 清空搜索
   */
  onClearSearch() {
    this.setData({
      searchValue: '',
      animalList: [],
      isEmpty: false,
      errorMessage: ''
    })
  },

  /**
   * 执行搜索
   */
  async handleSearch() {
    const { searchValue } = this.data
    
    if (!searchValue.trim()) {
      wx.showToast({
        title: '请输入动物品种',
        icon: 'none'
      })
      return
    }

    this.setData({
      loading: true,
      isEmpty: false,
      errorMessage: '',
      animalList: []
    })

    try {
      const result = await animalService.getAnimalGuide(searchValue.trim())
      
      if (result && result.length > 0) {
        this.setData({
          animalList: result,
          isEmpty: false
        })
      } else {
        this.setData({
          animalList: [],
          isEmpty: true
        })
      }
    } catch (error) {
      console.error('查询动物百科失败:', error)
      this.setData({
        animalList: [],
        errorMessage: error.message || '查询失败，请重试'
      })
      
      wx.showToast({
        title: error.message || '查询失败',
        icon: 'none'
      })
    } finally {
      this.setData({
        loading: false
      })
    }
  },

  /**
   * 点击动物卡片，跳转到详情页
   */
  onAnimalTap(e) {
    const { index } = e.currentTarget.dataset
    const animal = this.data.animalList[index]
    
    if (animal) {
      wx.navigateTo({
        url: `/pages/encyclopedia/detail/detail?breed=${encodeURIComponent(animal.breed)}`
      })
    }
  },

  /**
   * 重新搜索
   */
  onRetrySearch() {
    this.handleSearch()
  },

  /**
   * 点击示例标签
   */
  onExampleTap(e) {
    const { variety } = e.currentTarget.dataset
    this.setData({
      searchValue: variety
    })
    this.handleSearch()
  }
})