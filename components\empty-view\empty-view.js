// components/empty-view/empty-view.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 显示的文本
    text: {
      type: String,
      value: '暂无数据'
    },
    // 图标地址
    image: {
      type: String,
      value: '/assets/images/empty.png'
    },
    // 是否显示按钮
    showButton: {
      type: <PERSON><PERSON>an,
      value: false
    },
    // 按钮文本
    buttonText: {
      type: String,
      value: '刷新'
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 点击按钮
    onButtonTap: function() {
      this.triggerEvent('action');
    }
  }
})