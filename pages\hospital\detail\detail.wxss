/* pages/hospital/detail/detail.wxss */
/* ==================== 页面标题 ==================== */
.page-header {
  background-color: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  text-align: center;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.hospital-id-info {
  font-size: 28rpx;
  color: #666;
}
.container {
  min-height: 100vh;
  background: #f8f9fa;
  position: relative;
  padding-bottom: 120rpx;
}

/* 选项卡计数 */
.tab-count {
  font-size: 22rpx;
  color: #999;
  margin-left: 8rpx;
}

/* ==================== 加载状态 ==================== */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* ==================== 医院头部 ==================== */
.hospital-header {
  background-color: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.hospital-image {
  width: 100%;
  height: 380rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.hospital-info {
  padding: 0 10rpx;
}

.hospital-name-rating {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.hospital-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.hospital-rating {
  display: flex;
  align-items: center;
}

.rating-value {
  font-size: 32rpx;
  color: #ff9800;
  margin-right: 10rpx;
}

.rating-star {
  width: 32rpx;
  height: 32rpx;
}

.hospital-type {
  display: inline-block;
  font-size: 24rpx;
  color: white;
  background-color: #667eea;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  margin-bottom: 16rpx;
}

.hospital-address {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.location-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
}

.hospital-address text {
  font-size: 28rpx;
  color: #666;
}

.hospital-business-hours {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.business-hours-label {
  color: #999;
}

.hospital-phone {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.phone-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
}

.hospital-phone text {
  font-size: 28rpx;
  color: #666;
}

.hospital-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* ==================== 选项卡 ==================== */
.tabs {
  display: flex;
  background-color: white;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 20rpx;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab.active {
  color: #667eea;
  font-weight: bold;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #667eea;
  border-radius: 3rpx;
}

/* ==================== 医生列表 ==================== */
.doctor-list {
  padding: 0 20rpx;
}

.doctor-card {
  display: flex;
  background-color: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.doctor-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.doctor-info {
  flex: 1;
}

.doctor-name-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.doctor-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.doctor-title {
  font-size: 24rpx;
  color: #667eea;
  background: #f0f3ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.doctor-specialty, .doctor-experience {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.doctor-introduction {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.doctor-schedule {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 12rpx;
}

.schedule-label {
  color: #999;
}

.schedule-value {
  color: #667eea;
}

.doctor-actions {
  display: flex;
  justify-content: flex-end;
}

.btn-reserve {
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  background-color: #667eea;
  color: white;
  border-radius: 20rpx;
  line-height: 1.5;
}

/* ==================== 医院信息 ==================== */
.hospital-info-detail {
  background-color: white;
  border-radius: 12rpx;
  margin: 0 20rpx 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.info-item {
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  display: block;
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

/* ==================== 评价相关 ==================== */
.evaluation-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 20rpx 20rpx;
}

.evaluation-title-section {
  display: flex;
  flex-direction: column;
}

.evaluation-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.evaluation-count {
  font-size: 24rpx;
  color: #666;
}

.btn-evaluate {
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  background-color: #667eea;
  color: white;
  border-radius: 20rpx;
  line-height: 1.5;
}

.evaluation-list {
  padding: 0 20rpx;
}

.evaluation-item {
  background-color: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.evaluation-user {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.evaluation-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rating-stars {
  display: flex;
}

.star-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 4rpx;
}

.rating-value {
  font-size: 24rpx;
  color: #666;
}

.evaluation-content {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  line-height: 1.5;
}

.evaluation-time {
  font-size: 24rpx;
  color: #999;
  text-align: right;
}

/* ==================== 空状态 ==================== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-image {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #ccc;
}

/* ==================== 加载更多 ==================== */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.loading-more text {
  font-size: 26rpx;
  color: #999;
  margin-left: 10rpx;
}

.no-more {
  text-align: center;
  padding: 30rpx 0;
}

.no-more text {
  font-size: 26rpx;
  color: #999;
}

/* ==================== 评价弹窗样式 ==================== */
.evaluation-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.evaluation-modal {
  background-color: white;
  border-radius: 20rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(50rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 弹窗头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

/* 弹窗内容 */
.modal-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

/* 医院信息卡片 */
.hospital-info-card {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}

.hospital-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.hospital-name-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 评分区域 */
.rating-section {
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.star-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.star-item {
  width: 60rpx;
  height: 60rpx;
  margin: 0 6rpx;
  transition: all 0.2s ease;
}

.star-item.active {
  transform: scale(1.1);
}

.rating-text {
  text-align: center;
  font-size: 26rpx;
  color: #666;
  display: block;
}

/* 评价内容区域 */
.content-section {
  margin-bottom: 20rpx;
}

.content-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  background-color: #fafafa;
  margin-bottom: 10rpx;
}

.content-textarea::placeholder {
  color: #999;
}

.content-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
}

/* 弹窗底部 */
.modal-footer {
  display: flex;
  padding: 20rpx 30rpx 30rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.btn-cancel {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 40rpx;
  background-color: white;
  color: #666;
  font-size: 28rpx;
  line-height: 80rpx;
}

.btn-submit {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-size: 28rpx;
  line-height: 80rpx;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.btn-submit[disabled] {
  background: #ccc;
  color: #999;
  box-shadow: none;
}