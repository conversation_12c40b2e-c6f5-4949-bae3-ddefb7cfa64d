import { uploadAnimalInfo } from '../../services/rescueService';

const animalData = {
    // 动物信息字段
};

uploadAnimalInfo(animalData)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            wx.showToast({
                title: '上传动物信息成功',
                icon: 'success'
            });
        } else {
            wx.showToast({
                title: res.message || '上传动物信息失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('上传动物信息出错', err);
        wx.showToast({
            title: '上传动物信息出错，请重试',
            icon: 'none'
        });
    });