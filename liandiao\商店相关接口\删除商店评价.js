import { deleteShopEvaluation } from '../../services/shopService';

const evaluationId = 1;

deleteShopEvaluation(evaluationId)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            wx.showToast({
                title: '删除评价成功',
                icon: 'success'
            });
        } else {
            wx.showToast({
                title: res.message || '删除评价失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('删除商店评价出错', err);
        wx.showToast({
            title: '删除商店评价出错，请重试',
            icon: 'none'
        });
    });