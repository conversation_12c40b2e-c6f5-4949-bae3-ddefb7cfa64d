// pages/mine/appointments/appointments.js
import hospitalService from '../../../services/hospitalService'
import userService from '../../../services/userService'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    appointmentList: [],    // 预约列表
    page: 1,               // 当前页码
    pageSize: 5,           // 每页数量
    total: 0,              // 总数
    loading: false,        // 加载状态
    hasMore: true          // 是否还有更多数据
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadAppointments()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时刷新数据
    this.refreshAppointments()
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshAppointments()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreAppointments()
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '我的医疗预约',
      path: '/pages/mine/appointments/appointments'
    }
  },

  /**
   * 刷新预约列表
   */
  refreshAppointments() {
    this.setData({
      page: 1,
      appointmentList: [],
      hasMore: true,
      total: 0
    })
    this.loadAppointments()
  },

  /**
   * 加载预约列表 - 修复：使用正确的方法名
   */
  async loadAppointments() {
    // 检查用户是否登录
    if (!userService || !userService.isLoggedIn()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/login'
        })
      }, 1500)
      return
    }

    this.setData({ loading: true })

    try {
      // 修复：使用正确的方法名 getMedicalReserveStatus
      const params = {
        page: this.data.page,
        pageSize: this.data.pageSize
      }
      const result = await hospitalService.getMedicalReserveStatus(params)
      
      // 统一使用200作为成功状态码，符合API文档要求
      if (result.code === 200) {
        // 处理图片URL - 使用data字段
        const processedData = (result.data || []).map(item => ({
          ...item,
          avatar: this.formatImageUrl(item.avatar)
        }))

        this.setData({
          appointmentList: processedData,
          total: result.total || processedData.length,
          hasMore: processedData.length >= this.data.pageSize,
          loading: false
        })
        
        // 停止下拉刷新
        wx.stopPullDownRefresh()
      } else {
        wx.showToast({
          title: result.message || '获取预约列表失败',
          icon: 'none'
        })
        this.setData({ loading: false })
      }
    } catch (error) {
      console.error('加载预约列表失败：', error)
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
      this.setData({ loading: false })
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 加载更多预约 - 修复：使用正确的方法名
   */
  async loadMoreAppointments() {
    if (this.data.loading || !this.data.hasMore) return

    this.setData({ loading: true })

    try {
      const nextPage = this.data.page + 1
      // 修复：使用正确的方法名 getMedicalReserveStatus
      const params = {
        page: nextPage,
        pageSize: this.data.pageSize
      }
      const result = await hospitalService.getMedicalReserveStatus(params)
      
      // 统一使用200作为成功状态码
      if (result.code === 200) {
        // 处理图片URL
        const processedData = (result.data || []).map(item => ({
          ...item,
          avatar: this.formatImageUrl(item.avatar)
        }))

        const newList = [...this.data.appointmentList, ...processedData]
        
        this.setData({
          appointmentList: newList,
          page: nextPage,
          total: result.total || newList.length,
          hasMore: processedData.length >= this.data.pageSize,
          loading: false
        })
      } else {
        wx.showToast({
          title: result.message || '加载更多失败',
          icon: 'none'
        })
        this.setData({ loading: false })
      }
    } catch (error) {
      console.error('加载更多失败：', error)
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
      this.setData({ loading: false })
    }
  },

  /**
   * 处理图片URL
   * @param {string} photo 原始图片路径
   * @returns {string} 处理后的图片URL
   */
  formatImageUrl(avatar) {
    if (!avatar) return '/assets/images/default-avatar.png' // 提供默认图片
    
    // 如果是完整URL（http或https开头），直接使用
    if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
      return avatar
    }
    
    // 否则拼接本地路径前缀（按照API文档D:/images/规则处理）
    return `D:/images/${avatar.startsWith('/') ? avatar.substring(1) : avatar}`
  },

  /**
   * 点击预约项
   */
  onAppointmentTap(e) {
    const appointment = e.currentTarget.dataset.appointment
    // 可以跳转到预约详情页面
    wx.navigateTo({
      url: `/pages/appointment/detail/detail?id=${appointment.id || appointment.ID}`
    })
  },

  /**
   * 取消预约 - 完全符合接口文档要求
   */
  onCancelAppointment(e) {
    const appointment = e.currentTarget.dataset.appointment
    
    // 修复：统一使用 id 字段（兼容 ID 字段）
    const appointmentId = appointment.id || appointment.ID
    
    // 验证ID为正整数（根据API文档要求）
    if (!appointmentId || !Number.isInteger(Number(appointmentId)) || Number(appointmentId) <= 0) {
      wx.showToast({
        title: '无效的预约ID',
        icon: 'none'
      });
      return;
    }

    // 确保用户已登录
    if (!userService || !userService.isLoggedIn()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }, 1500);
      return;
    }
    
    wx.showModal({
      title: '确认删除',
      content: `确定要取消"${appointment.username}"的预约吗？`,
      success: (res) => {
        if (res.confirm) {
          // 调用删除预约接口
          this.performCancelAppointment(appointmentId)
        }
      }
    })
  },

  /**
   * 执行取消预约 - 完全符合接口文档要求
   */
  async performUpdateAppointmentStatus(appointmentId, status = "已取消") {
    wx.showLoading({ 
      title: '修改中...',
      mask: true
    })
    
    try {
      // 调用修改预约状态接口
      const result = await hospitalService.updateHospitalReservation(appointmentId, status)
      
      wx.hideLoading()
      
      // 根据API文档，成功状态码是200
      if (result && result.code === 200) {
        wx.showToast({
          title: result.message || '修改成功',
          icon: 'success'
        })
        
        // 刷新列表
        this.refreshAppointments()
      } else {
        wx.showToast({
          title: result.message || '修改失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('修改预约状态失败:', error)
      
      let errorMessage = '修改失败，请重试';
      if (error.statusCode === 403) {
        errorMessage = '无权限修改此预约';
      } else if (error.statusCode === 404) {
        errorMessage = '预约不存在';
      } else if (error.code === 403) {
        errorMessage = error.message || '无权限修改此预约';
      } else if (error.code === 404) {
        errorMessage = error.message || '预约不存在';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      wx.showToast({
        title: errorMessage,
        icon: 'none'
      })
    }
  },

  /**
   * 跳转到医院列表页面
   */
  goToHospitalList() {
    wx.navigateTo({
      url: '/pages/hospital/list/list'
    })
  }
})