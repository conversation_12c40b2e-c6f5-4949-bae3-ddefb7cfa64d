/* pages/shop/list/list.wxss */

.pet-list-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.add-btn {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #ff6b6b, #ffa500);
  border-radius: 50rpx;
  color: #fff;
  font-size: 28rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.add-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
  font-weight: bold;
}

.add-text {
  font-size: 26rpx;
}

/* 宠物列表 */
.pet-list {
  padding: 20rpx 30rpx 0;
}

.pet-card {
  background-color: #fff;
  border-radius: 24rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
}

.pet-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
}

/* 宠物图片容器 */
.pet-image-container {
  position: relative;
  width: 100%;
  height: 400rpx;
  overflow: hidden;
}

.pet-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.stock-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: rgba(255, 59, 48, 0.9);
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  backdrop-filter: blur(10rpx);
}

.stock-text {
  font-weight: 500;
}

/* 宠物信息 */
.pet-info {
  padding: 30rpx;
}

.pet-breed {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.pet-details {
  margin-bottom: 24rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 16rpx;
  min-width: 80rpx;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

/* 性别样式 */
.gender-boy {
  color: #007aff;
}

.gender-girl {
  color: #ff3b30;
}

/* 库存样式 */
.stock-available {
  color: #34c759;
}

.stock-empty {
  color: #ff3b30;
}

/* 价格 */
.pet-price {
  display: flex;
  align-items: baseline;
  justify-content: flex-end;
}

.price-symbol {
  font-size: 24rpx;
  color: #ff6b6b;
  margin-right: 4rpx;
}

.price-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b6b;
}

/* 加载更多 */
.load-more {
  padding: 40rpx 30rpx;
  text-align: center;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.no-more {
  padding: 20rpx 0;
}

.no-more-text {
  font-size: 26rpx;
  color: #999;
}

.load-more-btn {
  background-color: #fff;
  border: 2rpx solid #ff6b6b;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  color: #ff6b6b;
  font-size: 28rpx;
  display: inline-block;
}

.load-more-btn:active {
  background-color: #ff6b6b;
  color: #fff;
}

.load-more-text {
  font-weight: 500;
}

/* 空状态 */
.empty-state, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
  min-height: 60vh;
}

.empty-icon, .error-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-title, .error-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc, .error-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 60rpx;
}

.empty-action, .error-action {
  width: 100%;
}

.retry-btn {
  background: linear-gradient(135deg, #ff6b6b, #ffa500);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 60rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 107, 0.3);
}

.retry-btn:active {
  transform: scale(0.95);
}

/* 首次加载状态 */
.first-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 60rpx;
  text-align: center;
}

.first-loading .loading-icon {
  width: 60rpx;
  height: 60rpx;
  border-width: 6rpx;
  margin-right: 0;
  margin-bottom: 30rpx;
}

.first-loading .loading-text {
  font-size: 30rpx;
  color: #666;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .pet-card {
    margin-bottom: 20rpx;
  }
  
  .pet-image-container {
    height: 350rpx;
  }
  
  .pet-info {
    padding: 24rpx;
  }
  
  .pet-breed {
    font-size: 30rpx;
  }
}