import { getAnimalInfoList } from '../../services/animalService';

const params = {
    // 查询参数
};

getAnimalInfoList(params)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('动物信息列表:', res.data);
        } else {
            wx.showToast({
                title: res.message || '获取动物信息列表失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('获取动物信息列表出错', err);
        wx.showToast({
            title: '获取动物信息列表出错，请重试',
            icon: 'none'
        });
    });