<!-- pages/login/login.wxml -->
<view class="login-container">
  <view class="header">
    <image class="logo" src="/assets/images/logo.png" mode="aspectFit"></image>
    <view class="title">宠物之家</view>
    <view class="subtitle">欢迎回来，请登录您的账号</view>
  </view>

  <view class="form-container">
    <!-- 账号 -->
    <view class="form-item">
      <view class="form-label">账号 <text class="required">*</text></view>
      <input 
        class="form-input {{errors.account ? 'input-error' : ''}}"
        placeholder="请输入手机号或邮箱"
        value="{{formData.account}}"
        data-field="account"
        bindinput="onInput"
        maxlength="50"
      />
      <view wx:if="{{errors.account}}" class="error-message">{{errors.account}}</view>
    </view>

    <!-- 密码 -->
    <view class="form-item">
      <view class="form-label">密码 <text class="required">*</text></view>
      <view class="password-input-container">
        <input 
          class="form-input {{errors.password ? 'input-error' : ''}}"
          placeholder="请输入密码"
          password="{{!showPassword}}"
          value="{{formData.password}}"
          data-field="password"
          bindinput="onInput"
          maxlength="20"
        />
        <view class="password-toggle" catchtap="togglePasswordVisibility">
          <image 
            class="eye-icon" 
            src="{{showPassword ? '/assets/images/eye-open.png' : '/assets/images/eye-close.png'}}"
            mode="aspectFit"
          ></image>
        </view>
      </view>
      <view wx:if="{{errors.password}}" class="error-message">{{errors.password}}</view>
    </view>

    <!-- 用户类型 -->
    <view class="form-item">
      <view class="form-label">用户类型 <text class="required">*</text></view>
      <picker 
        bindchange="onUserTypeChange" 
        value="{{userTypeIndex}}" 
        range="{{userTypes}}"
        range-key="name"
      >
        <view class="form-picker">
          {{userTypes[userTypeIndex].name}}
          <image class="picker-arrow" src="/assets/images/arrow-down.png" mode="aspectFit"></image>
        </view>
      </picker>
    </view>

    <!-- 地址 -->
    <view class="form-item">
      <view class="form-label">地址 <text class="required">*</text></view>
      <input 
        class="form-input {{errors.address ? 'input-error' : ''}}"
        placeholder="请输入所在城市或地区"
        value="{{formData.address}}"
        data-field="address"
        bindinput="onInput"
        maxlength="100"
      />
      <view wx:if="{{errors.address}}" class="error-message">{{errors.address}}</view>
    </view>

    <!-- 记住账号 -->
    <view class="form-options">
      <view class="remember-account" bindtap="toggleRememberAccount">
        <view class="checkbox {{rememberAccount ? 'checkbox-checked' : ''}}">
          <image 
            wx:if="{{rememberAccount}}"
            class="checkbox-icon" 
            src="/assets/images/check.png" 
            mode="aspectFit"
          ></image>
        </view>
        <text class="remember-text">记住账号</text>
      </view>
      
      <text class="forgot-password" bindtap="goToForgotPassword">忘记密码？</text>
    </view>

    <!-- 登录按钮 -->
    <button 
      class="login-btn {{isLoggingIn ? 'btn-disabled' : ''}}" 
      bindtap="submitForm"
      disabled="{{isLoggingIn}}"
      loading="{{isLoggingIn}}"
    >
      {{isLoggingIn ? '登录中...' : '登录'}}
    </button>

    <!-- 注册链接 -->
    <view class="register-link">
      还没有账号？<text class="link" bindtap="goToRegister">立即注册</text>
    </view>
  </view>
</view>