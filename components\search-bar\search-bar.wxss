/* components/search-bar/search-bar.wxss */

.search-bar {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background-color: #fff;
}

.search-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  height: 72rpx;
  padding: 0 20rpx;
}

.search-input-container.round {
  border-radius: 36rpx;
}

.search-input-container.square {
  border-radius: 8rpx;
}

.search-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  opacity: 0.5;
}

.search-input {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
  color: #333;
}

.search-placeholder {
  color: #999;
}

.search-clear {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  margin-left: 10rpx;
}

.clear-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.5;
}

.search-cancel {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #4eaaa8;
  white-space: nowrap;
}