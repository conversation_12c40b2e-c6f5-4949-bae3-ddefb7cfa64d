import { searchShops } from '../../services/searchService';

const params = {
    // 搜索参数
};

searchShops(params)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('商店搜索结果:', res.data);
        } else {
            wx.showToast({
                title: res.message || '商店搜索失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('商店搜索出错', err);
        wx.showToast({
            title: '商店搜索出错，请重试',
            icon: 'none'
        });
    });