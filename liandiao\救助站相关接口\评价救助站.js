import { evaluateRescueStation } from '../../services/rescueService';

const evaluationData = {
    stationId: 1,
    rating: 5,
    content: '服务很好'
};

evaluateRescueStation(evaluationData)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            wx.showToast({
                title: '评价成功',
                icon: 'success'
            });
        } else {
            wx.showToast({
                title: res.message || '评价失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('评价救助站出错', err);
        wx.showToast({
            title: '评价救助站出错，请重试',
            icon: 'none'
        });
    });