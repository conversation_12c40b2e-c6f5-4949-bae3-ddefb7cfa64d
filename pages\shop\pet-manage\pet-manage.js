// pages/shop/pet-manage/pet-manage.js
import shopService from '../../../services/shopService.js';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 页面模式：'add' 新增 | 'edit' 编辑
    mode: 'add',
    // 编辑时的宠物ID
    petId: null,
    // 表单数据
    formData: {
      breed: '',      // 品种
      gender: '',     // 性别：男/女
      age: '',        // 年龄（月）
      stock: '',      // 库存数量
      photo: '',      // 图片URL
      price: ''       // 价格
    },
    // 原始数据（用于编辑时对比）
    originalData: {},
    // 提交状态
    submitting: false,
    // 是否可以提交
    canSubmit: false,
    // 数据加载状态
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log('🐾 宠物管理页面加载', options);
    
    // 检查登录状态和权限
    if (!this.checkAuth()) {
      return;
    }

    // 判断页面模式
    if (options.id) {
      // 编辑模式
      this.setData({
        mode: 'edit',
        petId: parseInt(options.id)
      });
      
      // 加载宠物详情
      this.loadPetDetail();
    } else {
      // 新增模式
      this.setData({
        mode: 'add'
      });
    }
  },

  /**
   * 检查登录状态和权限
   */
  checkAuth: function() {
    const token = wx.getStorageSync('token');
    
    if (!token) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再操作',
        showCancel: false,
        success: () => {
          wx.reLaunch({
            url: '/pages/login/login'
          });
        }
      });
      return false;
    }

    console.log('✅ 用户已登录，权限验证通过');
    return true;
  },

  /**
   * 加载宠物详情（编辑模式）
   */
  async loadPetDetail() {
    this.setData({ loading: true });

    try {
      // 这里需要调用获取单个宠物详情的接口
      // 如果没有单独的接口，可以从列表页面传递数据或使用其他方式获取
      const result = await shopService.getPetDetail(this.data.petId);
      
      if (result && result.code === 200 && result.data) {
        const petData = result.data;
        
        this.setData({
          formData: {
            breed: petData.breed || '',
            gender: petData.gender || '',
            age: petData.age ? String(petData.age) : '',
            stock: petData.stock ? String(petData.stock) : '',
            photo: petData.photo || '',
            price: petData.price ? String(petData.price) : ''
          },
          originalData: { ...petData }
        });

        // 验证表单
        this.validateForm();
      } else {
        wx.showToast({
          title: '获取宠物信息失败',
          icon: 'none'
        });
        wx.navigateBack();
      }
    } catch (error) {
      console.error('❌ 加载宠物详情失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
      wx.navigateBack();
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 输入框内容变化
   */
  onInputChange: function(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    }, () => {
      // 输入后重新验证表单
      this.validateForm();
    });
  },

  /**
   * 选择性别
   */
  selectGender: function(e) {
    const { gender } = e.currentTarget.dataset;
    
    this.setData({
      'formData.gender': gender
    }, () => {
      this.validateForm();
    });
  },

  /**
   * 选择图片
   */
  chooseImage: function() {
    const that = this;
    
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        if (res.tempFiles && res.tempFiles.length > 0) {
          const tempFilePath = res.tempFiles[0].tempFilePath;
          
          // 这里可以选择直接使用临时路径，或者上传到服务器
          // 根据接口文档，支持文件上传或者URL
          that.setData({
            'formData.photo': tempFilePath
          }, () => {
            that.validateForm();
          });
          
          console.log('✅ 图片选择成功:', tempFilePath);
        }
      },
      fail: (err) => {
        console.error('❌ 图片选择失败:', err);
        wx.showToast({
          title: '图片选择失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 表单验证
   */
  validateForm: function() {
    const { formData, mode } = this.data;
    
    // 检查必填字段
    const requiredFields = ['breed', 'gender', 'age', 'stock', 'photo', 'price'];
    const isValid = requiredFields.every(field => {
      const value = formData[field];
      return value !== null && value !== undefined && String(value).trim() !== '';
    });

    // 额外验证
    let isFormatValid = true;
    
    if (formData.gender && !['男', '女'].includes(formData.gender)) {
      isFormatValid = false;
    }
    
    if (formData.age && (isNaN(formData.age) || Number(formData.age) < 0)) {
      isFormatValid = false;
    }
    
    if (formData.stock && (isNaN(formData.stock) || Number(formData.stock) < 0)) {
      isFormatValid = false;
    }
    
    if (formData.price && (isNaN(formData.price) || Number(formData.price) <= 0)) {
      isFormatValid = false;
    }

    // 编辑模式下，检查是否有数据变更
    let hasChanges = true;
    if (mode === 'edit') {
      const { originalData } = this.data;
      hasChanges = requiredFields.some(field => {
        const currentValue = String(formData[field] || '').trim();
        const originalValue = String(originalData[field] || '').trim();
        return currentValue !== originalValue;
      });
    }

    const canSubmit = isValid && isFormatValid && hasChanges;
    
    this.setData({
      canSubmit: canSubmit
    });

    console.log('📝 表单验证结果:', { isValid, isFormatValid, hasChanges, canSubmit });
  },

  /**
   * 提交表单
   */
  submitForm: function() {
    if (!this.data.canSubmit || this.data.submitting) {
      return;
    }

    // 再次验证表单
    this.validateForm();
    if (!this.data.canSubmit) {
      wx.showToast({
        title: '请完善表单信息',
        icon: 'none'
      });
      return;
    }

    const { formData, mode, petId } = this.data;
    
    // 构造提交数据，确保符合接口格式
    const submitData = {
      breed: String(formData.breed).trim(),
      gender: formData.gender,
      age: parseInt(formData.age),
      stock: parseInt(formData.stock),
      photo: formData.photo,
      price: parseFloat(Number(formData.price).toFixed(2))
    };

    // 编辑模式需要添加 id
    if (mode === 'edit') {
      submitData.id = petId;
    }

    console.log('📤 准备提交数据:', submitData);

    // 设置提交状态
    this.setData({
      submitting: true
    });

    // 根据模式调用不同的接口
    const apiCall = mode === 'edit' 
      ? shopService.updatePetInfo(submitData)
      : shopService.uploadPetInfo(submitData);

    apiCall
      .then(res => {
        console.log(`✅ 宠物信息${mode === 'edit' ? '修改' : '上传'}成功:`, res);
        
        this.setData({
          submitting: false
        });

        // 根据接口文档，成功返回格式为 { code: 200, message: '成功', data: { id: xxx } }
        if (res && res.code === 200) {
          // 操作成功
          const successMsg = mode === 'edit' ? '修改成功' : '上传成功';
          wx.showModal({
            title: successMsg,
            content: res.message || `宠物信息已成功${mode === 'edit' ? '修改' : '上传'}！`,
            showCancel: false,
            success: () => {
              if (mode === 'edit') {
                // 编辑模式返回上一页
                wx.navigateBack();
              } else {
                // 新增模式清空表单或返回上一页
                this.resetForm();
              }
            }
          });
        } else {
          // 接口返回失败
          const errorMsg = res.message || `${mode === 'edit' ? '修改' : '上传'}失败，请重试`;
          wx.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 2000
          });
        }
      })
      .catch(err => {
        console.error(`❌ 宠物信息${mode === 'edit' ? '修改' : '上传'}失败:`, err);
        
        this.setData({
          submitting: false
        });

        // 根据错误类型显示不同提示
        let errorMsg = '网络请求失败，请检查网络连接';
        
        if (err.statusCode === 400) {
          errorMsg = '参数无效，请检查输入信息';
        } else if (err.statusCode === 403) {
          errorMsg = '无权限操作，请确认登录状态';
        } else if (err.statusCode === 404) {
          errorMsg = mode === 'edit' ? '宠物记录不存在' : '接口不存在';
        } else if (err.statusCode === 500) {
          errorMsg = '服务器错误，请稍后重试';
        } else if (err.message) {
          errorMsg = err.message;
        }

        wx.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 2000
        });
      });
  },

  /**
   * 重置表单
   */
  resetForm: function() {
    this.setData({
      formData: {
        breed: '',
        gender: '',
        age: '',
        stock: '',
        photo: '',
        price: ''
      },
      canSubmit: false
    });
    
    console.log('🔄 表单已重置');
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {
    console.log('🐾 宠物管理页面渲染完成');
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    console.log('🐾 宠物管理页面显示');
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
    console.log('🐾 宠物管理页面隐藏');
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
    console.log('🐾 宠物管理页面卸载');
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {
    console.log('🔄 下拉刷新');
    
    // 如果是编辑模式，重新加载数据
    if (this.data.mode === 'edit') {
      this.loadPetDetail();
    }
    
    wx.stopPullDownRefresh();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {
    const { mode } = this.data;
    return {
      title: mode === 'edit' ? '编辑宠物信息' : '上传宠物信息',
      path: '/pages/shop/pet-manage/pet-manage'
    };
  }
});