<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else>
    <!-- 空状态 -->
    <view wx:if="{{adoptions.length === 0}}" class="empty-container">
      <image class="empty-image" src="/assets/images/empty.png" mode="aspectFit" />
      <text class="empty-text">暂无领养记录</text>
      <view class="empty-action">
        <navigator url="/pages/adopt/adopt" class="goto-adopt-btn">
          去看看待领养的宠物
        </navigator>
      </view>
    </view>

    <!-- 领养记录列表 -->
    <view wx:else class="adoption-list">
      <view wx:for="{{adoptions}}" wx:key="id" class="adoption-item">
        <!-- 状态标签 -->
        <view class="status-tag status-{{item.status}}">
          {{item.statusText}}
        </view>

        <!-- 申请信息头部 -->
        <view class="adoption-header">
          <view class="adoption-number">申请编号：{{item.adoptionNumber}}</view>
          <view class="apply-time">{{item.applyTime}}</view>
        </view>

        <!-- 动物信息 -->
        <view class="animal-info">
          <image class="animal-image" src="{{item.animalImage}}" mode="aspectFill" />
          <view class="animal-details">
            <view class="animal-name">{{item.animalName}}</view>
            <view class="animal-meta">
              <text class="animal-type">{{item.animalType}} · {{item.animalBreed}}</text>
              <text class="animal-age">{{item.animalAge}}</text>
            </view>
            <view class="rescue-info">
              <text class="rescue-name">{{item.rescueStationName}}</text>
            </view>
          </view>
        </view>

        <!-- 申请信息 -->
        <view class="application-info">
          <view class="info-row">
            <text class="info-label">住房情况：</text>
            <text class="info-value">{{item.userHousing}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">健康状况：</text>
            <text class="info-value">{{item.userHealth}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">空闲时间：</text>
            <text class="info-value">{{item.userFreeTime}}</text>
          </view>
          <view wx:if="{{item.adoptionReason}}" class="info-row">
            <text class="info-label">申请理由：</text>
            <text class="info-value reason">{{item.adoptionReason}}</text>
          </view>
          <view wx:if="{{item.rejectReason}}" class="info-row reject">
            <text class="info-label">拒绝原因：</text>
            <text class="info-value">{{item.rejectReason}}</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="adoption-actions">
          <button class="action-btn detail-btn" bindtap="viewDetail" data-id="{{item.id}}">
            查看详情
          </button>
          <button 
            wx:if="{{item.status === 'pending'}}" 
            class="action-btn cancel-btn" 
            bindtap="cancelAdoption" 
            data-id="{{item.id}}"
          >
            取消申请
          </button>
          <button 
            class="action-btn contact-btn" 
            bindtap="contactRescue" 
            data-contact="{{item.rescueStationContact}}"
            data-name="{{item.rescueStationName}}"
          >
            联系救助站
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 下拉刷新提示 -->
  <view wx:if="{{refreshing}}" class="refresh-hint">
    <text>正在刷新...</text>
  </view>
</view>