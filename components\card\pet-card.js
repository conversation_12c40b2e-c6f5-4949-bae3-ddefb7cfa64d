// components/card/pet-card.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 宠物数据
    pet: {
      type: Object,
      value: {}
    },
    // 显示模式：compact(紧凑), normal(普通), detailed(详细)
    mode: {
      type: String,
      value: 'normal'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 默认图片
    defaultImage: '/assets/images/default-pet.png'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 点击卡片
    onTapCard: function() {
      const { pet } = this.properties;
      this.triggerEvent('tap', { petId: pet.id });
    },
    
    // 点击收藏按钮
    onTapFavorite: function(e) {
      e.stopPropagation(); // 阻止事件冒泡
      const { pet } = this.properties;
      this.triggerEvent('favorite', { petId: pet.id, isFavorite: !pet.isFavorite });
    },
    
    // 点击领养按钮
    onTapAdopt: function(e) {
      e.stopPropagation(); // 阻止事件冒泡
      const { pet } = this.properties;
      this.triggerEvent('adopt', { petId: pet.id });
    },
    
    // 图片加载失败
    onImageError: function() {
      this.setData({
        'pet.imageUrl': this.data.defaultImage
      });
    }
  }
})