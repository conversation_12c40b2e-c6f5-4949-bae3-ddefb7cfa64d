.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* Tab 导航 */
.tab-bar {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item:active {
  background-color: rgba(255, 111, 97, 0.05);
}

.tab-text {
  font-size: 30rpx;
  color: #666;
  transition: color 0.3s;
}

.tab-item.active .tab-text {
  color: #FF6F61;
  font-weight: bold;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(135deg, #FF6F61 0%, #FF8A75 100%);
  border-radius: 2rpx;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 60rpx;
    opacity: 1;
  }
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6F61;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 内容区域 */
.content {
  padding: 20rpx;
}

/* 统计信息头部 */
.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.stats-text {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

/* 添加评价按钮 */
.add-evaluation-btn {
  background: linear-gradient(135deg, #FF6F61 0%, #FF8A75 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  padding: 24rpx 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 111, 97, 0.3);
  transition: all 0.2s;
}

.add-evaluation-btn.small {
  font-size: 24rpx;
  padding: 16rpx 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 111, 97, 0.2);
}

.add-evaluation-btn::after {
  border: none;
}

.add-evaluation-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(255, 111, 97, 0.4);
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 40rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin-top: 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
  text-align: center;
  line-height: 1.4;
}

/* 评价列表 */
.evaluation-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.evaluation-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
}

.evaluation-item:active {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
}

/* 评价头部 */
.evaluation-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

/* 预约项目样式 */
.reservation-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
  margin-bottom: 20rpx;
}

.reservation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.username {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.contact {
  font-size: 24rpx;
  color: #666;
}

.cancel-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffebee;
  border-radius: 30rpx;
  transition: all 0.2s;
}

.cancel-btn:active {
  background-color: #ffcdd2;
  transform: scale(0.95);
}

.cancel-icon {
  font-size: 24rpx;
  color: #f44336;
  font-weight: bold;
}

.reservation-content {
  padding: 20rpx 0;
  border-top: 1rpx solid #f5f5f5;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 26rpx;
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}

.value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

.value.healthy {
  color: #4CAF50;
  font-weight: 500;
}

.value.unhealthy {
  color: #FF9800;
  font-weight: 500;
}

/* 修改按钮样式 */
.add-reservation-btn {
  background: linear-gradient(135deg, #FF6F61 0%, #FF8A75 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  padding: 24rpx 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 111, 97, 0.3);
  transition: all 0.2s;
}

.hospital-info,
.shop-info,
.rescue-info {
  flex: 1;
}

.hospital-name,
.shop-name,
.rescue-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
  line-height: 1.3;
}

.rating {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.stars-container {
  display: flex;
  gap: 4rpx;
}

.star {
  font-size: 28rpx;
  color: #E5E5E5;
  transition: color 0.2s;
}

.star.filled {
  color: #FFD700;
}

.rating-text {
  font-size: 24rpx;
  color: #666;
  margin-left: 8rpx;
}

.evaluation-actions {
  display: flex;
  align-items: center;
}

.delete-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  border-radius: 32rpx;
  transition: all 0.2s;
}

.delete-btn:active {
  background-color: #ffebee;
  transform: scale(0.95);
}

.delete-icon {
  font-size: 28rpx;
}

/* 评价内容 */
.evaluation-content {
  margin-bottom: 24rpx;
}

.evaluation-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  word-break: break-word;
}

/* 评价底部 */
.evaluation-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f5f5f5;
}

.evaluation-time {
  font-size: 24rpx;
  color: #999;
}

.status-badge {
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.status-badge.published {
  color: #4CAF50;
  background-color: rgba(76, 175, 80, 0.1);
}

.status-badge.pending {
  color: #FF9800;
  background-color: rgba(255, 152, 0, 0.1);
}

/* 加载更多和没有更多数据 */
.load-more-container,
.no-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  margin-top: 20rpx;
}

.load-more-text {
  font-size: 26rpx;
  color: #999;
}

.no-more-text {
  font-size: 26rpx;
  color: #ccc;
}

/* 刷新提示 */
.refresh-hint {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #FF6F61 0%, #FF8A75 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  z-index: 1000;
  animation: slideDown 0.3s ease-out;
  box-shadow: 0 4rpx 12rpx rgba(255, 111, 97, 0.3);
}

.refresh-icon {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .tab-text {
    font-size: 26rpx;
  }
  
  .evaluation-item {
    padding: 24rpx;
  }
  
  .hospital-name,
  .shop-name,
  .rescue-name {
    font-size: 28rpx;
  }
  
  .evaluation-text {
    font-size: 26rpx;
  }
  
  .add-evaluation-btn {
    font-size: 26rpx;
    padding: 20rpx 36rpx;
  }
  
  .stats-header {
    padding: 16rpx 20rpx;
  }
  
  .stats-text {
    font-size: 24rpx;
  }
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .container {
    background-color: #1a1a1a;
  }
  
  .tab-bar {
    background-color: #2d2d2d;
    border-bottom-color: #404040;
  }
  
  .tab-text {
    color: #b3b3b3;
  }
  
  .tab-item.active .tab-text {
    color: #FF8A75;
  }
  
  .evaluation-item,
  .stats-header,
  .empty-container {
    background-color: #2d2d2d;
  }
  
  .hospital-name,
  .shop-name,
  .rescue-name,
  .evaluation-text,
  .empty-text {
    color: #e6e6e6;
  }
  
  .delete-btn {
    background-color: #404040;
  }
  
  .delete-btn:active {
    background-color: #4a4a4a;
  }
}