// services/postService.js
import request from './request';

// 发布信息
export function createPost(data) {
    return request({
        url: '/api/post/create',
        method: 'POST',
        data
    });
}

// 获取发布列表
export function getPostList(params) {
    return request({
        url: '/api/post/list',
        method: 'GET',
        data: params
    });
}

// 获取单个发布详情
export function getPostDetail(id) {
    return request({
        url: `/api/post/${id}`,
        method: 'GET'
    });
}