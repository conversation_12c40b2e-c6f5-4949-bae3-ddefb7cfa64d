/**
 * 网络请求封装 - 联调优化版
 */
import { CONFIG } from './config.js';

// 请求队列，用于处理重复请求
const requestQueue = {};
// Loading 状态管理
let loadingCount = 0;

/**
 * 判断是否为Mock接口返回的随机文本
 * @param {string} text - 待检查的文本
 * @returns {boolean} 是否为随机文本
 */
function isMockRandomText(text) {
  if (!text || typeof text !== 'string') {
    return false;
  }
  const mockWords = [
    'lorem', 'ipsum', 'dolor', 'sit', 'amet', 'consectetur', 'adipiscing', 'elit',
    'proident', 'magna', 'aute', 'eiusmod', 'tempor', 'incididunt', 'labore',
    'dolore', 'aliqua', 'enim', 'minim', 'veniam', 'quis', 'nostrud',
    'exercitation', 'ullamco', 'laboris', 'nisi', 'aliquip', 'commodo', 'culpa'
  ];
  const words = text.toLowerCase().split(/\s+/);
  const mockWordsFound = words.filter(word => mockWords.includes(word));
  return mockWordsFound.length >= 1;
}

/**
 * 显示 Loading
 */
function showLoading(title = '加载中') {
  loadingCount++;
  if (loadingCount === 1) {
    wx.showLoading({
      title,
      mask: true
    });
  }
}

/**
 * 隐藏 Loading
 */
function hideLoading() {
  loadingCount--;
  if (loadingCount <= 0) {
    loadingCount = 0;
    wx.hideLoading();
  }
}

/**
 * 统一请求方法
 * @param {Object} options - 请求选项
 * @returns {Promise} 返回请求结果的 Promise
 */
function request({
  url,
  method = 'GET',
  data = {},
  headers = {},
  useMock = false, // 默认不使用Mock
  showLoading: showLoadingFlag = true,
  hideError = false,
  beforeRequest,
  afterRequest
}) {
  // 生成请求唯一标识
  const requestKey = `${method}-${url}-${JSON.stringify(data)}`;

  // 如果存在相同的请求，则返回已有的请求
  if (requestQueue[requestKey]) {
    console.log('🔄 发现重复请求，返回已有请求:', requestKey);
    return requestQueue[requestKey];
  }

  // 显示加载提示
  if (showLoadingFlag) {
    showLoading();
  }

  // 请求前钩子
  if (typeof beforeRequest === 'function') {
    beforeRequest();
  }

  // 处理请求地址
  const requestUrl = (useMock ? CONFIG.MOCK_URL || CONFIG.BASE_URL : CONFIG.BASE_URL) + url;

  // 构建默认headers
  const defaultHeaders = {
    'Authorization': wx.getStorageSync('token') || '',
    'Content-Type': method === 'POST' || method === 'PUT' ? 'application/json' : 'application/x-www-form-urlencoded'
  };

  // 合并自定义headers（自定义headers优先级更高）
  const finalHeaders = { ...defaultHeaders, ...headers };

  // 开发环境打印详细日志
  if (CONFIG.DEV_CONFIG?.ENABLE_LOG) {
    console.log(`🚀 发起${method}请求:`, requestUrl, data);
    console.log('📋 请求头:', finalHeaders);
    if (useMock && CONFIG.DEV_CONFIG?.SHOW_MOCK_TIP) {
      console.log('🎯 当前使用 Mock 数据');
    }
  }

  // 创建请求
  const requestTask = new Promise((resolve, reject) => {
    // 请求超时定时器
    let timeoutTimer = null;

    // 创建超时处理
    if (CONFIG.TIMEOUT > 0) {
      timeoutTimer = setTimeout(() => {
        if (showLoadingFlag) hideLoading();
        if (!hideError) {
          wx.showToast({
            title: '请求超时，请稍后重试',
            icon: 'none',
            duration: 2000
          });
        }
        delete requestQueue[requestKey];
        reject(new Error('请求超时'));
      }, CONFIG.TIMEOUT);
    }

    const requestObj = wx.request({
      url: requestUrl,
      method,
      data,
      header: finalHeaders,
      success: res => {
        if (timeoutTimer) clearTimeout(timeoutTimer);

        if (CONFIG.DEV_CONFIG?.ENABLE_LOG) {
          console.log(`✅ ${method}请求成功:`, requestUrl, res);
        }

        if (typeof afterRequest === 'function') {
          afterRequest(res);
        }

        // 状态码处理
        if (res.statusCode >= 200 && res.statusCode < 300) {
          if (res.data && typeof res.data === 'object') {
            if (res.data.code !== undefined) {
              if (res.data.code === CONFIG.ERROR_CODES.SUCCESS) {
                resolve(res.data);
              } else {
                let errorMsg = res.data.message || `业务错误(${res.data.code})`;
                if (isMockRandomText(errorMsg)) {
                  errorMsg = 'Mock接口暂时不可用，请稍后重试';
                }
                if (!hideError) {
                  wx.showToast({
                    title: errorMsg,
                    icon: 'none',
                    duration: 2000
                  });
                }
                reject(new Error(errorMsg));
              }
            } else {
              resolve(res.data);
            }
          } else {
            resolve(res.data);
          }
        } else if (res.statusCode === 401) {
          handleUnauthorized();
          reject(new Error('登录已过期，请重新登录'));
        } else {
          const errorMessage = res.data?.message || `请求失败(${res.statusCode})`;
          if (!hideError) {
            wx.showToast({
              title: errorMessage,
              icon: 'none',
              duration: 2000
            });
          }
          reject(new Error(errorMessage));
        }
      },
      fail: err => {
        if (timeoutTimer) clearTimeout(timeoutTimer);
        console.error(`❌ ${method}请求失败:`, requestUrl, err);
        let errorMessage = '网络异常，请检查网络连接';
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMessage = '请求超时，请稍后重试';
          } else if (err.errMsg.includes('fail')) {
            errorMessage = '网络连接失败，请检查网络设置';
          }
        }
        if (!hideError) {
          wx.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 2000
          });
        }
        reject(new Error(errorMessage));
      },
      complete: () => {
        if (timeoutTimer) clearTimeout(timeoutTimer);
        if (showLoadingFlag) hideLoading();
        delete requestQueue[requestKey];
      }
    });
  });

  requestQueue[requestKey] = requestTask;
  return requestTask;
}

/**
 * 处理未授权错误
 */
let isRedirecting = false;
function handleUnauthorized() {
  if (isRedirecting) {
    console.log('🔒 正在跳转中，忽略重复请求');
    return;
  }
  isRedirecting = true;
  wx.showToast({
    title: '登录已过期，请重新登录',
    icon: 'none',
    duration: 2000
  });
  wx.removeStorageSync('token');
  wx.removeStorageSync('userInfo');
  setTimeout(() => {
    wx.reLaunch({
      url: '/pages/login/login'
    });
    isRedirecting = false;
  }, 2000);
}

/**
 * GET 请求
 */
function get(url, data = {}, options = {}) {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  });
}

/**
 * POST 请求
 */
function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  });
}

/**
 * PUT 请求
 */
function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  });
}

/**
 * DELETE 请求
 */
function del(url, data = {}, options = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  });
}

/**
 * 上传文件
 */
function uploadFile(url, filePath, name = 'file', formData = {}, useMock = false, headers = {}) {
  return new Promise((resolve, reject) => {
    let timeoutTimer = null;
    showLoading('上传中');
    if (CONFIG.TIMEOUT > 0) {
      timeoutTimer = setTimeout(() => {
        hideLoading();
        wx.showToast({
          title: '上传超时，请稍后重试',
          icon: 'none',
          duration: 2000
        });
        reject(new Error('上传超时'));
      }, CONFIG.TIMEOUT);
    }
    const uploadUrl = (useMock ? CONFIG.MOCK_URL || CONFIG.BASE_URL : CONFIG.BASE_URL) + url;
    const defaultHeaders = {
      'Authorization': wx.getStorageSync('token') || ''
    };
    const finalHeaders = { ...defaultHeaders, ...headers };
    if (CONFIG.DEV_CONFIG?.ENABLE_LOG) {
      console.log('🚀 开始上传文件:', uploadUrl, { filePath, name, formData });
      console.log('📋 上传请求头:', finalHeaders);
    }
    const uploadTask = wx.uploadFile({
      url: uploadUrl,
      filePath,
      name,
      formData,
      header: finalHeaders,
      success: res => {
        let data;
        try {
          data = JSON.parse(res.data);
        } catch (e) {
          data = res.data;
        }
        if (CONFIG.DEV_CONFIG?.ENABLE_LOG) {
          console.log('✅ 文件上传成功:', data);
        }
        if (res.statusCode >= 200 && res.statusCode < 300) {
          if (data && data.code !== undefined) {
            if (data.code === CONFIG.ERROR_CODES.SUCCESS) {
              resolve(data);
            } else {
              let errorMsg = data.message || '上传失败';
              if (isMockRandomText(errorMsg)) {
                errorMsg = 'Mock接口暂时不可用，请稍后重试';
              }
              wx.showToast({
                title: errorMsg,
                icon: 'none',
                duration: 2000
              });
              reject(new Error(errorMsg));
            }
          } else {
            resolve(data);
          }
        } else {
          wx.showToast({
            title: data.message || '上传失败',
            icon: 'none',
            duration: 2000
          });
          reject(new Error(data.message || '上传失败'));
        }
      },
      fail: err => {
        wx.showToast({
          title: '上传失败，请检查网络连接',
          icon: 'none',
          duration: 2000
        });
        console.error('❌ 文件上传失败:', err);
        reject(err);
      },
      complete: () => {
        if (timeoutTimer) clearTimeout(timeoutTimer);
        hideLoading();
      }
    });
    uploadTask.onProgressUpdate((res) => {
      if (CONFIG.DEV_CONFIG?.ENABLE_LOG) {
        console.log('📊 上传进度:', res.progress + '%');
      }
    });
  });
}

/**
 * 批量请求
 */
function batchRequest(requests) {
  const promises = requests.map(req => {
    return request({
      url: req.url,
      method: req.method || 'GET',
      data: req.data || {},
      ...req.options
    });
  });
  return Promise.allSettled(promises);
}

export default {
  request,
  get,
  post,
  put,
  delete: del,
  uploadFile,
  batchRequest,
  showLoading,
  hideLoading
};