/* pages/rescue/detail/detail.wxss */

.container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6F61;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 详情内容 */
.detail-content {
  padding-bottom: 40rpx;
}

/* 救助站头部 */
.station-header {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.station-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.header-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 60rpx 30rpx 30rpx;
  color: white;
}

.station-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.station-address {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
}

/* 快捷操作按钮 */
.quick-actions {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  display: flex;
  gap: 15rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  min-width: 80rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.action-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 1);
}

/* 管理按钮特殊样式 */
.manage-btn {
  background: rgba(255, 111, 97, 0.9);
  color: white;
}

.manage-btn:active {
  background: rgba(255, 111, 97, 1);
}

.manage-btn .action-text {
  color: white;
}

.action-icon {
  font-size: 28rpx;
  margin-bottom: 5rpx;
}

.action-text {
  font-size: 22rpx;
  color: #333;
}

/* 信息区域 */
.info-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.evaluation-count {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}

/* 信息项目 */
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  word-break: break-word;
}

.website-link {
  color: #4285f4;
}

/* 描述文本 */
.description-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  word-break: break-word;
}

/* 服务项目网格 */
.services-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.service-item {
  padding: 12rpx 20rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 1rpx solid #e9ecef;
}

.service-text {
  font-size: 24rpx;
  color: #495057;
}

/* 评价区域 */
.evaluation-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.evaluation-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.my-evaluation-link {
  font-size: 26rpx;
  color: #4285f4;
}

.add-evaluation-btn {
  padding: 8rpx 20rpx;
  background: #FF6F61;
  border-radius: 20rpx;
  color: white;
  transition: background 0.2s;
}

.add-evaluation-btn:active {
  background: #e55a4e;
}

.btn-text {
  font-size: 24rpx;
}

/* 评价列表状态 */
.evaluations-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 60rpx 0;
  font-size: 26rpx;
  color: #999;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #FF6F61;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.no-evaluations {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 60rpx 0;
  font-size: 26rpx;
  color: #999;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

/* 评价列表 */
.evaluations-list {
  margin-top: 20rpx;
}

.evaluation-item {
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.evaluation-item:last-child {
  border-bottom: none;
}

.evaluation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.user-avatar-placeholder {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #FF6F61, #FF8A75);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.user-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.rating-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5rpx;
}

.rating-stars {
  display: flex;
  gap: 2rpx;
}

.star {
  font-size: 20rpx;
}

.star-filled {
  color: #FFD700;
}

.star-empty {
  color: #ddd;
}

.rating-number {
  font-size: 20rpx;
  color: #999;
}

.evaluation-content {
  margin-bottom: 15rpx;
}

.evaluation-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  word-break: break-word;
}

.evaluation-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.evaluation-time {
  font-size: 24rpx;
  color: #999;
}

/* 加载更多提示 */
.load-more-hint,
.no-more-hint {
  text-align: center;
  padding: 30rpx 0;
  font-size: 24rpx;
  color: #999;
}

/* 评价表单弹窗 */
.evaluation-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  width: 600rpx;
  max-height: 80vh;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    transform: scale(0.9) translateY(50rpx);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #999;
  border-radius: 20rpx;
  background: #f8f9fa;
}

.modal-close:active {
  background: #e9ecef;
}

/* 表单内容 */
.form-content {
  padding: 30rpx;
  max-height: 50vh;
  overflow-y: auto;
}

.station-info-display {
  margin-bottom: 25rpx;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.station-name-small {
  font-size: 26rpx;
  color: #666;
  text-align: center;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 600;
}

/* 评分选择器 */
.rating-selector {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.star-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  border-radius: 10rpx;
  transition: all 0.2s;
  color: #ddd;
}

.star-btn.selected {
  color: #FFD700;
  background: rgba(255, 215, 0, 0.1);
  transform: scale(1.1);
}

.star-btn:active {
  transform: scale(0.9);
}

.rating-desc {
  font-size: 26rpx;
  color: #666;
  margin-left: 10rpx;
}

/* 内容输入框 */
.content-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  background: #f8f9fa;
  box-sizing: border-box;
}

.content-input:focus {
  border-color: #FF6F61;
  background: white;
}

.char-count-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 10rpx;
}

.char-count {
  font-size: 24rpx;
  color: #999;
}

.char-count.char-limit {
  color: #ff4444;
}

/* 提交说明 */
.submit-note {
  margin-top: 20rpx;
  padding: 15rpx;
  background: #fff3cd;
  border-radius: 8rpx;
  border-left: 4rpx solid #ffc107;
}

.note-text {
  font-size: 24rpx;
  color: #856404;
  line-height: 1.4;
}

/* 弹窗操作按钮 */
.modal-actions {
  display: flex;
  padding: 20rpx 30rpx 30rpx;
  gap: 20rpx;
}

.modal-actions .action-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  font-size: 28rpx;
  transition: all 0.2s;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}

.cancel-btn:active {
  background: #e9ecef;
}

/* 提交按钮样式 */
.submit-btn {
  background: #FF6F61;
  color: white;
  position: relative;
}

.submit-btn:active {
  background: #e55a4e;
}

/* 提交按钮加载状态 */
.submit-btn.loading {
  background: #cccccc;
  pointer-events: none;
}

/* 提交按钮禁用状态 */
.submit-btn.disabled {
  background: #cccccc;
  color: #999;
  pointer-events: none;
}

/* 提交时的加载动画 */
.submit-loading {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.loading-spinner-small {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #FF6F61;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 评价操作按钮 */
.evaluation-actions-buttons {
  display: flex;
  gap: 15rpx;
  align-items: center;
}

.delete-evaluation-btn {
  padding: 8rpx 16rpx;
  background: #ff4444;
  color: white;
  border-radius: 12rpx;
  font-size: 22rpx;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 5rpx;
  min-width: 60rpx;
  justify-content: center;
}

.delete-evaluation-btn:active {
  background: #cc3333;
  transform: scale(0.95);
}

.delete-evaluation-btn.deleting {
  background: #cccccc;
  pointer-events: none;
}

.delete-loading {
  display: flex;
  align-items: center;
  gap: 5rpx;
  font-size: 20rpx;
}

.loading-spinner-tiny {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid #f3f3f3;
  border-top: 2rpx solid #ff4444;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
  .modal-content {
    width: 90%;
    margin: 0 5%;
  }
  
  .quick-actions {
    flex-direction: column;
    gap: 10rpx;
  }
  
  .action-btn {
    min-width: 70rpx;
    padding: 12rpx;
  }
  
  .action-icon {
    font-size: 24rpx;
  }
  
  .action-text {
    font-size: 20rpx;
  }
  
  .evaluation-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 15rpx;
  }
  
  .evaluation-actions-buttons {
    align-self: flex-end;
  }
}