<!-- components/card/rescue-card.wxml -->
<view class="rescue-card {{mode}}" bindtap="onTapCard">
  <!-- 紧凑模式 -->
  <block wx:if="{{mode === 'compact'}}">
    <view class="rescue-card-compact">
      <image 
        class="rescue-image-compact" 
        src="{{rescue.imageUrl || defaultImage}}" 
        mode="aspectFill"
        binderror="onImageError"
      ></image>
      <view class="rescue-info-compact">
        <view class="rescue-name-compact text-ellipsis">{{rescue.name || '未命名'}}</view>
        <view class="rescue-category-compact text-ellipsis text-secondary">{{rescue.category || '救助站'}}</view>
      </view>
    </view>
  </block>

  <!-- 普通模式 -->
  <block wx:elif="{{mode === 'normal'}}">
    <view class="rescue-card-normal">
      <image 
        class="rescue-image-normal" 
        src="{{rescue.imageUrl || defaultImage}}" 
        mode="aspectFill"
        binderror="onImageError"
      ></image>
      <view class="rescue-info-normal">
        <view class="rescue-name-normal text-ellipsis">{{rescue.name || '未命名'}}</view>
        <view class="rescue-rating-normal">
          <view class="rating-stars">
            <block wx:for="{{5}}" wx:key="index">
              <image 
                class="star-icon" 
                src="{{index < rescue.rating ? '/assets/images/star-filled.png' : '/assets/images/star.png'}}" 
                mode="aspectFit"
              ></image>
            </block>
          </view>
          <view class="rating-value">{{rescue.rating || '0.0'}}</view>
          <view class="rating-count text-secondary">({{rescue.ratingCount || 0}})</view>
        </view>
        <view class="rescue-address-normal text-ellipsis text-secondary">{{rescue.address || '暂无地址'}}</view>
        <view wx:if="{{rescue.animalCount}}" class="rescue-animal-count">
          <text class="animal-count-text">现有动物：</text>
          <text class="animal-count-value">{{rescue.animalCount || 0}}只</text>
        </view>
        <view class="rescue-actions-normal">
          <view 
            class="rescue-action-item" 
            catchtap="onTapPhone"
          >
            <image 
              class="action-icon" 
              src="/assets/images/phone.png" 
              mode="aspectFit"
            ></image>
            <text class="action-text">电话</text>
          </view>
          <view 
            class="rescue-action-item" 
            catchtap="onTapNavigation"
          >
            <image 
              class="action-icon" 
              src="/assets/images/location.png" 
              mode="aspectFit"
            ></image>
            <text class="action-text">导航</text>
          </view>
          <view 
            class="rescue-action-item" 
            catchtap="onTapDonate"
          >
            <image 
              class="action-icon" 
              src="/assets/images/heart.png" 
              mode="aspectFit"
            ></image>
            <text class="action-text">捐赠</text>
          </view>
        </view>
      </view>
    </view>
  </block>

  <!-- 详细模式 -->
  <block wx:else>
    <view class="rescue-card-detailed">
      <image 
        class="rescue-image-detailed" 
        src="{{rescue.imageUrl || defaultImage}}" 
        mode="aspectFill"
        binderror="onImageError"
      ></image>
      <view class="rescue-info-detailed">
        <view class="rescue-header-detailed">
          <view class="rescue-name-detailed">{{rescue.name || '未命名'}}</view>
          <view class="rescue-rating-detailed">
            <block wx:for="{{5}}" wx:key="index">
              <image 
                class="star-icon" 
                src="{{index < rescue.rating ? '/assets/images/star-filled.png' : '/assets/images/star.png'}}" 
                mode="aspectFit"
              ></image>
            </block>
            <view class="rating-value">{{rescue.rating || '0.0'}}</view>
          </view>
        </view>
        <view class="rescue-meta-detailed">
          <view class="rescue-meta-item">
            <image class="meta-icon" src="/assets/images/tag.png" mode="aspectFit"></image>
            <text class="meta-text">{{rescue.category || '救助站'}}</text>
          </view>
          <view class="rescue-meta-item">
            <image class="meta-icon" src="/assets/images/location-pin.png" mode="aspectFit"></image>
            <text class="meta-text">{{rescue.address || '暂无地址'}}</text>
          </view>
          <view class="rescue-meta-item">
            <image class="meta-icon" src="/assets/images/phone-alt.png" mode="aspectFit"></image>
            <text class="meta-text">{{rescue.phone || '暂无电话'}}</text>
          </view>
          <view class="rescue-meta-item">
            <image class="meta-icon" src="/assets/images/pet.png" mode="aspectFit"></image>
            <text class="meta-text">现有动物：{{rescue.animalCount || 0}}只</text>
          </view>
        </view>
        <view class="rescue-description-detailed">
          {{rescue.description || '暂无描述'}}
        </view>
        <view class="rescue-animal-types" wx:if="{{rescue.animalTypes && rescue.animalTypes.length > 0}}">
          <view class="rescue-animal-types-title">收养类型</view>
          <view class="rescue-animal-types-list">
            <view 
              class="rescue-animal-type-tag"
              wx:for="{{rescue.animalTypes}}"
              wx:key="index"
            >{{item}}</view>
          </view>
        </view>
        <view class="rescue-actions-detailed">
          <view 
            class="rescue-action-button primary" 
            catchtap="onTapDonate"
          >
            <image 
              class="action-icon" 
              src="/assets/images/heart.png" 
              mode="aspectFit"
            ></image>
            <text class="action-text">爱心捐赠</text>
          </view>
          <view 
            class="rescue-action-button" 
            catchtap="onTapPhone"
          >
            <image 
              class="action-icon" 
              src="/assets/images/phone.png" 
              mode="aspectFit"
            ></image>
            <text class="action-text">电话咨询</text>
          </view>
        </view>
      </view>
    </view>
  </block>
</view>