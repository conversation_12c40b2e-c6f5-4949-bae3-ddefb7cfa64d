<!-- pages/shop/shop.wxml -->
<view class="shop-container">
  <!-- 搜索头部区域 -->
  <view class="search-header">
    <view class="header-decoration"></view>
    <view class="header-decoration-2"></view>
    
    <view class="header-content">
      <text class="header-title">🛍️ 宠物商店</text>
      <text class="header-subtitle">一站式宠物用品购物平台</text>
      
      <!-- 地址搜索框 -->
      <view class="search-wrapper">
        <view class="search-input-container">
          <image class="search-icon" src="/assets/images/search.png"></image>
          <input 
            class="search-input" 
            placeholder="输入城市或地区搜索商店..." 
            value="{{searchAddress}}"
            bindinput="onSearchInput"
            confirm-type="search"
          />
          <view class="search-loading" wx:if="{{isLoading}}">
            <view class="loading-dot"></view>
            <view class="loading-dot"></view>
            <view class="loading-dot"></view>
          </view>
        </view>
      </view>

      <!-- 常用地址选择 -->
      <scroll-view scroll-x="true" class="address-scroll" show-scrollbar="{{false}}">
        <view class="address-container">
          <view 
            wx:for="{{commonAddresses}}" 
            wx:key="index" 
            class="address-chip {{searchAddress === item ? 'active' : ''}}" 
            bindtap="selectCommonAddress" 
            data-address="{{item}}"
          >
            <text class="address-text">{{item}}</text>
            <view class="address-indicator" wx:if="{{searchAddress === item}}"></view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 商店列表内容 -->
  <view class="content-section">
    <!-- 搜索结果统计 -->
    <view class="result-stats" wx:if="{{shops.length > 0 && !isLoading}}">
      <text class="stats-text">找到 {{total || shops.length}} 家商店</text>
      <text class="current-address">📍 {{searchAddress}}</text>
    </view>

    <!-- 商店卡片列表 -->
    <view class="shop-list" wx:if="{{shops.length > 0}}">
      <view 
        wx:for="{{shops}}" 
        wx:key="id" 
        class="shop-card"
        data-index="{{index}}"
      >
        <!-- 商店图片和信息 -->
        <view class="card-image-container" bindtap="goToDetail" data-id="{{item.id}}">
          <image class="shop-image" src="{{item.image}}" mode="aspectFill"></image>
          
          <!-- 营业状态标签 -->
          <view class="status-badge">
            <text class="status-text">营业中</text>
          </view>
          
          <!-- 悬停效果 -->
          <view class="image-overlay">
            <view class="overlay-content">
              <image class="overlay-icon" src="/assets/images/search.png"></image>
              <text class="overlay-text">查看详情</text>
            </view>
          </view>
        </view>

        <!-- 商店信息 -->
        <view class="card-content">
          <view class="shop-header">
            <text class="shop-name">{{item.name}}</text>
            <view class="shop-badge">
              <text class="badge-text">正品保证</text>
            </view>
          </view>

          <view class="shop-address">
            <image class="location-icon" src="/assets/images/location-pin.png"></image>
            <text class="address-text">{{item.address}}</text>
          </view>

          <view class="shop-description" wx:if="{{item.description}}">
            <text class="desc-text">{{item.description}}</text>
          </view>

          <view class="shop-meta">
            <view class="meta-item">
              <image class="meta-icon" src="/assets/images/heart.png"></image>
              <text class="meta-text">{{item.contact}}</text>
            </view>
            <view class="license-info" wx:if="{{item.license}}">
              <image class="license-icon" src="/assets/images/search.png"></image>
              <text class="license-text">{{item.license}}</text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="card-actions">
            <button 
              class="action-btn contact-btn" 
              bindtap="makePhoneCall" 
              data-contact="{{item.contact}}"
            >
              <image class="btn-icon" src="/assets/images/heart.png"></image>
              <text>联系商家</text>
            </button>
            
            <button 
              class="action-btn detail-btn" 
              bindtap="goToDetail" 
              data-id="{{item.id}}"
            >
              <image class="btn-icon" src="/assets/images/search.png"></image>
              <text>查看详情</text>
            </button>
          </view>
        </view>

        <!-- 卡片装饰元素 -->
        <view class="card-decoration">
          <view class="decoration-line"></view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-container" wx:elif="{{!isLoading}}">
      <view class="empty-content">
        <image class="empty-image" src="/assets/images/empty.png"></image>
        <view class="empty-text">
          <text class="empty-title">暂无商店数据</text>
          <text class="empty-subtitle">试试搜索其他地区或稍后再试</text>
        </view>
        <button class="refresh-btn" bindtap="onPullDownRefresh">
          <image class="refresh-icon" src="/assets/images/search.png"></image>
          <text>刷新重试</text>
        </button>
      </view>
    </view>

    <!-- 初始加载状态 -->
    <view class="loading-container" wx:if="{{isLoading && shops.length === 0}}">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在搜索附近商店...</text>
      </view>
    </view>

    <!-- 加载更多状态 -->
    <view class="load-more-section" wx:if="{{isLoading && shops.length > 0}}">
      <view class="load-more-content">
        <view class="loading-dots">
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
        </view>
        <text class="load-more-text">正在加载更多...</text>
      </view>
    </view>

    <!-- 加载完成提示 -->
    <view class="end-section" wx:if="{{!hasMore && !isLoading && shops.length > 0}}">
      <view class="end-content">
        <view class="end-line"></view>
        <text class="end-text">已显示全部商店</text>
        <view class="end-line"></view>
      </view>
    </view>
  </view>

  <!-- 悬浮刷新按钮 -->
  <view class="fab-container">
    <view class="fab-button" bindtap="onPullDownRefresh">
      <image class="fab-icon" src="/assets/images/search.png"></image>
      <view class="fab-ripple"></view>
    </view>
  </view>
</view>

<!-- 在shop.wxml中添加按钮 -->
<button bindtap="goToPetManage">管理宠物信息</button>