// pages/rescue/detail/detail.js
import evaluationService from '../../../services/evaluationService'
import rescueService from '../../../services/rescueService'
import userService from '../../../services/userService'

Page({
  data: {
    stationInfo: {}, 
    stationId: null, 
    loading: true,
    
    // 评价相关
    showEvaluationForm: false,
    evaluationForm: {
      rating: 5,
      content: ''
    },
    submittingEvaluation: false,
    
    // 评价列表
    evaluations: [],
    evaluationsLoading: false,
    evaluationPage: 1,
    evaluationPageSize: 10,
    evaluationHasMore: true,
    evaluationTotal: 0,
    
    // 删除评价相关
    deletingEvaluationId: null,
    
    // 星级评分
    stars: [1, 2, 3, 4, 5],
    
    // 当前用户信息
    currentUserId: null,
    currentUserType: null, // 用户类型，用于权限控制
    
    // 权限控制
    isRescueStationUser: false // 是否为救助站用户
  },

  onLoad(options) {
    const { id } = options;
    
    if (!id) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({ stationId: parseInt(id) });
    this.getCurrentUserInfo();
    this.loadStationInfo();
    this.loadEvaluations();
  },

  getCurrentUserInfo() {
    const userInfo = userService.getCurrentUser();
    if (userInfo) {
      const userType = userInfo.userType || '';
      const isRescueStationUser = userType.length >= 3 && userType.length <= 4;
      
      this.setData({
        currentUserId: userInfo.id,
        currentUserType: userType,
        isRescueStationUser: isRescueStationUser
      });
    }
  },

  /**
   * 加载救助站信息 - 需要对接真实API
   */
  async loadStationInfo() {
    try {
      // TODO: 等后端提供救助站详情接口后替换
      // const result = await rescueService.getStationDetail(this.data.stationId);
      
      // 暂时使用模拟数据
      const mockStationInfo = {
        id: this.data.stationId,
        name: '爱心动物救助站',
        address: '北京市朝阳区宠物救助大街123号',
        phone: '010-12345678',
        description: '专业的动物救助机构，致力于救助流浪动物，为它们寻找温暖的家。我们有专业的兽医团队，先进的医疗设备，为每一只流浪动物提供最好的照顾。',
        photo: '/assets/images/default-rescue.png',
        businessHours: '09:00-18:00',
        website: 'www.rescue-station.com',
        capacity: 100,
        currentAnimals: 45,
        establishedYear: 2015,
        services: ['动物救助', '医疗救治', '领养服务', '志愿者培训', '宠物寄养', '健康体检']
      };

      this.setData({
        stationInfo: mockStationInfo,
        loading: false
      });
    } catch (error) {
      console.error('获取救助站详情失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载某个救助站的评价列表 - 不同于用户个人评价API
   */
  async loadEvaluations(isRefresh = false) {
    if (isRefresh) {
      this.setData({
        evaluationPage: 1,
        evaluations: [],
        evaluationHasMore: true,
        evaluationTotal: 0
      });
    }

    this.setData({ evaluationsLoading: true });

    try {
      // 调用获取救助站评价列表接口（不是用户个人评价接口）
      const requestData = {
        page: this.data.evaluationPage,
        pageSize: this.data.evaluationPageSize,
        stationId: this.data.stationId // 这个参数是必需的，用于指定查询哪个救助站的评价
      };

      // 注意：这里调用的应该是获取救助站评价列表的接口，而不是用户个人评价接口
      const result = await evaluationService.getStationEvaluations(requestData);

      this.setData({ evaluationsLoading: false });

      if (result && result.code === 200) {
        const newEvaluations = result.data || [];
        const currentEvaluations = isRefresh ? [] : this.data.evaluations;
        
        // 处理头像路径 - 符合API文档要求
        const processedEvaluations = newEvaluations.map(item => ({
          ...item,
          avatar: this.processImagePath(item.avatar),
          userName: item.username,
          canDelete: this.checkCanDeleteEvaluation(item)
        }));
        
        this.setData({
          evaluations: [...currentEvaluations, ...processedEvaluations],
          evaluationPage: this.data.evaluationPage + 1,
          evaluationHasMore: newEvaluations.length >= this.data.evaluationPageSize,
          evaluationTotal: result.total || 0
        });
      } else {
        throw new Error(result.message || '获取评价失败');
      }
    } catch (error) {
      console.error('获取评价列表失败:', error);
      this.setData({ evaluationsLoading: false });
      
      let errorMessage = '获取评价失败';
      if (error.code === 400) {
        errorMessage = '参数无效';
      } else if (error.code === 403) {
        errorMessage = '无权限访问';
      } else if (error.code === 404) {
        errorMessage = '救助站不存在';
      } else if (error.message) {
        errorMessage = error.message;
      }

      if (isRefresh || this.data.evaluations.length === 0) {
        wx.showToast({
          title: errorMessage,
          icon: 'none'
        });
        this.loadMockEvaluations();
      }
    }
  },

  loadMockEvaluations() {
    const mockEvaluations = [
      {
        id: 1,
        username: '爱心用户A',
        avatar: 'https://avatars.githubusercontent.com/u/68187022',
        rating: 5,
        content: '非常棒的救助站，工作人员很负责，环境也很好！设施很完善，动物们都很健康。',
        createTime: '2024-03-15 14:30',
        userId: 1001
      },
      {
        id: 2,
        username: '志愿者B',
        avatar: 'D:/images/avatar2.jpg',
        rating: 4,
        content: '参与过志愿活动，感受到了大家对动物的爱心。希望更多人关注流浪动物。',
        createTime: '2024-03-10 16:20',
        userId: 1002
      }
    ];

    const processedMockEvaluations = mockEvaluations.map(item => ({
      ...item,
      avatar: this.processImagePath(item.avatar),
      userName: item.username,
      canDelete: this.checkCanDeleteEvaluation(item)
    }));

    this.setData({
      evaluations: processedMockEvaluations,
      evaluationHasMore: false,
      evaluationTotal: mockEvaluations.length
    });
  },

  checkCanDeleteEvaluation(evaluation) {
    const currentUserId = this.data.currentUserId;
    return currentUserId && evaluation.userId === currentUserId;
  },

  /**
   * 处理图片路径 - 严格按照API文档要求
   */
  processImagePath(imagePath) {
    if (!imagePath) return '/assets/images/default-avatar.png';
    
    // 如果是完整URL（http/https），直接使用
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return imagePath;
    }
    
    // 如果是相对路径（D:/images/），需要拼接前缀
    if (imagePath.startsWith('D:/images/')) {
      // TODO: 这里应该根据实际的图片服务器地址进行拼接
      // 例如：return `${CONFIG.IMAGE_BASE_URL}/${imagePath.replace('D:/images/', '')}`;
      return imagePath; // 暂时直接返回，等后端提供完整的图片服务配置
    }
    
    return imagePath;
  },

  /**
   * 提交评价 - 严格符合接口文档要求
   */
  async submitEvaluation() {
    const { stationId, evaluationForm } = this.data;
    const { rating, content } = evaluationForm;

    // 参数验证
    if (!stationId || !Number.isInteger(stationId) || stationId <= 0) {
      wx.showToast({
        title: '救助站ID无效',
        icon: 'none'
      });
      return;
    }

    if (!rating || rating < 1 || rating > 5) {
      wx.showToast({
        title: '请选择1-5星评分',
        icon: 'none'
      });
      return;
    }

    if (!content || content.trim() === '') {
      wx.showToast({
        title: '请输入评价内容',
        icon: 'none'
      });
      return;
    }

    if (content.length > 200) {
      wx.showToast({
        title: '评价内容不能超过200字符',
        icon: 'none'
      });
      return;
    }

    // 检查登录状态
    if (!userService.isLoggedIn()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }, 1500);
      return;
    }

    this.setData({ submittingEvaluation: true });

    try {
      const requestData = {
        stationId: stationId,
        rating: rating,
        content: content.trim()
      };

      const result = await evaluationService.evaluateRescueStation(requestData);

      this.setData({ submittingEvaluation: false });

      if (result && result.code === 200) {
        wx.showToast({
          title: result.message || '评价成功',
          icon: 'success'
        });

        this.hideEvaluationForm();
        this.loadEvaluations(true);
      } else {
        wx.showToast({
          title: result.message || '评价失败',
          icon: 'none'
        });
      }
    } catch (error) {
      this.setData({ submittingEvaluation: false });
      console.error('提交评价失败:', error);

      let errorMessage = '评价失败，请重试';
      
      if (error.code === 400) {
        errorMessage = error.message || '参数无效，请检查评分和内容';
      } else if (error.code === 403) {
        errorMessage = error.message || '请先登录';
      } else if (error.code === 404) {
        errorMessage = error.message || '救助站不存在';
      } else if (error.message) {
        errorMessage = error.message;
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none'
      });
    }
  },

  async deleteEvaluation(e) {
    const evaluationId = e.currentTarget.dataset.id;
    
    if (!evaluationId) {
      wx.showToast({
        title: '评价ID无效',
        icon: 'none'
      });
      return;
    }

    if (!userService.isLoggedIn()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条评价吗？删除后无法恢复',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.performDeleteEvaluation(evaluationId);
        }
      }
    });
  },

  async performDeleteEvaluation(evaluationId) {
    this.setData({ deletingEvaluationId: evaluationId });

    try {
      const result = await evaluationService.deleteRescueStationEvaluation(evaluationId);

      this.setData({ deletingEvaluationId: null });

      if (result && result.code === 200) {
        wx.showToast({
          title: result.message || '删除成功',
          icon: 'success'
        });

        const updatedEvaluations = this.data.evaluations.filter(item => item.id !== evaluationId);
        this.setData({
          evaluations: updatedEvaluations,
          evaluationTotal: Math.max(0, this.data.evaluationTotal - 1)
        });
      } else {
        wx.showToast({
          title: result.message || '删除失败',
          icon: 'none'
        });
      }
    } catch (error) {
      this.setData({ deletingEvaluationId: null });
      console.error('删除评价失败:', error);

      let errorMessage = '删除失败，请重试';
      
      if (error.code === 403) {
        errorMessage = error.message || '无权限删除此评价';
      } else if (error.code === 404) {
        errorMessage = error.message || '评价不存在';
      } else if (error.message) {
        errorMessage = error.message;
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none'
      });
    }
  },

  showEvaluationForm() {
    if (!userService.isLoggedIn()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }, 1500);
      return;
    }

    this.setData({ showEvaluationForm: true });
  },

  hideEvaluationForm() {
    this.setData({ 
      showEvaluationForm: false,
      evaluationForm: {
        rating: 5,
        content: ''
      }
    });
  },

  selectRating(e) {
    const rating = parseInt(e.currentTarget.dataset.rating);
    this.setData({
      'evaluationForm.rating': rating
    });
  },

  inputEvaluationContent(e) {
    const content = e.detail.value;
    
    if (content.length > 200) {
      wx.showToast({
        title: '评价内容不能超过200字符',
        icon: 'none'
      });
      return;
    }

    this.setData({
      'evaluationForm.content': content
    });
  },

  makePhoneCall() {
    const { phone } = this.data.stationInfo;
    if (!phone) {
      wx.showToast({
        title: '暂无联系电话',
        icon: 'none'
      });
      return;
    }

    wx.makePhoneCall({
      phoneNumber: phone,
      fail: () => {
        wx.showToast({
          title: '拨号失败',
          icon: 'none'
        });
      }
    });
  },

  navigateToStation() {
    const { address, name } = this.data.stationInfo;
    if (!address) {
      wx.showToast({
        title: '暂无地址信息',
        icon: 'none'
      });
      return;
    }

    wx.openLocation({
      name: name,
      address: address,
      fail: () => {
        wx.showToast({
          title: '定位失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 跳转到用户个人评价页面 - 这里会用到您提供的API
   */
  viewMyEvaluations() {
    wx.navigateTo({
      url: '/pages/mine/evaluation/evaluation?tab=2'
    });
  },

  /**
   * 跳转到领养申请管理页面
   */
  manageAdoptions() {
    if (!userService.isLoggedIn()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }, 1500);
      return;
    }

    if (!this.data.isRescueStationUser) {
      wx.showToast({
        title: '仅救助站用户可管理领养申请',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/rescue/adoption-manage/adoption-manage?stationId=${this.data.stationId}`
    });
  },

  onPullDownRefresh() {
    this.loadStationInfo();
    this.loadEvaluations(true);
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  onReachBottom() {
    if (this.data.evaluationHasMore && !this.data.evaluationsLoading) {
      this.loadEvaluations();
    }
  },

  onShareAppMessage() {
    return {
      title: `${this.data.stationInfo.name} - 救助站详情`,
      path: `/pages/rescue/detail/detail?id=${this.data.stationId}`
    };
  }
});