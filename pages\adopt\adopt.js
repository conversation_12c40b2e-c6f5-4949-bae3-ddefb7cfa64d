// pages/adopt/adopt.js
import animalService from '../../services/animalService';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 动物数据
    animals: [],
    // 搜索关键词
    searchKeyword: '',
    // 动物类型筛选
    animalType: '',
    // 是否正在加载
    isLoading: false,
    // 是否有更多数据
    hasMore: true,
    // 当前页码
    currentPage: 1,
    // 每页数据条数
    pageSize: 10,
    // 下拉刷新状态
    refreshing: false,
    // 用户地址
    userAddress: '云南省 贵林市 双桥区 彤路298号 67号门牌', // 可从用户信息获取
    // 数据总数
    total: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    // 初始化用户地址
    this.initUserAddress();
    // 获取动物列表
    this.getAnimalList();
  },

  /**
   * 初始化用户地址
   */
  initUserAddress: function() {
    // 从缓存中获取用户信息
    const userInfo = wx.getStorageSync('userInfo') || {};
    if (userInfo.address) {
      this.setData({
        userAddress: userInfo.address
      });
    }
  },

  /**
   * 获取动物列表
   * @param {boolean} isRefresh - 是否为刷新操作
   */
  getAnimalList: function(isRefresh = false) {
    const { currentPage, pageSize, searchKeyword, animalType } = this.data;
    
    // 如果是刷新，重置页码
    const page = isRefresh ? 1 : currentPage;
    
    // 设置加载状态
    this.setData({ isLoading: true });
    
    // 构建查询参数
    const params = {
      page: page,
      pageSize: pageSize,
      status: '待领养' // 只显示待领养的动物
    };
    
    // 调用服务获取动物列表 - 修改这里，使用正确的函数名
    animalService.getAnimalList(params)
      .then(res => {
        console.log('获取动物列表成功:', res);
        
        // 处理返回数据
        if (res && res.data && Array.isArray(res.data)) {
          // 如果数据结构是直接的动物列表，直接使用
          let animals = res.data;
          
          // 如果数据需要转换，可以在这里处理
          animals = animals.map(item => ({
            id: item.id,
            name: item.name || '未命名',
            type: item.type || '其他',
            breed: item.breed || '混种',
            age: item.age || '未知',
            gender: item.gender || '未知',
            image: item.image || '/assets/images/default-pet.png',
            status: item.status || '待领养',
            location: item.location || this.data.userAddress,
            description: item.description || '暂无描述'
          }));
          
          // 根据筛选条件过滤数据
          const filteredAnimals = this.filterAnimals(animals);
          
          // 更新数据
          this.setData({
            animals: isRefresh ? filteredAnimals : [...this.data.animals, ...filteredAnimals],
            currentPage: page + 1,
            hasMore: filteredAnimals.length >= pageSize, // 简化判断逻辑
            total: filteredAnimals.length,
            isLoading: false
          });
        } else {
          // 无数据时的处理
          this.setData({
            animals: isRefresh ? [] : this.data.animals,
            hasMore: false,
            isLoading: false
          });
        }
      })
      .catch(err => {
        console.error('获取动物列表失败', err);
        
        // 如果是第一次加载失败，使用本地模拟数据
        if (this.data.animals.length === 0) {
          this.loadMockData(isRefresh);
        } else {
          wx.showToast({
            title: err.message || '获取动物列表失败',
            icon: 'none'
          });
          this.setData({ isLoading: false });
        }
      });
  },

  /**
   * 根据品种映射动物类型
   */
  mapBreedToType: function(breed) {
    const dogBreeds = ['金毛', '萨摩耶', '柯基', '泰迪', '哈士奇', '边牧', '拉布拉多'];
    const catBreeds = ['英短', '美短', '橘猫', '蓝猫', '布偶', '波斯猫'];
    
    if (dogBreeds.some(dog => breed.includes(dog))) {
      return '狗狗';
    } else if (catBreeds.some(cat => breed.includes(cat))) {
      return '猫咪';
    } else {
      return '其他';
    }
  },

  /**
   * 根据品种和性别生成宠物名字
   */
  generatePetName: function(breed, gender) {
    const maleNames = ['小白', '旺财', '大黄', '豆豆', '球球', '毛毛'];
    const femaleNames = ['咪咪', '花花', '美美', '甜甜', '妞妞', '可可'];
    
    const names = gender === '雄' ? maleNames : femaleNames;
    return names[Math.floor(Math.random() * names.length)];
  },

  /**
   * 根据出生日期计算年龄
   */
  calculateAge: function(birthDate) {
    if (!birthDate) return '未知';
    
    const birth = new Date(birthDate);
    const now = new Date();
    const ageInMonths = (now.getFullYear() - birth.getFullYear()) * 12 + (now.getMonth() - birth.getMonth());
    
    if (ageInMonths < 12) {
      return `${ageInMonths}个月`;
    } else {
      const years = Math.floor(ageInMonths / 12);
      const months = ageInMonths % 12;
      return months > 0 ? `${years}岁${months}个月` : `${years}岁`;
    }
  },

  /**
   * 根据筛选条件过滤动物数据
   */
  filterAnimals: function(animals) {
    const { animalType, searchKeyword } = this.data;
    let filtered = animals;
    
    // 类型筛选
    if (animalType) {
      filtered = filtered.filter(animal => animal.type === animalType);
    }
    
    // 搜索筛选
    if (searchKeyword) {
      const keyword = searchKeyword.toLowerCase();
      filtered = filtered.filter(animal => 
        animal.name.toLowerCase().includes(keyword) || 
        animal.breed.toLowerCase().includes(keyword)
      );
    }
    
    return filtered;
  },

  /**
   * 加载模拟数据（当接口失败时使用）
   */
  loadMockData: function(isRefresh) {
    const mockAnimals = [
      {
        id: 1,
        name: '小白',
        type: '狗狗',
        breed: '萨摩耶',
        age: '2岁',
        gender: '公',
        image: '/assets/images/default-pet.png',
        status: '待领养',
        location: '北京市朝阳区',
        description: '性格温顺，喜欢和人玩耍'
      },
      {
        id: 2,
        name: '咪咪',
        type: '猫咪',
        breed: '英短',
        age: '1岁',
        gender: '母',
        image: '/assets/images/default-pet.png',
        status: '待领养',
        location: '上海市浦东新区',
        description: '安静可爱，适合家庭饲养'
      },
      // ... 其他模拟数据
    ];
    
    // 根据筛选条件过滤模拟数据
    const filteredAnimals = this.filterAnimals(mockAnimals);
    
    this.setData({
      animals: isRefresh ? filteredAnimals : [...this.data.animals, ...filteredAnimals],
      hasMore: false,
      isLoading: false
    });
    
    wx.showToast({
      title: '已切换到离线模式',
      icon: 'none'
    });
  },

  /**
   * 搜索输入事件
   */
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
    
    // 使用防抖处理搜索，避免频繁请求
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
    
    this.searchTimer = setTimeout(() => {
      // 重置页码并刷新列表
      this.setData({ currentPage: 1 });
      this.getAnimalList(true);
    }, 500);
  },

  /**
   * 切换动物类型
   */
  changeAnimalType: function(e) {
    const type = e.currentTarget.dataset.type;
    
    // 显示切换动画
    wx.showLoading({
      title: '加载中',
      mask: true
    });
    
    this.setData({
      animalType: type,
      currentPage: 1
    });
    
    // 重新获取列表
    this.getAnimalList(true);
    
    // 延迟关闭加载动画，避免闪烁
    setTimeout(() => {
      wx.hideLoading();
    }, 300);
  },

  /**
   * 跳转到详情页
   */
  goToDetail: function(e) {
    const { id } = e.currentTarget.dataset;
    
    // 添加轻微振动反馈
    if (wx.vibrateShort) {
      wx.vibrateShort({
        type: 'light'
      });
    }
    
    wx.navigateTo({
      url: `/pages/adopt/detail/detail?id=${id}`
    });
  },

  /**
   * 加载更多
   */
  loadMore: function() {
    if (this.data.hasMore && !this.data.isLoading) {
      // 添加轻微振动反馈
      if (wx.vibrateShort) {
        wx.vibrateShort({
          type: 'light'
        });
      }
      
      this.getAnimalList();
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {
    // 页面渲染完成后，显示一个欢迎提示
    wx.showToast({
      title: '欢迎来到领养页面',
      icon: 'none',
      duration: 1500
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 如果需要，可以在每次页面显示时刷新数据
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
    // 页面隐藏时的逻辑
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
    // 页面卸载时的清理工作
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {
    // 设置刷新状态
    this.setData({ 
      refreshing: true,
      currentPage: 1 
    });
    
    // 重新加载数据
    this.getAnimalList(true);
    
    // 延迟关闭刷新状态，提升体验
    setTimeout(() => {
      wx.stopPullDownRefresh();
      this.setData({ refreshing: false });
      
      // 显示刷新成功提示
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1000
      });
    }, 800);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    // 上拉触底时自动加载更多
    this.loadMore();
  },
  
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {
    return {
      title: '宠物领养 - 给它们一个温暖的家',
      path: '/pages/adopt/adopt',
      imageUrl: '/assets/images/default-pet.png'
    };
  }
})