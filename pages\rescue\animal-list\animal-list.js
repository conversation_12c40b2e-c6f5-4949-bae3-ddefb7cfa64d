// pages/rescue/animal-list/animal-list.js
import rescueService from '../../../services/rescueService'
import userService from '../../../services/userService'

Page({
  data: {
    // 动物列表数据
    animalList: [],
    
    // 分页相关
    currentPage: 1,
    pageSize: 10,
    total: 0,
    hasMore: true,
    
    // 加载状态
    loading: false,
    refreshing: false,
    loadingMore: false,
    
    // 页面状态
    isEmpty: false,
    error: null,
    
    // 筛选相关
    statusFilter: 'all', // all, 待领养, 已领养, 治疗中, 隔离观察
    statusFilterIndex: 0, // 当前选中的筛选器索引
    currentStatusText: '全部状态', // 当前选中的筛选器文本
    statusOptions: [
      { value: 'all', text: '全部状态' },
      { value: '待领养', text: '待领养' },
      { value: '已领养', text: '已领养' },
      { value: '治疗中', text: '治疗中' },
      { value: '隔离观察', text: '隔离观察' }
    ],
    
    // 搜索相关
    searchKeyword: '',
    showSearch: false
  },

  onLoad(options) {
    // 检查用户权限
    if (!this.checkUserPermission()) {
      return;
    }
    
    // 加载动物列表
    this.loadAnimalList(true);
  },

  onShow() {
    // 每次显示页面时检查登录状态
    if (!this.checkUserPermission()) {
      return;
    }
  },

  onPullDownRefresh() {
    this.refreshAnimalList();
  },

  onReachBottom() {
    this.loadMoreAnimals();
  },

  /**
   * 检查用户权限
   */
  checkUserPermission() {
    if (!userService.isLoggedIn()) {
      wx.showModal({
        title: '登录提示',
        content: '请先登录以查看动物信息',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          } else {
            wx.navigateBack();
          }
        }
      });
      return false;
    }

    const userInfo = userService.getCurrentUser();
    if (userInfo && userInfo.userType !== 'rescue_station') {
      wx.showModal({
        title: '权限不足',
        content: '只有救助站用户才能查看动物管理信息',
        confirmText: '确定',
        success: () => {
          wx.navigateBack();
        }
      });
      return false;
    }

    return true;
  },

  /**
   * 加载动物列表
   * @param {boolean} showLoading - 是否显示加载状态
   */
  async loadAnimalList(showLoading = false) {
    if (showLoading) {
      this.setData({ loading: true });
    }

    try {
      const params = {
        page: this.data.currentPage,
        pageSize: this.data.pageSize
      };

      const result = await rescueService.getAnimalsForRescue(params);
      
      if (result && result.code === 200) {
        const animalList = this.processAnimalList(result.data || []);
        
        this.setData({
          animalList: this.data.currentPage === 1 ? animalList : [...this.data.animalList, ...animalList],
          total: result.total || 0,
          hasMore: animalList.length === this.data.pageSize && this.data.animalList.length + animalList.length < (result.total || 0),
          isEmpty: this.data.currentPage === 1 && animalList.length === 0,
          error: null,
          loading: false,
          refreshing: false,
          loadingMore: false
        });
      } else {
        throw new Error(result.message || '获取动物列表失败');
      }
    } catch (error) {
      console.error('加载动物列表失败:', error);
      
      this.setData({
        loading: false,
        refreshing: false,
        loadingMore: false,
        error: error.message || '加载失败，请重试'
      });

      if (this.data.currentPage === 1) {
        this.setData({
          animalList: [],
          isEmpty: true
        });
      }

      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 处理动物列表数据，格式化图片URL
   * @param {Array} animalList - 原始动物列表
   * @returns {Array} 处理后的动物列表
   */
  processAnimalList(animalList) {
    return animalList.map(animal => {
      // 处理图片URL
      let photoUrl = animal.photo;
      if (photoUrl && !photoUrl.startsWith('http')) {
        // 如果是相对路径，拼接完整路径
        photoUrl = `D:/images/${photoUrl}`;
      }

      // 计算年龄（如果有出生日期）
      let ageText = '';
      if (animal.birthDate) {
        const birthDate = new Date(animal.birthDate);
        const now = new Date();
        const ageInMonths = Math.floor((now - birthDate) / (1000 * 60 * 60 * 24 * 30));
        if (ageInMonths < 12) {
          ageText = `${ageInMonths}个月`;
        } else {
          ageText = `${Math.floor(ageInMonths / 12)}岁${ageInMonths % 12 > 0 ? `${ageInMonths % 12}个月` : ''}`;
        }
      }

      return {
        ...animal,
        photo: photoUrl,
        ageText,
        // 添加状态样式类
        statusClass: this.getStatusClass(animal.status)
      };
    });
  },

  /**
   * 获取状态对应的样式类
   * @param {string} status - 动物状态
   * @returns {string} 样式类名
   */
  getStatusClass(status) {
    const statusMap = {
      '待领养': 'status-available',
      '已领养': 'status-adopted',
      '治疗中': 'status-treatment',
      '隔离观察': 'status-quarantine',
      '已安乐死': 'status-euthanized'
    };
    return statusMap[status] || 'status-default';
  },

  /**
   * 刷新动物列表
   */
  async refreshAnimalList() {
    this.setData({
      currentPage: 1,
      refreshing: true,
      hasMore: true
    });
    
    await this.loadAnimalList();
    wx.stopPullDownRefresh();
  },

  /**
   * 加载更多动物
   */
  async loadMoreAnimals() {
    if (!this.data.hasMore || this.data.loadingMore) {
      return;
    }

    this.setData({
      currentPage: this.data.currentPage + 1,
      loadingMore: true
    });

    await this.loadAnimalList();
  },

  /**
   * 重试加载
   */
  retryLoad() {
    this.setData({
      currentPage: 1,
      error: null
    });
    this.loadAnimalList(true);
  },

  /**
   * 查看动物详情
   */
  viewAnimalDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/rescue/animal-detail/animal-detail?id=${id}`
    });
  },

  /**
   * 编辑动物信息
   */
  editAnimal(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/rescue/animal-edit/animal-edit?id=${id}`
    });
  },

  /**
   * 预览动物照片
   */
  previewAnimalPhoto(e) {
    const { photo } = e.currentTarget.dataset;
    if (photo) {
      wx.previewImage({
        urls: [photo],
        current: photo
      });
    }
  },

  /**
   * 切换搜索框显示
   */
  toggleSearch() {
    this.setData({
      showSearch: !this.data.showSearch,
      searchKeyword: ''
    });
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 执行搜索
   */
  performSearch() {
    // TODO: 实现搜索功能（如果后端接口支持）
    wx.showToast({
      title: '搜索功能待实现',
      icon: 'none'
    });
  },

  /**
   * 状态筛选
   */
  onStatusFilterChange(e) {
    const index = parseInt(e.detail.value);
    const selectedStatus = this.data.statusOptions[index];
    
    this.setData({
      statusFilter: selectedStatus.value,
      statusFilterIndex: index,
      currentStatusText: selectedStatus.text
    });
    
    // TODO: 根据状态筛选（如果后端接口支持）
    // 目前先提示功能待实现
    if (selectedStatus.value !== 'all') {
      wx.showToast({
        title: '筛选功能待实现',
        icon: 'none'
      });
    }
  },

  /**
   * 跳转到上传动物页面
   */
  goToUploadAnimal() {
    wx.navigateTo({
      url: '/pages/rescue/animal-manage/animal-manage'
    });
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '动物信息管理 - 救助站',
      path: '/pages/rescue/animal-list/animal-list'
    };
  }
});