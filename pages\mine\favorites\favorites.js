// pages/mine/favorites/favorites.js
import animalService from '../../../services/animalService';
import userService from '../../../services/userService';
import { CONFIG } from '../../../services/config';

Page({
  data: {
    favorites: [],
    loading: true,
    page: 1,
    pageSize: 10,
    hasMore: true
  },

  onLoad() {
    this.checkLoginAndLoad();
  },

  onShow() {
    this.checkLoginAndLoad();
  },

  /**
   * 检查登录状态并加载数据
   */
  checkLoginAndLoad() {
    if (!userService.isLoggedIn()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }, 1500);
      return;
    }

    this.loadFavorites();
  },

  /**
   * 加载收藏列表
   */
  loadFavorites(isRefresh = false) {
    if (isRefresh) {
      this.setData({
        page: 1,
        favorites: [],
        hasMore: true
      });
    }

    this.setData({ loading: true });

    // 由于没有专门的收藏接口，这里模拟获取收藏的动物信息
    // 实际项目中应该有专门的收藏管理接口
    const mockFavorites = [
      {
        id: 1,
        name: '小白',
        type: '狗狗',
        breed: '萨摩耶',
        age: '2岁',
        image: '/assets/images/default-pet.png',
        location: '北京市朝阳区',
        addTime: '2024-03-15 10:30'
      },
      {
        id: 2,
        name: '咪咪',
        type: '猫咪',
        breed: '英国短毛猫',
        age: '1岁',
        image: '/assets/images/default-pet.png',
        location: '上海市浦东新区',
        addTime: '2024-03-12 14:20'
      }
    ];

    // 模拟异步加载
    setTimeout(() => {
      const currentFavorites = isRefresh ? [] : this.data.favorites;
      
      this.setData({
        favorites: [...currentFavorites, ...mockFavorites],
        loading: false,
        page: this.data.page + 1,
        hasMore: false // 模拟没有更多数据
      });
    }, 1000);
  },

  /**
   * 取消收藏
   */
  removeFavorite(e) {
    const id = e.currentTarget.dataset.id;
    const name = e.currentTarget.dataset.name;

    wx.showModal({
      title: '确认取消',
      content: `确定要取消收藏"${name}"吗？`,
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用取消收藏的API
          // 目前模拟移除操作
          const favorites = this.data.favorites.filter(item => item.id !== id);
          this.setData({ favorites });

          wx.showToast({
            title: '已取消收藏',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 查看动物详情
   */
  viewDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/adopt/detail/detail?id=${id}`
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadFavorites(true);
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
 * 联系救助站
 */
contactShelter(e) {
  const id = e.currentTarget.dataset.id;
  wx.showModal({
    title: '联系救助站',
    content: '是否要联系救助站了解领养详情？',
    success: (res) => {
      if (res.confirm) {
        wx.navigateTo({
          url: `/pages/adopt/apply/apply?id=${id}`
        });
      }
    }
  });
},

/**
 * 跳转到领养页面
 */
goToAdopt() {
  wx.switchTab({
    url: '/pages/adopt/adopt'
  });
},
  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadFavorites();
    }
  }
});