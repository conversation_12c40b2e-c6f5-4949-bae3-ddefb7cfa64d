<!--pages/adopt/adopt.wxml-->
<view class="page-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="page-title">宠物领养</view>
    <view class="page-subtitle">给流浪的它们一个温暖的家</view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-container">
    <view class="search-bar">
      <image class="search-icon" src="/assets/images/search.png" mode="aspectFit"></image>
      <input class="search-input" placeholder="搜索宠物名称、品种" bindinput="onSearchInput" value="{{searchKeyword}}"></input>
    </view>
  </view>
  
  <!-- 筛选栏 -->
  <scroll-view scroll-x class="filter-scroll">
    <view class="filter-container">
      <view class="filter-item {{animalType === '' ? 'active' : ''}}" data-type="" bindtap="changeAnimalType">
        全部
      </view>
      <view class="filter-item {{animalType === '猫咪' ? 'active' : ''}}" data-type="猫咪" bindtap="changeAnimalType">
        <text class="filter-icon">🐱</text> 猫咪
      </view>
      <view class="filter-item {{animalType === '狗狗' ? 'active' : ''}}" data-type="狗狗" bindtap="changeAnimalType">
        <text class="filter-icon">🐶</text> 狗狗
      </view>
      <view class="filter-item {{animalType === '其他' ? 'active' : ''}}" data-type="其他" bindtap="changeAnimalType">
        <text class="filter-icon">🐾</text> 其他
      </view>
    </view>
  </scroll-view>
  
  <!-- 数据统计信息 -->
  <view class="stats-info" wx:if="{{total > 0}}">
    <text class="stats-text">共找到 {{total}} 只待领养的小伙伴</text>
  </view>
  
  <!-- 宠物列表 -->
  <view class="pets-list">
    <block wx:if="{{animals.length > 0}}">
      <view class="pet-card" wx:for="{{animals}}" wx:key="id" data-id="{{item.id}}" bindtap="goToDetail">
        <image class="pet-image" src="{{item.image || '/assets/images/default-pet.png'}}" mode="aspectFill" lazy-load="true"></image>
        <view class="pet-info">
          <view class="pet-header">
            <view class="pet-name">{{item.name}}</view>
            <view class="pet-status {{item.status === '待领养' ? 'available' : 'unavailable'}}">{{item.status}}</view>
          </view>
          <view class="pet-desc">{{item.breed}} · {{item.age}} · {{item.gender}}</view>
          <view class="pet-location">
            <image class="location-icon" src="/assets/images/location-pin.png" mode="aspectFit"></image>
            {{item.location || '未知地点'}}
          </view>
          <!-- 医疗信息提示 -->
          <view class="pet-medical" wx:if="{{item.medicalRecord}}">
            <text class="medical-icon">🏥</text>
            <text class="medical-text">{{item.medicalRecord}}</text>
          </view>
        </view>
      </view>
    </block>
    
    <!-- 空状态 -->
    <view class="empty-container" wx:if="{{!isLoading && animals.length === 0}}">
      <image class="empty-image" src="/assets/images/empty.png" mode="aspectFit"></image>
      <view class="empty-text">暂无符合条件的宠物</view>
      <view class="empty-desc">换个筛选条件试试吧</view>
      <button class="retry-btn" bindtap="getAnimalList" data-refresh="{{true}}">重新加载</button>
    </view>
  </view>
  
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <image class="loading-icon" src="/assets/images/loading.gif" mode="aspectFit"></image>
    <view class="loading-text">正在为您寻找小伙伴...</view>
  </view>
  
  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && !isLoading && animals.length > 0}}" bindtap="loadMore">
    <view class="load-more-text">点击加载更多</view>
    <view class="load-more-count">已显示 {{animals.length}}/{{total}}</view>
  </view>
  
  <view class="load-more end" wx:if="{{!hasMore && animals.length > 0}}">
    <view class="divider"></view>
    <view class="end-text">已经到底啦~ 共 {{animals.length}} 只</view>
    <view class="divider"></view>
  </view>
</view>