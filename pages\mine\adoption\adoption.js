// pages/mine/adoption/adoption.js
import adoptService from '../../../services/adoptService';
import userService from '../../../services/userService';
import { CONFIG } from '../../../services/config';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    adoptions: [],
    loading: true,
    page: 1,
    pageSize: 10,
    hasMore: true,
    refreshing: false,
    total: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.checkLoginAndLoad();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    this.checkLoginAndLoad();
  },

  /**
   * 检查登录状态并加载数据
   */
  checkLoginAndLoad: function() {
    if (!userService.isLoggedIn()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }, 1500);
      return;
    }

    this.loadAdoptions();
  },

  /**
   * 加载领养记录
   */
  loadAdoptions: function(isRefresh = false) {
    if (isRefresh) {
      this.setData({
        page: 1,
        adoptions: [],
        hasMore: true,
        refreshing: true
      });
    }

    this.setData({ loading: true });

    const { page, pageSize } = this.data;
    
    // 构建查询参数
    const params = {
      page: isRefresh ? 1 : page,
      pageSize: pageSize
    };

    // 调用领养状态接口
    adoptService.getAdoptStatus(params)
      .then(res => {
        console.log('获取领养状态成功:', res);
        
        if (res && res.code === 200) {
          // 处理返回数据，转换为界面需要的格式
          const newAdoptions = this.transformAdoptionData(res.data || []);
          const currentAdoptions = isRefresh ? [] : this.data.adoptions;
          
          this.setData({
            adoptions: [...currentAdoptions, ...newAdoptions],
            loading: false,
            refreshing: false,
            page: (isRefresh ? 1 : page) + 1,
            total: res.total || 0,
            hasMore: newAdoptions.length >= pageSize && [...currentAdoptions, ...newAdoptions].length < (res.total || 0)
          });
        } else {
          this.handleLoadError(res.message || '获取领养记录失败');
        }
      })
      .catch(err => {
        console.error('加载领养记录失败:', err);
        this.handleLoadError('网络错误，请重试');
      });
  },

  /**
   * 转换接口数据为界面数据格式
   * @param {Array} apiData - 接口返回的数据
   * @returns {Array} 转换后的数据
   */
  transformAdoptionData: function(apiData) {
    return apiData.map(item => {
      // 处理图片URL：如果不是完整URL，则拼接D:/images/
      const processedPhoto = this.processImageUrl(item.photo);
      
      // 转换状态文字
      const statusInfo = this.getStatusInfo(item.matchStatus);
      
      return {
        id: item.ID,
        adoptionNumber: `ADOPT${item.ID.toString().padStart(8, '0')}`, // 生成申请编号
        animalId: item.animalID,
        animalName: this.generateAnimalName(item.breed, item.gender), // 根据品种和性别生成名字
        animalType: this.getAnimalType(item.breed),
        animalBreed: item.breed,
        animalAge: '未知', // 接口没提供年龄，使用默认值
        animalImage: processedPhoto,
        animalGender: item.gender,
        rescueStationName: '救助站', // 接口没提供救助站信息，使用默认值
        rescueStationAddress: '地址信息请联系救助站',
        rescueStationContact: '************',
        applyTime: item.adoptionTime,
        status: statusInfo.status,
        statusText: statusInfo.text,
        userHousing: '住房信息', // 这些用户信息接口没返回，显示默认值
        userHealth: '良好',
        userFreeTime: '充足',
        userContact: wx.getStorageSync('userInfo')?.phone || '未设置',
        adoptionReason: '希望给宠物一个温暖的家',
        rejectReason: item.matchStatus === '拒绝' ? '请联系救助站了解详细原因' : ''
      };
    });
  },

  /**
   * 处理图片URL
   * @param {string} photo - 原始图片路径或URL
   * @returns {string} 处理后的图片URL
   */
  processImageUrl: function(photo) {
    if (!photo) {
      return '/assets/images/default-pet.png';
    }
    
    // 如果是完整URL（以http开头），直接使用
    if (photo.startsWith('http')) {
      return photo;
    }
    
    // 否则拼接D:/images/
    return `D:/images${photo}`;
  },

  /**
   * 根据matchStatus获取状态信息
   * @param {string} matchStatus - 匹配状态
   * @returns {Object} 状态信息
   */
  getStatusInfo: function(matchStatus) {
    const statusMap = {
      '领养成功': { status: 'completed', text: '领养成功' },
      '待审核': { status: 'pending', text: '审核中' },
      '拒绝': { status: 'rejected', text: '审核未通过' },
      '审核通过': { status: 'approved', text: '审核通过' }
    };
    
    return statusMap[matchStatus] || { status: 'pending', text: matchStatus };
  },

  /**
   * 根据品种获取动物类型
   * @param {string} breed - 品种
   * @returns {string} 动物类型
   */
  getAnimalType: function(breed) {
    const dogBreeds = ['金毛', '萨摩耶', '柯基', '泰迪', '哈士奇', '边牧', '拉布拉多', '牧羊犬'];
    const catBreeds = ['英短', '美短', '橘猫', '蓝猫', '布偶', '波斯猫', '短毛猫'];
    
    if (dogBreeds.some(dog => breed.includes(dog))) {
      return '狗狗';
    } else if (catBreeds.some(cat => breed.includes(cat))) {
      return '猫咪';
    } else {
      return '其他';
    }
  },

  /**
   * 根据品种和性别生成宠物名字
   * @param {string} breed - 品种
   * @param {string} gender - 性别
   * @returns {string} 宠物名字
   */
  generateAnimalName: function(breed, gender) {
    const maleNames = ['小白', '旺财', '大黄', '豆豆', '球球', '毛毛'];
    const femaleNames = ['咪咪', '花花', '美美', '甜甜', '妞妞', '可可'];
    
    const names = (gender === '雄' || gender === '公') ? maleNames : femaleNames;
    return names[Math.floor(Math.random() * names.length)];
  },

  /**
   * 处理加载错误
   */
  handleLoadError: function(message) {
    wx.showToast({
      title: message,
      icon: 'none'
    });

    this.setData({
      loading: false,
      refreshing: false
    });
  },

  /**
   * 查看领养详情
   */
  viewDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    const adoption = this.data.adoptions.find(item => item.id === id);
    
    if (adoption) {
      wx.showModal({
        title: '领养详情',
        content: `动物：${adoption.animalName}(${adoption.animalBreed})\n性别：${adoption.animalGender}\n状态：${adoption.statusText}\n申请时间：${adoption.applyTime}${adoption.rejectReason ? '\n拒绝原因：' + adoption.rejectReason : ''}`,
        confirmText: '查看动物',
        cancelText: '关闭',
        success: (res) => {
          if (res.confirm) {
            // 跳转到动物详情页
            wx.navigateTo({
              url: `/pages/adopt/detail/detail?id=${adoption.animalId}`
            });
          }
        }
      });
    }
  },

  /**
   * 取消领养申请
   */
  cancelAdoption: function(e) {
    const id = e.currentTarget.dataset.id;
    const adoption = this.data.adoptions.find(item => item.id === id);
    
    if (!adoption) return;
    
    // 验证ID为正整数（根据API文档要求）
    if (!id || !Number.isInteger(Number(id)) || Number(id) <= 0) {
      wx.showToast({
        title: '无效的记录ID',
        icon: 'none'
      });
      return;
    }
    
    // 只有待审核状态才能取消
    if (adoption.status !== 'pending') {
      wx.showToast({
        title: '当前状态不允许取消',
        icon: 'none'
      });
      return;
    }
  
    wx.showModal({
      title: '确认取消',
      content: `确定要取消对"${adoption.animalName}"的领养申请吗？`,
      success: (res) => {
        if (res.confirm) {
          this.performCancelAdoption(id);
        }
      }
    });
  },

  /**
   * 执行取消领养操作
   */
  performCancelAdoption: function(id) {
    wx.showLoading({
      title: '取消中...',
      mask: true
    });
  
    // 调用删除领养状态接口
    adoptService.deleteAdoptStatus(id)
      .then(res => {
        wx.hideLoading();
        
        // 根据API文档，成功状态码是200
        if (res && res.code === 200) {
          wx.showToast({
            title: res.message || '已取消申请', // 优先使用API返回的message
            icon: 'success'
          });
  
          // 重新加载数据
          this.loadAdoptions(true);
        } else {
          // 处理API返回的错误信息
          wx.showToast({
            title: res.message || '取消失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('取消领养申请失败:', err);
        
        // 根据API文档可能的错误状态
        let errorMessage = '取消失败，请重试';
        if (err.statusCode === 403) {
          errorMessage = '无权限删除此记录';
        } else if (err.statusCode === 404) {
          errorMessage = '记录不存在';
        }
        
        wx.showToast({
          title: errorMessage,
          icon: 'none'
        });
      });
  },

  /**
   * 联系救助站
   */
  contactRescue: function(e) {
    const contact = e.currentTarget.dataset.contact;
    const name = e.currentTarget.dataset.name;
    
    wx.showModal({
      title: '联系救助站',
      content: `${name}\n联系电话：${contact}`,
      confirmText: '拨打电话',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: contact
          });
        }
      }
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {
    this.loadAdoptions(true);
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadAdoptions();
    }
  }
});