// pages/register/register.js
import userService from '../../services/userService';
import { CONFIG } from '../../services/config';
import validate from '../../utils/validate';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 表单字段
    username: '',
    account: '',
    password: '',
    confirmPassword: '',
    address: '',
    userType: '普通用户', // 默认选择普通用户
    
    // 用户类型选项
    userTypes: Object.values(CONFIG.USER_TYPES),
    
    // 状态控制
    isSubmitting: false,
    
    // 错误信息
    errorMessage: '',
    fieldErrors: {
      username: '',
      account: '',
      password: '',
      confirmPassword: '',
      address: ''
    },
    
    // 密码可见性
    passwordVisible: false,
    confirmPasswordVisible: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log('注册页面加载');
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '用户注册'
    });
  },

  /**
   * 用户名输入事件
   */
  usernameInput: function(e) {
    const value = e.detail.value;
    this.setData({
      username: value,
      'fieldErrors.username': ''
    });
    this.clearGlobalError();
  },

  /**
   * 账号输入事件
   */
  accountInput: function(e) {
    const value = e.detail.value;
    this.setData({
      account: value,
      'fieldErrors.account': ''
    });
    this.clearGlobalError();
  },

  /**
   * 密码输入事件
   */
  passwordInput: function(e) {
    const value = e.detail.value;
    this.setData({
      password: value,
      'fieldErrors.password': ''
    });
    this.clearGlobalError();
  },

  /**
   * 确认密码输入事件
   */
  confirmPasswordInput: function(e) {
    const value = e.detail.value;
    this.setData({
      confirmPassword: value,
      'fieldErrors.confirmPassword': ''
    });
    this.clearGlobalError();
  },

  /**
   * 地址输入事件
   */
  addressInput: function(e) {
    const value = e.detail.value;
    this.setData({
      address: value,
      'fieldErrors.address': ''
    });
    this.clearGlobalError();
  },

  /**
   * 用户类型选择事件
   */
  userTypeChange: function(e) {
    const index = e.detail.value;
    this.setData({
      userType: this.data.userTypes[index]
    });
  },

  /**
   * 切换密码可见性
   */
  togglePasswordVisible: function() {
    this.setData({
      passwordVisible: !this.data.passwordVisible
    });
  },

  /**
   * 切换确认密码可见性
   */
  toggleConfirmPasswordVisible: function() {
    this.setData({
      confirmPasswordVisible: !this.data.confirmPasswordVisible
    });
  },

  /**
   * 验证单个字段
   */
  validateField: function(field, value) {
    switch (field) {
      case 'username':
        if (!value || value.trim() === '') {
          return '请输入用户名';
        }
        if (!validate.isValidUsername(value, { minLength: 2, maxLength: 20 })) {
          return '用户名长度应在2-20个字符之间';
        }
        break;
        
      case 'account':
        if (!value || value.trim() === '') {
          return '请输入账号';
        }
        // 修改：只验证长度4-10字符，不验证手机号/邮箱格式
        const trimmedAccount = value.trim();
        if (trimmedAccount.length < 4 || trimmedAccount.length > 10) {
          return '账号长度应在4-10个字符之间';
        }
        break;
        
      case 'password':
        if (!value || value.trim() === '') {
          return '请输入密码';
        }
        if (!validate.validatePassword(value)) {
          return '密码长度应在6-20个字符之间';
        }
        break;
        
      case 'confirmPassword':
        if (!value || value.trim() === '') {
          return '请再次输入密码';
        }
        if (value !== this.data.password) {
          return '两次输入的密码不一致';
        }
        break;
        
      case 'address':
        if (!value || value.trim() === '') {
          return '请输入地址';
        }
        if (value.length < 2) {
          return '地址长度不能少于2个字符';
        }
        break;
    }
    return '';
  },

  /**
   * 验证表单
   */
  validateForm: function() {
    const { username, account, password, confirmPassword, address } = this.data;
    const fieldErrors = {};
    let hasError = false;
    
    // 验证各个字段
    const fields = [
      { name: 'username', value: username },
      { name: 'account', value: account },
      { name: 'password', value: password },
      { name: 'confirmPassword', value: confirmPassword },
      { name: 'address', value: address }
    ];
    
    fields.forEach(field => {
      const error = this.validateField(field.name, field.value);
      fieldErrors[field.name] = error;
      if (error) {
        hasError = true;
      }
    });
    
    // 更新错误状态
    this.setData({
      fieldErrors
    });
    
    return !hasError;
  },

  /**
   * 显示全局错误信息
   */
  showGlobalError: function(message) {
    this.setData({
      errorMessage: message
    });
  },

  /**
   * 清除全局错误信息
   */
  clearGlobalError: function() {
    if (this.data.errorMessage) {
      this.setData({
        errorMessage: ''
      });
    }
  },

  /**
   * 清除所有错误信息
   */
  clearAllErrors: function() {
    this.setData({
      errorMessage: '',
      fieldErrors: {
        username: '',
        account: '',
        password: '',
        confirmPassword: '',
        address: ''
      }
    });
  },

  /**
   * 注册按钮点击事件
   */
  register: function() {
    console.log('开始注册流程');
    
    // 清除之前的错误信息
    this.clearAllErrors();
    
    // 验证表单
    if (!this.validateForm()) {
      wx.showToast({
        title: '请检查输入信息',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 设置正在提交状态
    this.setData({
      isSubmitting: true
    });
    
    // 构建注册数据
    const registerData = {
      username: this.data.username.trim(),
      account: this.data.account.trim(),
      password: this.data.password,
      usertype: this.data.userType,
      address: this.data.address.trim()
    };
    
    console.log('注册数据:', registerData);
    
    // 调用注册接口
    userService.register(registerData)
      .then(res => {
        console.log('注册接口响应:', res);
        
        // 修改：根据接口文档，成功时返回 code: 0
        if (res.code === 0) {
          // 显示注册成功提示
          wx.showToast({
            title: res.message || '注册成功',
            icon: 'success',
            duration: 2000
          });
          
          // 延迟跳转到登录页面
          setTimeout(() => {
            wx.redirectTo({
              url: '/pages/login/login'
            });
          }, 2000);
        } else {
          // 显示错误信息
          const errorMessage = res.message || '注册失败，请稍后重试';
          this.showGlobalError(errorMessage);
          wx.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 2000
          });
        }
      })
      .catch(err => {
        console.error('注册失败:', err);
        
        // 显示错误信息
        const errorMessage = err.message || '注册失败，请检查网络连接';
        this.showGlobalError(errorMessage);
        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 2000
        });
      })
      .finally(() => {
        // 取消提交状态
        this.setData({
          isSubmitting: false
        });
      });
  },

  /**
   * 跳转到登录页面
   */
  goToLogin: function() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  /**
   * 页面分享
   */
  onShareAppMessage: function() {
    return {
      title: '宠物之家 - 注册',
      path: '/pages/register/register'
    };
  }
});