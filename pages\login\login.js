// pages/login/login.js
import userService from '../../services/userService';
import { CONFIG } from '../../services/config';
import validate from '../../utils/validate';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 表单数据
    formData: {
      account: '',
      password: '',
      usertype: '普通用户',
      address: ''
    },
    // 错误信息
    errors: {
      account: '',
      password: '',
      address: ''
    },
    // 是否显示密码
    showPassword: false,
    // 用户类型列表 - 与注册页面保持一致
    userTypes: Object.values(CONFIG.USER_TYPES).map(type => ({ id: type, name: type })),
    // 是否正在登录
    isLoggingIn: false,
    // 是否记住账号
    rememberAccount: false,
    // 当前选中的用户类型索引
    userTypeIndex: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    // 尝试从本地存储获取之前保存的账号信息
    const savedAccount = wx.getStorageSync('savedAccount');
    const savedAddress = wx.getStorageSync('savedAddress');
    const savedUserType = wx.getStorageSync('savedUserType');
    
    if (savedAccount) {
      // 找到对应的用户类型索引
      let userTypeIndex = 0;
      if (savedUserType) {
        const typeIndex = this.data.userTypes.findIndex(type => type.id === savedUserType);
        if (typeIndex !== -1) {
          userTypeIndex = typeIndex;
        }
      }
      
      this.setData({
        'formData.account': savedAccount,
        'formData.address': savedAddress || '',
        'formData.usertype': savedUserType || '普通用户',
        userTypeIndex: userTypeIndex,
        rememberAccount: true
      });
    }
    
    // 如果已经登录，跳转到首页
    const app = getApp();
    if (app.globalData && app.globalData.isLogin) {
      wx.switchTab({
        url: '/pages/home/<USER>'
      });
    }
  },

  /**
   * 输入框内容变化事件处理
   */
  onInput: function(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    // 更新表单数据
    this.setData({
      [`formData.${field}`]: value,
      [`errors.${field}`]: '' // 清除对应字段的错误信息
    });
  },

  /**
   * 切换密码可见性
   */
  togglePasswordVisibility: function() {
    console.log('Toggle password visibility', !this.data.showPassword);
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  /**
   * 选择用户类型
   */
  onUserTypeChange: function(e) {
    const index = parseInt(e.detail.value);
    const userType = this.data.userTypes[index].id;
    
    this.setData({
      'formData.usertype': userType,
      userTypeIndex: index
    });
  },

  /**
   * 切换记住账号
   */
  toggleRememberAccount: function() {
    this.setData({
      rememberAccount: !this.data.rememberAccount
    });
  },

  /**
   * 表单验证
   */
  validateForm: function() {
    const { account, password, address } = this.data.formData;
    let isValid = true;
    const errors = {};

    // 验证账号
    if (!account || account.trim() === '') {
      errors.account = '请输入账号';
      isValid = false;
    } else if (!validate.validateAccount(account)) {
      errors.account = '账号格式不正确，请使用手机号或邮箱';
      isValid = false;
    }

    // 验证密码
    if (!password || password.trim() === '') {
      errors.password = '请输入密码';
      isValid = false;
    } else if (!validate.validatePassword(password)) {
      errors.password = '密码长度应为6-20个字符';
      isValid = false;
    }

    // 验证地址
    if (!address || address.trim() === '') {
      errors.address = '请输入地址';
      isValid = false;
    }

    // 更新错误信息
    this.setData({
      errors
    });

    return isValid;
  },

  /**
   * 提交登录表单
   */
  submitForm: function() {
    // 防止重复提交
    if (this.data.isLoggingIn) {
      return;
    }

    // 表单验证
    if (!this.validateForm()) {
      wx.showToast({
        title: '请检查表单填写',
        icon: 'none'
      });
      return;
    }

    // 更新登录状态
    this.setData({
      isLoggingIn: true
    });

    // 调用登录接口
    userService.login(this.data.formData)
      .then(res => {
        console.log('登录响应数据:', res);
        
        // 根据接口文档，登录成功直接返回用户信息和token
        if (res && res.Jwttoken) {
          // 登录成功处理
          this.handleLoginSuccess(res);
        } else {
          // 处理登录失败的情况
          throw new Error('登录失败，请检查账号密码');
        }
      })
      .catch(err => {
        console.error('登录失败:', err);
        // 处理登录失败
        let errorMessage = '登录失败，请检查账号密码';
        
        // 如果是网络错误或其他具体错误
        if (err.message) {
          errorMessage = err.message;
        } else if (err.data && err.data.message) {
          errorMessage = err.data.message;
        }
        
        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 2000
        });
      })
      .finally(() => {
        // 重置登录状态
        this.setData({
          isLoggingIn: false
        });
      });
  },

  /**
   * 处理登录成功
   */
  handleLoginSuccess: function(userData) {
    console.log('登录成功，用户数据:', userData);
    
    // 保存记住的账号信息
    if (this.data.rememberAccount) {
      wx.setStorageSync('savedAccount', this.data.formData.account);
      wx.setStorageSync('savedAddress', this.data.formData.address);
      wx.setStorageSync('savedUserType', this.data.formData.usertype);
    } else {
      wx.removeStorageSync('savedAccount');
      wx.removeStorageSync('savedAddress');
      wx.removeStorageSync('savedUserType');
    }
    
    // 保存用户信息到本地存储
    const userInfo = {
      username: userData.username,
      account: userData.account,
      address: userData.address,
      usertype: userData.usertype
    };
    
    wx.setStorageSync('userInfo', userInfo);
    
    // 保存 token
    if (userData.Jwttoken) {
      wx.setStorageSync('token', userData.Jwttoken);
    }
    
    // 登录成功后处理 - 更新全局状态
    const app = getApp();
    if (app.doLogin) {
      app.doLogin(userInfo, userData.Jwttoken);
    } else {
      // 如果 app.doLogin 不存在，直接更新全局数据
      if (!app.globalData) {
        app.globalData = {};
      }
      app.globalData.isLogin = true;
      app.globalData.userInfo = userInfo;
      app.globalData.token = userData.Jwttoken;
    }
    
    // 显示登录成功提示
    wx.showToast({
      title: '登录成功',
      icon: 'success',
      duration: 1500
    });
    
    // 延迟跳转，让用户看到成功提示
    setTimeout(() => {
      wx.switchTab({
        url: '/pages/home/<USER>'
      });
    }, 1500);
  },

  /**
   * 跳转到注册页
   */
  goToRegister: function() {
    wx.navigateTo({
      url: '/pages/register/register'
    });
  },

  /**
   * 跳转到忘记密码页（如果有的话）
   */
  goToForgotPassword: function() {
    wx.showToast({
      title: '该功能暂未开放',
      icon: 'none'
    });
    // wx.navigateTo({
    //   url: '/pages/forgot-password/forgot-password'
    // });
  }
});