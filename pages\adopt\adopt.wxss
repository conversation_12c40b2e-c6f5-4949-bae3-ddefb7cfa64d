/* pages/adopt/adopt.wxss */

.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f7f8fa;
  padding: 0 30rpx 120rpx;
}

/* 页面标题 */
.page-header {
  padding: 30rpx 0;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.page-subtitle {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 搜索栏样式 */
.search-container {
  margin-bottom: 24rpx;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 32rpx;
  padding: 16rpx 24rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  height: 64rpx;
  font-size: 28rpx;
}

/* 筛选栏样式 */
.filter-scroll {
  width: 100%;
  white-space: nowrap;
  margin-bottom: 30rpx;
}

.filter-container {
  display: inline-flex;
  padding: 10rpx 0;
}

.filter-item {
  display: inline-flex;
  align-items: center;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 24rpx;
  background-color: #f0f0f0;
  transition: all 0.3s;
}

.filter-item.active {
  color: #fff;
  background-color: #4eaaa8;
  box-shadow: 0 4rpx 8rpx rgba(78, 170, 168, 0.2);
}

.filter-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}

/* 数据统计信息 */
.stats-info {
  padding: 0 30rpx 20rpx;
}

.stats-text {
  font-size: 24rpx;
  color: #999;
}

/* 宠物列表样式 */
.pets-list {
  margin-bottom: 30rpx;
}

.pet-card {
  display: flex;
  padding: 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
}

.pet-card:active {
  transform: scale(0.98);
}

.pet-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.pet-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.pet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.pet-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.pet-status {
  font-size: 22rpx;
  color: #fff;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.pet-status.available {
  background-color: #4eaaa8;
}

.pet-status.unavailable {
  background-color: #999;
}

.pet-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.pet-location {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.location-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

/* 医疗信息样式 */
.pet-medical {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
  padding: 6rpx 12rpx;
  background-color: #f0f9ff;
  border-radius: 12rpx;
  border-left: 3rpx solid #4eaaa8;
}

.medical-icon {
  font-size: 20rpx;
  margin-right: 6rpx;
}

.medical-text {
  font-size: 22rpx;
  color: #666;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 空状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 20rpx;
  opacity: 0.8;
}

.empty-text {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
}

/* 重试按钮 */
.retry-btn {
  margin-top: 20rpx;
  padding: 12rpx 30rpx;
  background-color: #4eaaa8;
  color: #fff;
  font-size: 26rpx;
  border-radius: 24rpx;
  border: none;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #999;
}

/* 加载更多样式 */
.load-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.load-more-text {
  font-size: 26rpx;
  color: #4eaaa8;
  padding: 10rpx 30rpx;
  border: 1rpx solid #4eaaa8;
  border-radius: 30rpx;
}

.load-more-count {
  font-size: 22rpx;
  color: #999;
  margin-top: 10rpx;
}

.load-more.end {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.divider {
  height: 1rpx;
  width: 80rpx;
  background-color: #e0e0e0;
}

.end-text {
  font-size: 24rpx;
  color: #999;
  margin: 0 16rpx;
}