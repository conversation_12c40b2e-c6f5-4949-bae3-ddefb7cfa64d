/**
 * 领养相关接口服务
 */
import request from './request';
import { CONFIG } from './config';

/**
 * 申请领养动物（普通用户）--已检查，严格按照接口文档
 * @param {Object} data - 领养申请数据
 * @param {number} data.animalId - 动物ID (必需)
 * @param {string} data.contact - 联系方式 (必需)
 * @param {string} data.housing - 住房情况 (必需)
 * @param {string} data.job - 工作情况 (必需)
 * @param {string} data.healthCondition - 健康状况 (必需)
 * @param {string} data.freeTime - 空闲时间 (必需)
 * @returns {Promise} 申请结果
 * 接口: POST /users/common/adoptInfo
 * 参数: animalId, contact, housing, job, healthCondition, freeTime (Body), Authorization (Header)
 * 返回: { code: 200, message: string, data: object }
 */
function applyAdopt(data) {
  console.log('🚀 申请领养动物请求:', { ...data, contact: '***' });

  return new Promise((resolve, reject) => {
    try {
      // 获取token
      const token = wx.getStorageSync(CONFIG.STORAGE_KEYS.TOKEN);

      // 参数验证 - 严格按照接口文档要求
      const errors = validateAdoptData(data);
      if (errors.length > 0) {
        reject(new Error(errors[0]));
        return;
      }

      // 构造请求数据，只包含接口文档要求的字段
      const requestData = {
        animalId: data.animalId,
        contact: data.contact,
        housing: data.housing,
        job: data.job,
        healthCondition: data.healthCondition,
        freeTime: data.freeTime
      };

      // 准备请求选项
      const requestOptions = {
        useMock: CONFIG.USE_MOCK,
        showLoading: true
      };

      // 如果有token，添加Authorization头部
      if (token) {
        requestOptions.headers = {
          'Authorization': token
        };
      }

      request.post(CONFIG.API_PATHS.ADOPT_INFO, requestData, requestOptions).then(result => {
        console.log('✅ 申请领养响应:', result);

        // 使用配置的成功状态码
        if (result && result.code === CONFIG.ERROR_CODES.SUCCESS) {
          console.log('🎉 申请领养成功');

          resolve({
            code: CONFIG.ERROR_CODES.SUCCESS,
            message: result.message || '申请提交成功',
            data: result.data || {}
          });
        } else {
          // API返回了错误状态
          const errorMsg = result?.message || '申请提交失败，请稍后重试';
          reject(new Error(errorMsg));
        }
      }).catch(error => {
        console.error('❌ 申请领养失败:', error);
        reject(error);
      });

    } catch (err) {
      console.error('❌ 申请领养参数准备失败:', err);
      reject(new Error('申请提交失败，请检查参数'));
    }
  });
}

/**
 * 查询领养状态（普通用户）--已检查，严格按照接口文档
 * @param {Object} params - 查询参数 (可选)
 * @returns {Promise} 领养状态列表
 * 接口: GET /users/common/adoptstatus
 * 参数: Authorization (Header)
 * 返回: { code: 200, message: string, total: integer, data: [{ID, animalID, breed, photo, gender, matchStatus, adoptionTime}] }
 */
function getAdoptStatus(params = {}) {
  console.log('🚀 查询领养状态请求:', params);

  return new Promise((resolve, reject) => {
    try {
      // 获取token
      const token = wx.getStorageSync(CONFIG.STORAGE_KEYS.TOKEN);

      // 准备请求选项
      const requestOptions = {
        useMock: CONFIG.USE_MOCK,
        showLoading: true
      };

      // 如果有token，添加Authorization头部
      if (token) {
        requestOptions.headers = {
          'Authorization': token
        };
      }

      request.get(CONFIG.API_PATHS.ADOPT_STATUS, params, requestOptions).then(result => {
        console.log('✅ 查询领养状态响应:', result);

        // 使用配置的成功状态码
        if (result && result.code === CONFIG.ERROR_CODES.SUCCESS) {
          console.log('🎉 查询领养状态成功');

          resolve({
            code: CONFIG.ERROR_CODES.SUCCESS,
            message: result.message || '查询成功',
            total: result.total || 0,
            data: result.data || []
          });
        } else {
          // API返回了错误状态
          const errorMsg = result?.message || '查询领养状态失败，请稍后重试';
          reject(new Error(errorMsg));
        }
      }).catch(error => {
        console.error('❌ 查询领养状态失败:', error);
        reject(error);
      });

    } catch (err) {
      console.error('❌ 查询领养状态参数准备失败:', err);
      reject(new Error('查询领养状态失败，请检查参数'));
    }
  });
}

/**
 * 删除领养状态信息（普通用户）--已检查
 * @param {string} adoptId - 领养ID
 * @returns {Promise} 删除结果
 */
function deleteAdoptStatus(adoptId) {
  return request.delete(`${CONFIG.API_PATHS.DELETE_ADOPT_STATUS}/${adoptId}`, {}, { useMock: CONFIG.USE_MOCK });
}

/**
 * 查看领养匹配状态（救助站）--已检查
 * @param {Object} params - 查询参数
 * @returns {Promise} 领养匹配状态列表
 */
function getAdoptStatusForRescue(params = {}) {
  return request.get(CONFIG.API_PATHS.VIEW_ADOPT_STATUS, params, { useMock: CONFIG.USE_MOCK });
}

/**
 * 修改领养匹配状态（救助站）--已检查
 * @param {Object} data - 更新数据
 * @returns {Promise} 更新结果
 */
function updateAdoptStatus(data) {
  return request.put(CONFIG.API_PATHS.UPDATE_ADOPT_STATUS, data, { useMock: CONFIG.USE_MOCK });
}

export default {
  applyAdopt,
  getAdoptStatus,
  deleteAdoptStatus,
  getAdoptStatusForRescue,
  updateAdoptStatus
};

/**
 * 验证申请领养数据
 * @param {Object} data - 申请数据
 * @returns {Array} 错误信息数组
 */
function validateAdoptData(data) {
  const errors = [];

  // 必填字段检查
  if (!data.animalId || typeof data.animalId !== 'number') {
    errors.push('动物ID不能为空且必须为数字');
  }

  if (!data.contact || data.contact.toString().trim() === '') {
    errors.push('联系方式不能为空');
  }

  if (!data.housing || data.housing.trim() === '') {
    errors.push('住房情况不能为空');
  }

  if (!data.job || data.job.trim() === '') {
    errors.push('工作情况不能为空');
  }

  if (!data.healthCondition || data.healthCondition.trim() === '') {
    errors.push('健康状况不能为空');
  }

  if (!data.freeTime || data.freeTime.trim() === '') {
    errors.push('空闲时间不能为空');
  }

  return errors;
}