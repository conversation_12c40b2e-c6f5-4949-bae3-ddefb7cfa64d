import { searchHospitals } from '../../services/searchService';

const params = {
    // 搜索参数
};

searchHospitals(params)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('医院搜索结果:', res.data);
        } else {
            wx.showToast({
                title: res.message || '医院搜索失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('医院搜索出错', err);
        wx.showToast({
            title: '医院搜索出错，请重试',
            icon: 'none'
        });
    });