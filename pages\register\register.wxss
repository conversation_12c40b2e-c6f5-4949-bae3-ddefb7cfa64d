/* pages/register/register.wxss */
.container {
  padding: 40rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  box-sizing: border-box;
}

/* 头部样式 */
.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
  padding-top: 20rpx;
}

.logo {
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.subtitle {
  font-size: 24rpx;
  color: #666;
}

/* 表单容器 */
.form-container {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
}

/* 表单组 */
.form-group {
  margin-bottom: 32rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

/* 标签样式 */
.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.required {
  color: #ff4d4f;
  margin-left: 4rpx;
}

/* 输入框样式 */
.input-wrapper {
  position: relative;
}

.input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fafafa;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.input:focus {
  border-color: #4eaaa8;
  background-color: #fff;
  box-shadow: 0 0 0 4rpx rgba(78, 170, 168, 0.1);
}

.input-error {
  border-color: #ff4d4f !important;
  background-color: #fff6f6 !important;
}

.input-error:focus {
  box-shadow: 0 0 0 4rpx rgba(255, 77, 79, 0.1) !important;
}

/* 密码显示/隐藏图标 */
.eye-icon {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.eye-icon image {
  width: 28rpx;
  height: 28rpx;
  opacity: 0.6;
}

/* 选择器样式 */
.picker {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #fafafa;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.picker:active {
  border-color: #4eaaa8;
  background-color: #fff;
}

.picker-error {
  border-color: #ff4d4f !important;
  background-color: #fff6f6 !important;
}

.picker-arrow {
  width: 24rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.picker-arrow image {
  width: 20rpx;
  height: 20rpx;
  opacity: 0.6;
}

/* 错误信息样式 */
.field-error {
  color: #ff4d4f;
  font-size: 24rpx;
  margin-top: 8rpx;
  padding-left: 8rpx;
  display: flex;
  align-items: center;
}

.field-error::before {
  content: "⚠";
  margin-right: 6rpx;
  font-size: 20rpx;
}

.global-error {
  display: flex;
  align-items: center;
  background-color: #fff2f0;
  border: 1rpx solid #ffccc7;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 24rpx;
}

.error-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.global-error text {
  color: #ff4d4f;
  font-size: 26rpx;
  flex: 1;
  line-height: 1.4;
}

/* 注册按钮样式 */
.btn-register {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #4eaaa8 0%, #3d8b8a 100%);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 48rpx;
  margin-top: 32rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(78, 170, 168, 0.3);
  transition: all 0.3s ease;
  border: none;
  position: relative;
}

.btn-register:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(78, 170, 168, 0.3);
}

.btn-register[disabled],
.btn-submitting {
  background: linear-gradient(135deg, #bdbdbd 0%, #9e9e9e 100%) !important;
  color: rgba(255, 255, 255, 0.8) !important;
  box-shadow: none !important;
  transform: none !important;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 登录链接 */
.login-link {
  text-align: center;
  font-size: 26rpx;
  color: #666;
  margin-top: 20rpx;
}

.link {
  color: #4eaaa8;
  font-weight: 500;
  text-decoration: underline;
}

/* 底部说明 */
.footer {
  padding: 20rpx 0;
}

.tips {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 20rpx;
  border: 1rpx solid rgba(78, 170, 168, 0.1);
}

.tips text {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

.tips text:last-child {
  margin-bottom: 0;
}

.tips text::before {
  content: "•";
  color: #4eaaa8;
  margin-right: 8rpx;
}

/* 响应式适配 */
@media screen and (max-width: 350px) {
  .container {
    padding: 30rpx 20rpx;
  }
  
  .form-container {
    padding: 30rpx 20rpx;
  }
  
  .title {
    font-size: 32rpx;
  }
  
  .input,
  .picker {
    height: 80rpx;
    font-size: 26rpx;
  }
  
  .btn-register {
    height: 88rpx;
    font-size: 30rpx;
  }
  
  .field-error {
    font-size: 22rpx;
  }
}

/* 焦点状态优化 */
.input:focus,
.picker:active {
  outline: none;
}

/* 禁用状态 */
.input:disabled {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

/* 成功状态（可选） */
.input-success {
  border-color: #52c41a;
  background-color: #f6ffed;
}

.input-success:focus {
  box-shadow: 0 0 0 4rpx rgba(82, 196, 26, 0.1);
}