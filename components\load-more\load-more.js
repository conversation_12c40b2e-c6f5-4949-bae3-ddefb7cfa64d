// components/load-more/load-more.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 加载状态：loading(加载中), noMore(没有更多), error(加载失败)
    status: {
      type: String,
      value: 'loading'
    },
    // 加载中的文本
    loadingText: {
      type: String,
      value: '加载中...'
    },
    // 没有更多内容的文本
    noMoreText: {
      type: String,
      value: '没有更多了'
    },
    // 加载失败的文本
    errorText: {
      type: String,
      value: '加载失败，点击重试'
    },
    // 是否显示分割线
    showLine: {
      type: Boolean,
      value: true
    },
    // 是否自动隐藏（当状态为noMore时）
    autoHide: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 点击加载更多
    onClick: function() {
      const { status } = this.properties;
      if (status === 'error') {
        this.triggerEvent('retry');
      }
    }
  }
})