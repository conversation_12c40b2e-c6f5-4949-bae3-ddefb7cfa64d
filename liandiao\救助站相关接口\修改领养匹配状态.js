import { updateAdoptStatus } from '../../services/rescueService';

const updateData = {
    // 需要更新的领养匹配状态字段
};

updateAdoptStatus(updateData)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            wx.showToast({
                title: '修改领养匹配状态成功',
                icon: 'success'
            });
        } else {
            wx.showToast({
                title: res.message || '修改领养匹配状态失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('修改领养匹配状态出错', err);
        wx.showToast({
            title: '修改领养匹配状态出错，请重试',
            icon: 'none'
        });
    });