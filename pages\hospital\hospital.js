// pages/hospital/hospital.js
import hospitalService from '../../services/hospitalService';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 医院数据
    hospitals: [],
    // 搜索关键词
    searchKeyword: '',
    // 类型筛选
    serviceType: '',
    // 用户位置
    userAddress: '北京市',
    // 是否正在加载
    isLoading: false,
    // 是否有更多数据
    hasMore: true,
    // 当前页码
    currentPage: 1,
    // 每页数据条数
    pageSize: 10,
    // 总数据条数
    totalCount: 0,
    // 下拉刷新状态
    refreshing: false,
    // 服务类型选项
    serviceTypes: ['全部', '常规诊疗', '疫苗接种', '手术治疗', '美容服务', '寄养服务'],
    // 位置获取状态
    locationLoading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 首先获取用户位置，然后获取医院列表
    this.getUserLocation().finally(() => {
      this.getHospitalList(true);
    });
  },

  /**
   * 获取用户位置
   */
  getUserLocation() {
    this.setData({ locationLoading: true });
    
    return new Promise((resolve) => {
      // 尝试从缓存获取位置
      const cachedLocation = wx.getStorageSync('userLocation');
      if (cachedLocation) {
        console.log('📍 使用缓存位置:', cachedLocation);
        this.setData({ 
          userAddress: cachedLocation,
          locationLoading: false 
        });
        resolve(cachedLocation);
        return;
      }
      
      // 获取当前位置
      wx.getLocation({
        type: 'wgs84',
        success: (res) => {
          console.log('📍 获取位置成功:', res);
          // 将坐标转换为地址（这里简化处理，实际项目中需要调用地理编码API）
          this.reverseGeocode(res.latitude, res.longitude)
            .then(address => {
              this.setData({ 
                userAddress: address,
                locationLoading: false 
              });
              // 缓存位置信息
              wx.setStorageSync('userLocation', address);
              resolve(address);
            })
            .catch(() => {
              this.setData({ locationLoading: false });
              resolve(this.data.userAddress);
            });
        },
        fail: (err) => {
          console.warn('📍 获取位置失败:', err);
          this.setData({ locationLoading: false });
          
          // 位置获取失败时的处理
          if (err.errMsg?.includes('auth deny')) {
            wx.showModal({
              title: '位置权限',
              content: '获取位置权限被拒绝，将使用默认位置搜索医院',
              showCancel: false
            });
          }
          
          resolve(this.data.userAddress);
        }
      });
    });
  },

  /**
   * 逆地理编码（坐标转地址）
   * 简化实现，实际项目中应调用腾讯地图、高德地图等API
   */
  reverseGeocode(latitude, longitude) {
    return new Promise((resolve) => {
      // 这里应该调用地理编码API，简化处理返回模拟城市
      const mockCities = ['北京市', '上海市', '广州市', '深圳市', '杭州市', '成都市'];
      const randomCity = mockCities[Math.floor(Math.random() * mockCities.length)];
      
      setTimeout(() => {
        resolve(randomCity);
      }, 500);
    });
  },

  /**
   * 获取医院列表 - 更新为符合API文档的实现
   * @param {boolean} isRefresh - 是否为刷新操作
   */
  getHospitalList(isRefresh = false) {
    const { currentPage, pageSize, userAddress } = this.data;
    
    // 如果是刷新，重置页码
    const page = isRefresh ? 1 : currentPage;
    
    // 设置加载状态
    this.setData({ isLoading: true });
    
    // 构造查询参数 - 符合API文档要求
    const params = {
      page,
      pageSize,
      address: userAddress || '北京市'
    };
    
    console.log('🏥 请求医院列表参数:', params);
    
    // 调用服务获取医院列表
    hospitalService.getHospitalList(params)
      .then(res => {
        console.log('✅ 医院列表接口响应:', res);
        this.handleHospitalListSuccess(res, page, pageSize, isRefresh);
      })
      .catch(err => {
        console.error('❌ 获取医院列表失败:', err);
        this.handleHospitalListError(err, isRefresh);
      });
  },

  /**
   * 处理接口成功响应
   */
  handleHospitalListSuccess(res, page, pageSize, isRefresh) {
    // 检查响应状态
    if (res.code !== 200) {
      console.error('❌ 接口返回错误:', res.message);
      this.handleHospitalListError(new Error(res.message || '接口返回错误'), isRefresh);
      return;
    }
    
    // 处理返回数据
    const hospitals = Array.isArray(res.data) ? res.data : [];
    const totalCount = res.total || 0;
    
    console.log(`📊 获取到 ${hospitals.length} 条医院数据，总数: ${totalCount}`);
    
    // 过滤数据（根据前端筛选条件）
    const filteredHospitals = this.filterHospitalData(hospitals);
    
    // 更新数据
    this.setData({
      hospitals: isRefresh ? filteredHospitals : [...this.data.hospitals, ...filteredHospitals],
      currentPage: page + 1,
      totalCount: totalCount,
      hasMore: (page * pageSize) < totalCount,
      isLoading: false
    });
    
    // 如果是刷新操作并成功，显示提示
    if (isRefresh) {
      wx.showToast({
        title: `已更新 ${filteredHospitals.length} 家医院`,
        icon: 'success',
        duration: 1500
      });
    }
  },

  /**
   * 处理接口错误
   */
  handleHospitalListError(error, isRefresh) {
    console.error('🔥 处理医院列表错误:', error);
    
    this.setData({ isLoading: false });
    
    // 显示错误提示
    const errorMessage = error.message || '获取医院信息失败';
    
    if (isRefresh) {
      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000
      });
    } else {
      // 非刷新情况下，可以选择显示更友好的错误处理
      console.log('使用本地数据或显示错误状态');
    }
  },

  /**
   * 根据前端筛选条件过滤医院数据
   */
  filterHospitalData(hospitals) {
    let filtered = [...hospitals];
    
    // 按服务类型筛选
    if (this.data.serviceType && this.data.serviceType !== '全部') {
      filtered = filtered.filter(hospital => 
        hospital.type === this.data.serviceType
      );
    }
    
    // 按关键词搜索
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase();
      filtered = filtered.filter(hospital => 
        hospital.name.toLowerCase().includes(keyword) || 
        hospital.description.toLowerCase().includes(keyword) ||
        hospital.address.toLowerCase().includes(keyword)
      );
    }
    
    return filtered;
  },

  /**
   * 搜索输入事件
   */
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
    
    // 使用防抖处理搜索，避免频繁请求
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
    
    this.searchTimer = setTimeout(() => {
      // 重置页码并刷新列表
      this.setData({ currentPage: 1 });
      this.getHospitalList(true);
    }, 500);
  },

  /**
   * 切换服务类型
   */
  changeServiceType(e) {
    const type = e.currentTarget.dataset.type;
    
    // 显示切换动画
    wx.showLoading({
      title: '筛选中',
      mask: true
    });
    
    this.setData({
      serviceType: type,
      currentPage: 1
    });
    
    // 重新获取列表
    this.getHospitalList(true);
    
    // 延迟关闭加载动画，避免闪烁
    setTimeout(() => {
      wx.hideLoading();
    }, 300);
  },

  /**
   * 切换位置
   */
  changeLocation() {
    wx.showActionSheet({
      itemList: ['重新定位', '北京市', '上海市', '广州市', '深圳市', '杭州市', '成都市'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 重新定位
          this.getUserLocation().then(() => {
            this.getHospitalList(true);
          });
        } else {
          // 选择城市
          const cities = ['北京市', '上海市', '广州市', '深圳市', '杭州市', '成都市'];
          const selectedCity = cities[res.tapIndex - 1];
          
          this.setData({ 
            userAddress: selectedCity,
            currentPage: 1
          });
          
          // 更新缓存
          wx.setStorageSync('userLocation', selectedCity);
          
          // 重新获取列表
          this.getHospitalList(true);
        }
      }
    });
  },

  /**
   * 跳转到详情页
   */
  goToDetail(e) {
    const { id } = e.currentTarget.dataset;
    
    // 添加轻微振动反馈
    if (wx.vibrateShort) {
      wx.vibrateShort({
        type: 'light'
      });
    }
    
    wx.navigateTo({
      url: `/pages/hospital/detail/detail?id=${id}`
    });
  },

  /**
   * 跳转到预约页面
   */
  goToAppointment(e) {
    const { id } = e.currentTarget.dataset;
    
    // 添加轻微振动反馈
    if (wx.vibrateShort) {
      wx.vibrateShort({
        type: 'light'
      });
    }
    
    wx.navigateTo({
      url: `/pages/hospital/appointment/appointment?id=${id}`
    });
  },

  /**
   * 加载更多
   */
  loadMore() {
    if (this.data.hasMore && !this.data.isLoading) {
      console.log(`📄 加载更多数据，当前页: ${this.data.currentPage}`);
      this.getHospitalList();
    } else if (!this.data.hasMore) {
      console.log('📄 已无更多数据');
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 页面渲染完成后，显示一个欢迎提示
    wx.showToast({
      title: '欢迎来到宠物医院',
      icon: 'none',
      duration: 1500
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('🔄 用户下拉刷新');
    
    // 设置刷新状态
    this.setData({ 
      refreshing: true,
      currentPage: 1 
    });
    
    // 重新加载数据
    this.getHospitalList(true);
    
    // 延迟关闭刷新状态，提升体验
    setTimeout(() => {
      wx.stopPullDownRefresh();
      this.setData({ refreshing: false });
    }, 800);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('📄 用户上拉触底');
    // 上拉触底时自动加载更多
    this.loadMore();
  },
  
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: `宠物医院 - ${this.data.userAddress}附近的专业医疗服务`,
      path: '/pages/hospital/hospital',
      imageUrl: '/assets/images/default-pet.png'
    };
  },

  /**
   * 页面显示时的处理
   */
  onShow() {
    // 检查是否需要刷新数据（比如从详情页返回）
    const shouldRefresh = wx.getStorageSync('shouldRefreshHospitalList');
    if (shouldRefresh) {
      wx.removeStorageSync('shouldRefreshHospitalList');
      this.getHospitalList(true);
    }
  },

  /**
   * 页面隐藏时的处理
   */
  onHide() {
    // 清除搜索定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
      this.searchTimer = null;
    }
  },

  /**
   * 页面卸载时的处理
   */
  onUnload() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
      this.searchTimer = null;
    }
  }
})