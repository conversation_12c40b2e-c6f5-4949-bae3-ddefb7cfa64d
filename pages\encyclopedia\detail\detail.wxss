/* pages/encyclopedia/detail/detail.wxss */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
}

/* 加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #ffffff;
  font-size: 28rpx;
}

/* 详情内容 */
.detail-section {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  padding-bottom: 40rpx;
}

/* 头部信息 */
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
  border-radius: 30rpx;
  padding: 40rpx 30rpx;
  margin-top: 20rpx;
}

.breed-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.breed-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.breed-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

.header-icon {
  font-size: 80rpx;
  opacity: 0.8;
}

/* 信息卡片容器 */
.info-cards {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 信息卡片 */
.info-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-radius: 25rpx;
  padding: 0;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.card-icon {
  font-size: 40rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.card-content {
  padding: 30rpx;
}

.card-text {
  font-size: 30rpx;
  line-height: 1.6;
  color: #666666;
  text-indent: 2em;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  height: 90rpx;
  border-radius: 45rpx;
  font-size: 28rpx;
  border: none;
  transition: all 0.2s ease;
}

.action-btn.primary {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.action-btn.primary:active {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(2rpx);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn.secondary:active {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(2rpx);
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-weight: 500;
}

/* 错误状态 */
.error-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  text-align: center;
  padding: 40rpx;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.error-title {
  font-size: 36rpx;
  color: #ffffff;
  font-weight: bold;
  margin-bottom: 15rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.error-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.error-buttons {
  display: flex;
  gap: 20rpx;
  width: 100%;
}

.retry-btn, .back-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  transition: all 0.2s ease;
}

.retry-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
}

.retry-btn:active {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(2rpx);
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.back-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(2rpx);
}