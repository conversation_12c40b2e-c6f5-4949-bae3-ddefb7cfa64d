// pages/rescue/animal-manage/animal-manage.js
import rescueService from '../../../services/rescueService'
import userService from '../../../services/userService'

Page({
  data: {
    // 页面模式：'create' 新增模式，'edit' 编辑模式
    pageMode: 'create',
    animalId: null, // 编辑时的动物ID
    
    // 表单数据
    animalForm: {
      breed: '',
      gender: '男', // 默认选择男
      age: '',
      source: '',
      status: '待领养', // 默认待领养
      photo: '',
      birthDate: '',
      medicalRecord: ''
    },
    
    // 表单验证
    formErrors: {},
    
    // 选择器数据
    genderOptions: ['男', '女'],
    genderIndex: 0,
    
    statusOptions: ['待领养', '已领养', '治疗中', '隔离观察', '已安乐死'],
    statusIndex: 0,
    
    // 上传状态
    uploading: false,
    uploadingPhoto: false,
    
    // 图片相关
    selectedImage: '',
    imagePreview: '',
    
    // 其他状态
    loading: false,
    loadingAnimalInfo: false, // 加载动物信息状态
    
    // 当前日期（用于限制出生日期选择）
    currentDate: ''
  },

  onLoad(options) {
    // 检查用户登录状态和权限
    if (!this.checkUserPermission()) {
      return;
    }
    
    // 设置当前日期
    const today = new Date();
    const currentDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    this.setData({ currentDate });
    
    // 判断是否为编辑模式
    if (options.id) {
      this.setData({
        pageMode: 'edit',
        animalId: parseInt(options.id)
      });
      this.loadAnimalInfo(options.id);
    } else {
      this.setData({
        pageMode: 'create'
      });
    }
  },

  onShow() {
    // 每次显示页面时检查登录状态
    this.checkUserPermission();
  },

  /**
   * 加载动物信息（编辑模式使用）
   */
  async loadAnimalInfo(animalId) {
    this.setData({ loadingAnimalInfo: true });
    
    try {
      // 这里需要调用获取单个动物信息的接口
      const result = await rescueService.getAnimalInfo(animalId);
      
      if (result && result.code === 200 && result.data) {
        const animalData = result.data;
        
        // 计算年龄（如果有出生日期）
        let age = animalData.age || '';
        if (animalData.birthDate && !age) {
          const birthDate = new Date(animalData.birthDate);
          const today = new Date();
          const months = (today.getFullYear() - birthDate.getFullYear()) * 12 + (today.getMonth() - birthDate.getMonth());
          age = Math.max(0, months).toString();
        }
        
        // 设置选择器索引
        const genderIndex = this.data.genderOptions.indexOf(animalData.gender || '男');
        const statusIndex = this.data.statusOptions.indexOf(animalData.status || '待领养');
        
        this.setData({
          animalForm: {
            breed: animalData.breed || '',
            gender: animalData.gender || '男',
            age: age,
            source: animalData.source || '',
            status: animalData.status || '待领养',
            photo: animalData.photo || '',
            birthDate: animalData.birthDate || '',
            medicalRecord: animalData.medicalRecord || ''
          },
          genderIndex: Math.max(0, genderIndex),
          statusIndex: Math.max(0, statusIndex),
          selectedImage: animalData.photo || '',
          imagePreview: animalData.photo || '',
          loadingAnimalInfo: false
        });
        
        // 更新页面标题
        wx.setNavigationBarTitle({
          title: '编辑动物信息'
        });
        
      } else {
        throw new Error(result.message || '获取动物信息失败');
      }
      
    } catch (error) {
      this.setData({ loadingAnimalInfo: false });
      console.error('加载动物信息失败:', error);
      
      wx.showToast({
        title: error.message || '加载动物信息失败',
        icon: 'none'
      });
      
      // 加载失败，返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 检查用户权限
   */
  checkUserPermission() {
    if (!userService.isLoggedIn()) {
      wx.showModal({
        title: '登录提示',
        content: '请先登录以使用动物管理功能',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          } else {
            wx.navigateBack();
          }
        }
      });
      return false;
    }

    // TODO: 这里可以添加检查是否为救助站用户的逻辑
    const userInfo = userService.getCurrentUser();
    if (userInfo && userInfo.userType !== 'rescue_station') {
      wx.showModal({
        title: '权限不足',
        content: '只有救助站用户才能管理动物信息',
        confirmText: '确定',
        success: () => {
          wx.navigateBack();
        }
      });
      return false;
    }

    return true;
  },

  /**
   * 输入框变化处理
   */
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`animalForm.${field}`]: value
    });
    
    // 清除对应字段的错误信息
    if (this.data.formErrors[field]) {
      this.setData({
        [`formErrors.${field}`]: ''
      });
    }
  },

  /**
   * 数字输入处理（年龄）
   */
  onNumberInput(e) {
    const { field } = e.currentTarget.dataset;
    let value = e.detail.value;
    
    // 确保输入的是非负整数
    value = value.replace(/[^\d]/g, '');
    if (value && parseInt(value) < 0) {
      value = '0';
    }
    
    this.setData({
      [`animalForm.${field}`]: value
    });
    
    // 清除错误信息
    if (this.data.formErrors[field]) {
      this.setData({
        [`formErrors.${field}`]: ''
      });
    }
  },

  /**
   * 性别选择
   */
  onGenderChange(e) {
    const index = e.detail.value;
    this.setData({
      genderIndex: index,
      'animalForm.gender': this.data.genderOptions[index]
    });
  },

  /**
   * 状态选择
   */
  onStatusChange(e) {
    const index = e.detail.value;
    this.setData({
      statusIndex: index,
      'animalForm.status': this.data.statusOptions[index]
    });
  },

  /**
   * 出生日期选择
   */
  onBirthDateChange(e) {
    this.setData({
      'animalForm.birthDate': e.detail.value
    });
  },

  /**
   * 选择图片
   */
  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.setData({
          selectedImage: tempFilePath,
          imagePreview: tempFilePath,
          'animalForm.photo': tempFilePath
        });
        
        // 清除图片相关错误
        if (this.data.formErrors.photo) {
          this.setData({
            'formErrors.photo': ''
          });
        }
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 删除已选图片
   */
  removeImage() {
    this.setData({
      selectedImage: '',
      imagePreview: '',
      'animalForm.photo': ''
    });
  },

  /**
   * 预览图片
   */
  previewImage() {
    if (this.data.imagePreview) {
      wx.previewImage({
        urls: [this.data.imagePreview],
        current: this.data.imagePreview
      });
    }
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { animalForm } = this.data;
    const errors = {};

    // 必填字段验证
    if (!animalForm.breed || !animalForm.breed.trim()) {
      errors.breed = '请输入动物品种';
    } else if (animalForm.breed.length > 50) {
      errors.breed = '品种名称不能超过50字符';
    }

    if (!animalForm.gender) {
      errors.gender = '请选择性别';
    }

    if (!animalForm.age || animalForm.age === '') {
      errors.age = '请输入年龄';
    } else {
      const age = parseInt(animalForm.age);
      if (isNaN(age) || age < 0) {
        errors.age = '请输入有效的年龄（非负整数）';
      }
    }

    if (!animalForm.source || !animalForm.source.trim()) {
      errors.source = '请输入动物来源';
    } else if (animalForm.source.length > 100) {
      errors.source = '动物来源不能超过100字符';
    }

    if (!animalForm.status) {
      errors.status = '请选择动物状态';
    }

    if (!animalForm.photo) {
      errors.photo = '请选择动物照片';
    }

    // 可选字段验证
    if (animalForm.medicalRecord && animalForm.medicalRecord.length > 500) {
      errors.medicalRecord = '医疗记录不能超过500字符';
    }

    // 出生日期验证
    if (animalForm.birthDate) {
      const birthDate = new Date(animalForm.birthDate);
      const today = new Date();
      if (birthDate > today) {
        errors.birthDate = '出生日期不能超过今天';
      }
    }

    this.setData({ formErrors: errors });
    return Object.keys(errors).length === 0;
  },

  /**
   * 提交表单
   */
  async submitForm() {
    // 检查权限
    if (!this.checkUserPermission()) {
      return;
    }

    // 表单验证
    if (!this.validateForm()) {
      wx.showToast({
        title: '请检查输入信息',
        icon: 'none'
      });
      return;
    }

    this.setData({ uploading: true });

    try {
      // 准备提交数据
      const submitData = {
        breed: this.data.animalForm.breed.trim(),
        gender: this.data.animalForm.gender,
        age: parseInt(this.data.animalForm.age),
        source: this.data.animalForm.source.trim(),
        status: this.data.animalForm.status,
        photo: this.data.animalForm.photo, // 这里可能需要先上传图片获取URL
        birthDate: this.data.animalForm.birthDate || undefined,
        medicalRecord: this.data.animalForm.medicalRecord.trim() || undefined
      };

      let result;
      
      if (this.data.pageMode === 'edit') {
        // 编辑模式：调用更新接口
        submitData.id = this.data.animalId;
        result = await rescueService.updateAnimalInfo(submitData);
      } else {
        // 新增模式：调用上传接口
        result = await rescueService.uploadAnimalInfo(submitData);
      }

      this.setData({ uploading: false });

      if (result && result.code === 200) {
        wx.showToast({
          title: result.message || (this.data.pageMode === 'edit' ? '修改成功' : '上传成功'),
          icon: 'success'
        });

        // 编辑模式成功后返回，新增模式重置表单
        if (this.data.pageMode === 'edit') {
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          // 重置表单
          this.resetForm();
          
          // 可以跳转到动物列表页面或返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      } else {
        throw new Error(result.message || (this.data.pageMode === 'edit' ? '修改失败' : '上传失败'));
      }

    } catch (error) {
      this.setData({ uploading: false });
      console.error(`${this.data.pageMode === 'edit' ? '修改' : '上传'}动物信息失败:`, error);

      let errorMessage = this.data.pageMode === 'edit' ? '修改失败，请重试' : '上传失败，请重试';
      
      if (error.code === 400) {
        errorMessage = error.message || '参数无效，请检查输入信息';
      } else if (error.code === 403) {
        errorMessage = error.message || '无权限操作，请确认登录状态';
      } else if (error.code === 404 && this.data.pageMode === 'edit') {
        errorMessage = error.message || '动物记录不存在';
      } else if (error.code === 500) {
        errorMessage = error.message || '服务器错误，请稍后重试';
      } else if (error.message) {
        errorMessage = error.message;
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      });
    }
  },

  /**
   * 重置表单
   */
  resetForm() {
    this.setData({
      animalForm: {
        breed: '',
        gender: '男',
        age: '',
        source: '',
        status: '待领养',
        photo: '',
        birthDate: '',
        medicalRecord: ''
      },
      formErrors: {},
      genderIndex: 0,
      statusIndex: 0,
      selectedImage: '',
      imagePreview: ''
    });
  },

  /**
   * 清空表单
   */
  clearForm() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有已输入的信息吗？',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.resetForm();
          wx.showToast({
            title: '已清空',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 返回上一页
   */
  goBack() {
    // 检查是否有未保存的数据
    const hasData = this.data.animalForm.breed || 
                   this.data.animalForm.age || 
                   this.data.animalForm.source || 
                   this.data.animalForm.photo ||
                   this.data.animalForm.medicalRecord;

    if (hasData) {
      wx.showModal({
        title: '确认离开',
        content: '当前有未保存的信息，确定要离开吗？',
        confirmColor: '#ff4444',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          }
        }
      });
    } else {
      wx.navigateBack();
    }
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    const title = this.data.pageMode === 'edit' ? '编辑动物信息' : '上传动物信息';
    return {
      title: `${title} - 救助站管理`,
      path: '/pages/rescue/animal-manage/animal-manage'
    };
  }
});