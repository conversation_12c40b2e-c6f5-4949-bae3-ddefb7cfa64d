<!-- pages/hospital/appointment/appointment.wxml -->
<view class="appointment-container">
  <!-- 加载状态 -->
  <view class="loading-screen" wx:if="{{loading}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载医院信息...</text>
    </view>
  </view>

  <!-- 预约表单 -->
  <view class="appointment-form" wx:elif="{{!appointmentSuccess}}">
    <!-- 顶部医院信息卡片 -->
    <view class="hospital-info-card">
      <view class="hospital-bg">
        <image class="hospital-bg-image" src="{{hospitalInfo.image || '/assets/images/default-pet.png'}}" mode="aspectFill"></image>
        <view class="hospital-bg-overlay"></view>
      </view>
      
      <view class="hospital-details">
        <view class="hospital-main-info">
          <image class="hospital-avatar" src="{{hospitalInfo.image || '/assets/images/default-pet.png'}}" mode="aspectFill"></image>
          <view class="hospital-text">
            <text class="hospital-name">{{hospitalInfo.name}}</text>
            <view class="hospital-rating">
              <image class="rating-star" src="/assets/images/heart-filled.png"></image>
              <text class="rating-text">{{hospitalInfo.rating}}</text>
              <text class="rating-label">评分</text>
            </view>
          </view>
        </view>
        
        <view class="hospital-meta-info">
          <view class="meta-item">
            <image class="meta-icon" src="/assets/images/location-pin.png"></image>
            <text class="meta-text">{{hospitalInfo.address}}</text>
          </view>
          <view class="meta-item">
            <image class="meta-icon" src="/assets/images/cat-hospital.png"></image>
            <text class="meta-text">{{hospitalInfo.businessHours}}</text>
          </view>
          <view class="meta-item">
            <image class="meta-icon" src="/assets/images/heart.png"></image>
            <text class="meta-text">{{hospitalInfo.contact}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 主人信息 -->
      <view class="form-section">
        <view class="section-header">
          <view class="section-icon">👤</view>
          <text class="section-title">主人信息</text>
          <view class="section-line"></view>
        </view>
        
        <view class="form-group">
          <text class="form-label">姓名 <text class="required">*</text></text>
          <view class="input-wrapper">
            <input 
              class="form-input {{errors.ownerName ? 'error' : ''}}" 
              placeholder="请输入您的姓名" 
              value="{{formData.ownerName}}"
              bindinput="handleInputChange"
              data-field="ownerName"
            />
            <view class="input-focus-line"></view>
          </view>
          <text class="error-message" wx:if="{{errors.ownerName}}">{{errors.ownerName}}</text>
        </view>

        <view class="form-group">
          <text class="form-label">联系电话 <text class="required">*</text></text>
          <view class="input-wrapper">
            <input 
              class="form-input {{errors.ownerPhone ? 'error' : ''}}" 
              placeholder="请输入您的手机号" 
              value="{{formData.ownerPhone}}"
              type="number"
              maxlength="11"
              bindinput="handleInputChange"
              data-field="ownerPhone"
            />
            <view class="input-focus-line"></view>
          </view>
          <text class="error-message" wx:if="{{errors.ownerPhone}}">{{errors.ownerPhone}}</text>
        </view>
      </view>

      <!-- 宠物信息 -->
      <view class="form-section">
        <view class="section-header">
          <view class="section-icon">🐾</view>
          <text class="section-title">宠物信息</text>
          <view class="section-line"></view>
        </view>
        
        <view class="form-group">
          <text class="form-label">宠物名称 <text class="required">*</text></text>
          <view class="input-wrapper">
            <input 
              class="form-input {{errors.petName ? 'error' : ''}}" 
              placeholder="请输入宠物名称" 
              value="{{formData.petName}}"
              bindinput="handleInputChange"
              data-field="petName"
            />
            <view class="input-focus-line"></view>
          </view>
          <text class="error-message" wx:if="{{errors.petName}}">{{errors.petName}}</text>
        </view>

        <view class="form-group">
          <text class="form-label">宠物类型 <text class="required">*</text></text>
          <view 
            class="form-picker {{errors.petType ? 'error' : ''}} {{formData.petType ? 'selected' : ''}}"
            bindtap="showPetTypePicker"
          >
            <text class="picker-text">{{formData.petType || '请选择宠物类型'}}</text>
            <image class="picker-arrow" src="/assets/images/arrow-down.png"></image>
          </view>
          <text class="error-message" wx:if="{{errors.petType}}">{{errors.petType}}</text>
        </view>

        <view class="form-row">
          <view class="form-group form-group-half">
            <text class="form-label">宠物年龄</text>
            <view class="input-wrapper">
              <input 
                class="form-input" 
                placeholder="年龄" 
                value="{{formData.petAge}}"
                bindinput="handleInputChange"
                data-field="petAge"
                type="digit"
              />
              <view class="input-focus-line"></view>
            </view>
          </view>
          
          <view class="form-group form-group-half">
            <text class="form-label">宠物性别</text>
            <view 
              class="form-picker {{formData.petGender ? 'selected' : ''}}"
              bindtap="showGenderPicker"
            >
              <text class="picker-text">{{formData.petGender || '性别'}}</text>
              <image class="picker-arrow" src="/assets/images/arrow-down.png"></image>
            </view>
          </view>
        </view>
      </view>

      <!-- 预约信息 -->
      <view class="form-section">
        <view class="section-header">
          <view class="section-icon">📅</view>
          <text class="section-title">预约信息</text>
          <view class="section-line"></view>
        </view>
        
        <view class="form-group">
          <text class="form-label">预约时间 <text class="required">*</text></text>
          <view 
            class="form-picker {{errors.appointmentTime ? 'error' : ''}} {{formData.appointmentTime ? 'selected' : ''}}" 
            bindtap="showDatePickerPopup"
          >
            <text class="picker-text">{{formData.appointmentTime || '请选择预约时间'}}</text>
            <image class="picker-arrow" src="/assets/images/arrow-down.png"></image>
          </view>
          <text class="error-message" wx:if="{{errors.appointmentTime}}">{{errors.appointmentTime}}</text>
        </view>

        <view class="form-group">
          <text class="form-label">症状描述</text>
          <view class="textarea-wrapper">
            <textarea 
              class="form-textarea" 
              placeholder="请简要描述宠物症状（选填）" 
              value="{{formData.symptoms}}"
              bindinput="handleInputChange"
              data-field="symptoms"
              maxlength="300"
            ></textarea>
            <text class="textarea-counter">{{formData.symptoms.length}}/300</text>
          </view>
        </view>

        <view class="form-group">
          <text class="form-label">备注信息</text>
          <view class="textarea-wrapper">
            <textarea 
              class="form-textarea" 
              placeholder="其他需要说明的情况（选填）" 
              value="{{formData.remark}}"
              bindinput="handleInputChange"
              data-field="remark"
              maxlength="200"
            ></textarea>
            <text class="textarea-counter">{{formData.remark.length}}/200</text>
          </view>
        </view>
      </view>

      <!-- 预约须知 -->
      <view class="tips-section">
        <view class="tips-header">
          <view class="tips-icon">💡</view>
          <text class="tips-title">预约须知</text>
        </view>
        <view class="tips-content">
          <view class="tips-item">
            <view class="tips-dot"></view>
            <text>请准时到院，若需取消请提前联系医院</text>
          </view>
          <view class="tips-item">
            <view class="tips-dot"></view>
            <text>预约成功后，医护人员可能会与您电话确认</text>
          </view>
          <view class="tips-item">
            <view class="tips-dot"></view>
            <text>到院后请出示预约信息，以便医护人员核对</text>
          </view>
          <view class="tips-item">
            <view class="tips-dot"></view>
            <text>请确保宠物已做好防护措施，以免交叉感染</text>
          </view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-section">
        <button 
          class="submit-button {{submitting ? 'loading' : ''}}" 
          bindtap="submitForm"
          disabled="{{submitting}}"
        >
          <view class="submit-content">
            <view class="submit-spinner" wx:if="{{submitting}}"></view>
            <text class="submit-text">{{submitting ? '提交中...' : '立即预约'}}</text>
          </view>
        </button>
      </view>
    </view>
  </view>

  <!-- 预约成功页面 -->
  <view class="success-page" wx:elif="{{appointmentSuccess}}">
    <view class="success-header">
      <view class="success-animation">
        <view class="success-circle">
          <image class="success-icon" src="/assets/images/check.png"></image>
        </view>
        <view class="success-particles">
          <view class="particle particle-1"></view>
          <view class="particle particle-2"></view>
          <view class="particle particle-3"></view>
          <view class="particle particle-4"></view>
        </view>
      </view>
      <text class="success-title">预约成功！</text>
      <text class="success-subtitle">我们将尽快与您联系确认</text>
    </view>

    <view class="success-details">
      <view class="detail-card">
        <view class="detail-item">
          <text class="detail-label">预约单号</text>
          <text class="detail-value">{{appointmentNumber}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">预约医院</text>
          <text class="detail-value">{{hospitalInfo.name}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">宠物名称</text>
          <text class="detail-value">{{formData.petName}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">预约时间</text>
          <text class="detail-value highlight">{{formData.appointmentTime}}</text>
        </view>
      </view>
    </view>

    <view class="success-actions">
      <button class="action-button secondary" bindtap="viewMyAppointments">
        <image class="action-icon" src="/assets/images/cat-hospital.png"></image>
        <text>查看我的预约</text>
      </button>
      <button class="action-button primary" bindtap="goHome">
        <image class="action-icon" src="/assets/images/heart-filled.png"></image>
        <text>返回首页</text>
      </button>
    </view>
  </view>

  <!-- 选择器弹窗 -->
  <!-- 宠物类型选择 -->
  <view class="picker-modal" wx:if="{{showPetTypePicker}}">
    <view class="modal-mask" bindtap="closePetTypePicker"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">选择宠物类型</text>
        <image class="modal-close" src="/assets/images/close-circle.png" bindtap="closePetTypePicker"></image>
      </view>
      <view class="option-grid">
        <view 
          class="option-item {{formData.petType === type ? 'selected' : ''}}" 
          wx:for="{{petTypes}}" 
          wx:key="index" 
          wx:for-item="type"
          bindtap="selectPetType"
          data-type="{{type}}"
        >
          <text class="option-text">{{type}}</text>
          <view class="option-check" wx:if="{{formData.petType === type}}">✓</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 性别选择 -->
  <view class="picker-modal" wx:if="{{showGenderPicker}}">
    <view class="modal-mask" bindtap="closeGenderPicker"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">选择宠物性别</text>
        <image class="modal-close" src="/assets/images/close-circle.png" bindtap="closeGenderPicker"></image>
      </view>
      <view class="option-grid">
        <view 
          class="option-item {{formData.petGender === gender ? 'selected' : ''}}" 
          wx:for="{{genderOptions}}" 
          wx:key="index" 
          wx:for-item="gender"
          bindtap="selectGender"
          data-gender="{{gender}}"
        >
          <text class="option-text">{{gender}}</text>
          <view class="option-check" wx:if="{{formData.petGender === gender}}">✓</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 日期选择 -->
  <view class="picker-modal" wx:if="{{showDatePicker}}">
    <view class="modal-mask" bindtap="closeDatePicker"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">选择日期</text>
        <image class="modal-close" src="/assets/images/close-circle.png" bindtap="closeDatePicker"></image>
      </view>
      <picker 
        mode="date" 
        value="{{selectedDate}}" 
        start="{{minDate}}" 
        end="{{maxDate}}" 
        bindchange="confirmDatePicker"
      >
        <view class="date-picker">
          <text class="date-placeholder" wx:if="{{!selectedDate}}">请选择日期</text>
          <text class="date-value" wx:else>{{selectedDate}}</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 时间选择 -->
  <view class="picker-modal" wx:if="{{showTimePicker}}">
    <view class="modal-mask" bindtap="closeTimePicker"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">选择时间</text>
        <image class="modal-close" src="/assets/images/close-circle.png" bindtap="closeTimePicker"></image>
      </view>
      <view class="time-grid">
        <view 
          class="time-item {{selectedTime === time ? 'selected' : ''}}" 
          wx:for="{{timeOptions}}" 
          wx:key="index" 
          wx:for-item="time"
          bindtap="selectTime"
          data-time="{{time}}"
        >
          <text class="time-text">{{time}}</text>
        </view>
      </view>
    </view>
  </view>
</view>