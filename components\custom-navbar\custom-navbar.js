// components/custom-navbar/custom-navbar.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 标题
    title: {
      type: String,
      value: ''
    },
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      value: true
    },
    // 背景色
    backgroundColor: {
      type: String,
      value: '#4eaaa8'
    },
    // 标题颜色
    titleColor: {
      type: String,
      value: '#ffffff'
    },
    // 是否固定在顶部
    fixed: {
      type: Boolean,
      value: true
    },
    // 胶囊按钮颜色，可选值为 white 或 black
    capsuleColor: {
      type: String,
      value: 'white'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    statusBarHeight: 0,
    navBarHeight: 0,
    capsulePosition: null,
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached: function() {
      this.setNavBarInfo();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 设置导航栏信息
    setNavBarInfo: function() {
      const app = getApp();
      const systemInfo = app.globalData.systemInfo || wx.getSystemInfoSync();
      const statusBarHeight = systemInfo.statusBarHeight;
      
      // 获取胶囊按钮位置信息
      const capsulePosition = wx.getMenuButtonBoundingClientRect();
      
      // 计算导航栏高度
      const navBarHeight = (capsulePosition.top - statusBarHeight) * 2 + capsulePosition.height;
      
      this.setData({
        statusBarHeight,
        navBarHeight,
        capsulePosition
      });
    },
    
    // 返回上一页
    navigateBack: function() {
      wx.navigateBack({
        delta: 1,
        fail: () => {
          // 如果返回失败，则跳转到首页
          wx.switchTab({
            url: '/pages/home/<USER>'
          });
        }
      });
    }
  }
})