/* components/load-more/load-more.wxss */

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 30rpx;
}

.load-more.hide {
  display: none;
}

.load-more-line {
  flex: 1;
  height: 1rpx;
  background-color: #eee;
}

.load-more-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
}

.loading-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
}

.loading-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: #999;
  margin: 0 2rpx;
  animation: loading 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.error-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.load-more-text {
  font-size: 24rpx;
  color: #999;
}

.load-more-text.error {
  color: #f56c6c;
}