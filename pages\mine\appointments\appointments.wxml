<!--pages/mine/appointments/appointments.wxml-->
<view class="appointment-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">我的预约</text>
    <text class="page-subtitle">共{{total}}条预约记录</text>
  </view>

  <!-- 预约列表 -->
  <view wx:if="{{appointmentList.length > 0}}" class="appointment-list">
    <view 
      wx:for="{{appointmentList}}" 
      wx:key="id" 
      class="appointment-item"
      data-appointment="{{item}}"
      bindtap="onAppointmentTap"
    >
      <!-- 预约卡片 -->
      <view class="appointment-card">
        <!-- 医院信息 -->
        <view class="hospital-info">
          <image 
            class="user-avatar" 
            src="{{item.avatar}}"  
            mode="aspectFill"
            lazy-load="{{true}}"
          />
          <view class="user-details">
            <text class="user-name">{{item.username}}</text>  <!-- ✅ 使用username字段 -->
            <text class="animal-status">动物状态：{{item.animalHealthyStatus}}</text>  <!-- ✅ 显示动物健康状态 -->
            <text class="appointment-id">预约ID：{{item.id}}</text>  <!-- ✅ 显示预约ID -->
          </view>
        </view>

        <!-- 预约状态 -->
        <view class="appointment-status">
          <view class="status-badge status-{{item.animalHealthyStatus === '健康' ? 'healthy' : 'unhealthy'}}">
  {{item.animalHealthyStatus}}  <!-- ✅ 显示动物健康状态 -->
          </view>
        </view>

        <!-- 预约时间 -->
        <view class="appointment-time">
          <text class="time-label">预约时间：</text>
          <text class="time-value">{{item.appointmentTime}}</text>
        </view>

        <!-- 联系方式 -->
        <view class="contact-info" wx:if="{{item.contact}}">
          <text class="contact-label">联系电话：</text>
          <text class="contact-value">{{item.contact}}</text>
        </view>

        <!-- 操作按钮 -->
        <view class="appointment-actions">
          <!-- 修复：使用正确的状态判断，只有待确认状态才能删除 -->
          <button 
            class="cancel-btn"
            size="mini"
            data-appointment="{{item}}"
            catchtap="onCancelAppointment"
          >
            取消预约
          </button>
          <button 
            class="detail-btn"
            size="mini"
            data-appointment="{{item}}"
            catchtap="onAppointmentTap"
          >
            查看详情
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{appointmentList.length === 0 && !loading}}" class="empty-container">
    <empty-view 
      icon="/assets/images/empty-appointment.png"
      title="暂无预约记录"
      subtitle="您还没有任何医疗预约"
      buttonText="立即预约"
      bindbutton="goToHospitalList"
    />
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading && appointmentList.length === 0}}" class="loading-container">
    <view class="loading-item">
      <view class="loading-skeleton hospital-skeleton"></view>
      <view class="loading-skeleton text-skeleton"></view>
      <view class="loading-skeleton time-skeleton"></view>
    </view>
    <view class="loading-item">
      <view class="loading-skeleton hospital-skeleton"></view>
      <view class="loading-skeleton text-skeleton"></view>
      <view class="loading-skeleton time-skeleton"></view>
    </view>
    <view class="loading-item">
      <view class="loading-skeleton hospital-skeleton"></view>
      <view class="loading-skeleton text-skeleton"></view>
      <view class="loading-skeleton time-skeleton"></view>
    </view>
  </view>

  <!-- 加载更多 -->
  <load-more 
    wx:if="{{appointmentList.length > 0}}"
    loading="{{loading}}"
    hasMore="{{hasMore}}"
    loadingText="正在加载更多预约..."
    noMoreText="没有更多预约了"
  />
</view>

<!-- 浮动操作按钮 -->
<view class="fab-container">
  <button class="fab-button" bindtap="goToHospitalList">
    <text class="fab-icon">+</text>
    <text class="fab-text">新预约</text>
  </button>
</view>