// pages/encyclopedia/detail/detail.js
import animalService from '../../../services/animalService'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    breed: '', // 动物品种
    animalInfo: null, // 动物信息
    loading: true, // 加载状态
    errorMessage: '' // 错误信息
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { breed } = options
    if (breed) {
      this.setData({
        breed: decodeURIComponent(breed)
      })
      this.loadAnimalDetail()
    } else {
      this.setData({
        loading: false,
        errorMessage: '缺少动物品种参数'
      })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: this.data.breed ? `${this.data.breed} - 动物百科` : '动物百科详情'
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadAnimalDetail()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: `${this.data.breed} - 动物百科`,
      path: `/pages/encyclopedia/detail/detail?breed=${encodeURIComponent(this.data.breed)}`
    }
  },

  /**
   * 加载动物详情
   */
  async loadAnimalDetail() {
    const { breed } = this.data
    
    if (!breed) {
      return
    }

    this.setData({
      loading: true,
      errorMessage: ''
    })

    try {
      const result = await animalService.getAnimalGuide(breed)
      
      if (result && result.length > 0) {
        // 找到匹配的动物信息
        const animalInfo = result.find(item => item.breed === breed) || result[0]
        this.setData({
          animalInfo: animalInfo
        })
      } else {
        this.setData({
          errorMessage: '未找到相关动物信息'
        })
      }
    } catch (error) {
      console.error('加载动物详情失败:', error)
      this.setData({
        errorMessage: error.message || '加载失败，请重试'
      })
    } finally {
      this.setData({
        loading: false
      })
    }
  },

  /**
   * 重新加载
   */
  onRetryLoad() {
    this.loadAnimalDetail()
  },

  /**
   * 返回搜索页面
   */
  onBackToSearch() {
    wx.navigateBack({
      delta: 1
    })
  },

  /**
   * 搜索其他动物
   */
  onSearchOther() {
    wx.navigateTo({
      url: '/pages/encyclopedia/encyclopedia'
    })
  }
})