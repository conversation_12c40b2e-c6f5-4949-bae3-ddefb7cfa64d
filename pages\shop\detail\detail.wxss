/* pages/shop/detail/detail.wxss */
.container {
  padding: 0;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 选项卡计数 */
.tab-count {
  font-size: 22rpx;
  color: #999;
  margin-left: 8rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

/* 商店头部 */
.shop-header {
  background-color: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.shop-image {
  width: 100%;
  height: 380rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.shop-info {
  padding: 0 10rpx;
}

.shop-name-rating {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.shop-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.shop-rating {
  display: flex;
  align-items: center;
}

.rating-value {
  font-size: 32rpx;
  color: #ff9800;
  margin-right: 10rpx;
}

.rating-star {
  width: 32rpx;
  height: 32rpx;
}

.shop-type {
  display: inline-block;
  font-size: 24rpx;
  color: white;
  background-color: #4CAF50;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
  margin-bottom: 16rpx;
}

.shop-address {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.location-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
}

.shop-address text {
  font-size: 28rpx;
  color: #666;
}

.shop-business-hours {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.business-hours-label {
  color: #999;
}

.shop-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 选项卡 */
.tabs {
  display: flex;
  background-color: white;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 20rpx;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab.active {
  color: #4CAF50;
  font-weight: bold;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #4CAF50;
  border-radius: 3rpx;
}

/* 宠物列表 */
.pet-list {
  padding: 0 20rpx;
}

.pet-card {
  display: flex;
  background-color: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.pet-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.pet-info {
  flex: 1;
}

.pet-name-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.pet-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.pet-price {
  font-size: 30rpx;
  color: #f44336;
  font-weight: bold;
}

.pet-breed, .pet-age, .pet-gender, .pet-stock {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.pet-stock.out-of-stock {
  color: #f44336;
}

.pet-actions {
  margin-top: 16rpx;
  display: flex;
  justify-content: flex-end;
}

.btn-reserve {
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  background-color: #4CAF50;
  color: white;
  border-radius: 30rpx;
  line-height: 1.5;
}

.btn-reserve[disabled] {
  background-color: #ccc;
  color: #fff;
}

/* 商店信息 */
.shop-info-detail {
  background-color: white;
  border-radius: 12rpx;
  margin: 0 20rpx 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.info-item {
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  display: block;
  font-size: 26rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

/* 评价相关 */
.evaluation-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 20rpx 20rpx;
}

.evaluation-title-section {
  display: flex;
  flex-direction: column;
}

.evaluation-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.evaluation-count {
  font-size: 24rpx;
  color: #666;
}

.btn-evaluate {
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  background-color: #4CAF50;
  color: white;
  border-radius: 30rpx;
  line-height: 1.5;
}

.evaluation-list {
  padding: 0 20rpx;
}

.evaluation-item {
  background-color: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.evaluation-user {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.user-info {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.evaluation-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rating-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.rating-stars {
  display: flex;
}

.rating-value {
  font-size: 24rpx;
  color: #666;
}

.star-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 6rpx;
}

.evaluation-content {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  line-height: 1.5;
}

.evaluation-time {
  font-size: 24rpx;
  color: #999;
  text-align: right;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-image {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #ccc;
}

/* 加载更多 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.loading-more text {
  font-size: 26rpx;
  color: #999;
}

/* 没有更多 */
.no-more {
  text-align: center;
  padding: 30rpx 0;
}

.no-more text {
  font-size: 26rpx;
  color: #999;
}

/* ==================== 评价弹窗样式 ==================== */
.evaluation-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.evaluation-modal {
  background-color: white;
  border-radius: 20rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(50rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 弹窗头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

/* 弹窗内容 */
.modal-content {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

/* 商店信息卡片 */
.shop-info-card {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}

.shop-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.shop-name-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 评分区域 */
.rating-section {
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.star-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.star-item {
  width: 60rpx;
  height: 60rpx;
  margin: 0 6rpx;
  transition: all 0.2s ease;
}

.star-item.active {
  transform: scale(1.1);
}

.rating-text {
  text-align: center;
  font-size: 26rpx;
  color: #666;
  display: block;
}

/* 评价内容区域 - 修改为textarea样式 */
.content-section {
  margin-bottom: 20rpx;
}

.content-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  background-color: #fafafa;
  margin-bottom: 10rpx;
  box-sizing: border-box;
}

.content-textarea::placeholder {
  color: #999;
}

.content-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
}

/* 弹窗底部 */
.modal-footer {
  display: flex;
  padding: 20rpx 30rpx 30rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.btn-cancel {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 40rpx;
  background-color: white;
  color: #666;
  font-size: 28rpx;
  line-height: 80rpx;
}

.btn-submit {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  font-size: 28rpx;
  line-height: 80rpx;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.btn-submit[disabled] {
  background: #ccc;
  color: #999;
  box-shadow: none;
}

/* 快捷操作按钮 */
.fab-buttons {
  position: fixed;
  right: 30rpx;
  bottom: 60rpx;
  z-index: 100;
}

.fab-button {
  width: 90rpx;
  height: 90rpx;
  background-color: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  margin-bottom: 20rpx;
}

.fab-button:last-child {
  margin-bottom: 0;
}

.fab-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 评价内容显示区域 */
.content-display {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  background-color: #fafafa;
  margin-bottom: 10rpx;
  box-sizing: border-box;
  position: relative;
  display: flex;
  align-items: flex-start;
}

.content-placeholder {
  color: #999;
  font-size: 28rpx;
  line-height: 1.5;
  flex: 1;
}

.content-text {
  color: #333;
  font-size: 28rpx;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
  flex: 1;
}

.edit-icon {
  font-size: 24rpx;
  color: #4CAF50;
  margin-left: 10rpx;
  flex-shrink: 0;
}

/* 内容输入模态框 */
.content-input-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease;
}

.content-input-container {
  background-color: white;
  border-radius: 20rpx;
  width: 85%;
  max-width: 550rpx;
  max-height: 70vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

/* 输入模态框头部 */
.input-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.input-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.input-close {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  color: #999;
  border: none;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* 输入模态框内容 */
.input-body {
  padding: 30rpx;
  max-height: 50vh;
  overflow-y: auto;
}

.content-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.quick-option {
  padding: 10rpx 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 25rpx;
  background-color: white;
  color: #666;
  font-size: 24rpx;
  line-height: 1.5;
}

.quick-option:active {
  background-color: #4CAF50;
  color: white;
  border-color: #4CAF50;
}

.or-divider {
  text-align: center;
  margin: 25rpx 0;
  position: relative;
}

.or-divider text {
  background-color: white;
  padding: 0 20rpx;
  color: #999;
  font-size: 24rpx;
}

.or-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1rpx;
  background-color: #e0e0e0;
  z-index: -1;
}

.custom-input-area {
  margin-top: 20rpx;
}

.custom-input {
  width: 100%;
  min-height: 150rpx;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  background-color: #fafafa;
  margin-bottom: 10rpx;
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
}

.custom-placeholder {
  color: #999;
  font-size: 28rpx;
  line-height: 1.5;
}

.custom-text {
  color: #333;
  font-size: 28rpx;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
}

.temp-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
}

/* 输入模态框底部 */
.input-footer {
  display: flex;
  padding: 20rpx 30rpx 30rpx;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.btn-input-cancel {
  flex: 1;
  height: 70rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 35rpx;
  background-color: white;
  color: #666;
  font-size: 26rpx;
  line-height: 70rpx;
}

.btn-input-confirm {
  flex: 1;
  height: 70rpx;
  border: none;
  border-radius: 35rpx;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  font-size: 26rpx;
  line-height: 70rpx;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}