<!-- components/card/hospital-card.wxml -->
<view class="hospital-card {{mode}}" bindtap="onTapCard">
  <!-- 紧凑模式 -->
  <block wx:if="{{mode === 'compact'}}">
    <view class="hospital-card-compact">
      <image 
        class="hospital-image-compact" 
        src="{{hospital.imageUrl || defaultImage}}" 
        mode="aspectFill"
        binderror="onImageError"
      ></image>
      <view class="hospital-info-compact">
        <view class="hospital-name-compact text-ellipsis">{{hospital.name || '未命名'}}</view>
        <view class="hospital-category-compact text-ellipsis text-secondary">{{hospital.category || '宠物医院'}}</view>
      </view>
    </view>
  </block>

  <!-- 普通模式 -->
  <block wx:elif="{{mode === 'normal'}}">
    <view class="hospital-card-normal">
      <image 
        class="hospital-image-normal" 
        src="{{hospital.imageUrl || defaultImage}}" 
        mode="aspectFill"
        binderror="onImageError"
      ></image>
      <view class="hospital-info-normal">
        <view class="hospital-name-normal text-ellipsis">{{hospital.name || '未命名'}}</view>
        <view class="hospital-rating-normal">
          <view class="rating-stars">
            <block wx:for="{{5}}" wx:key="index">
              <image 
                class="star-icon" 
                src="{{index < hospital.rating ? '/assets/images/star-filled.png' : '/assets/images/star.png'}}" 
                mode="aspectFit"
              ></image>
            </block>
          </view>
          <view class="rating-value">{{hospital.rating || '0.0'}}</view>
          <view class="rating-count text-secondary">({{hospital.ratingCount || 0}})</view>
        </view>
        <view class="hospital-address-normal text-ellipsis text-secondary">{{hospital.address || '暂无地址'}}</view>
        <view class="hospital-actions-normal">
          <view 
            class="hospital-action-item" 
            catchtap="onTapPhone"
          >
            <image 
              class="action-icon" 
              src="/assets/images/phone.png" 
              mode="aspectFit"
            ></image>
            <text class="action-text">电话</text>
          </view>
          <view 
            class="hospital-action-item" 
            catchtap="onTapNavigation"
          >
            <image 
              class="action-icon" 
              src="/assets/images/location.png" 
              mode="aspectFit"
            ></image>
            <text class="action-text">导航</text>
          </view>
          <view 
            class="hospital-action-item" 
            catchtap="onTapAppointment"
          >
            <image 
              class="action-icon" 
              src="/assets/images/calendar.png" 
              mode="aspectFit"
            ></image>
            <text class="action-text">预约</text>
          </view>
        </view>
      </view>
    </view>
  </block>

  <!-- 详细模式 -->
  <block wx:else>
    <view class="hospital-card-detailed">
      <image 
        class="hospital-image-detailed" 
        src="{{hospital.imageUrl || defaultImage}}" 
        mode="aspectFill"
        binderror="onImageError"
      ></image>
      <view class="hospital-info-detailed">
        <view class="hospital-header-detailed">
          <view class="hospital-name-detailed">{{hospital.name || '未命名'}}</view>
          <view class="hospital-rating-detailed">
            <block wx:for="{{5}}" wx:key="index">
              <image 
                class="star-icon" 
                src="{{index < hospital.rating ? '/assets/images/star-filled.png' : '/assets/images/star.png'}}" 
                mode="aspectFit"
              ></image>
            </block>
            <view class="rating-value">{{hospital.rating || '0.0'}}</view>
          </view>
        </view>
        <view class="hospital-meta-detailed">
          <view class="hospital-meta-item">
            <image class="meta-icon" src="/assets/images/tag.png" mode="aspectFit"></image>
            <text class="meta-text">{{hospital.category || '宠物医院'}}</text>
          </view>
          <view class="hospital-meta-item">
            <image class="meta-icon" src="/assets/images/location-pin.png" mode="aspectFit"></image>
            <text class="meta-text">{{hospital.address || '暂无地址'}}</text>
          </view>
          <view class="hospital-meta-item">
            <image class="meta-icon" src="/assets/images/clock.png" mode="aspectFit"></image>
            <text class="meta-text">{{hospital.hours || '暂无营业时间'}}</text>
          </view>
          <view class="hospital-meta-item">
            <image class="meta-icon" src="/assets/images/phone-alt.png" mode="aspectFit"></image>
            <text class="meta-text">{{hospital.phone || '暂无电话'}}</text>
          </view>
        </view>
        <view class="hospital-description-detailed">
          {{hospital.description || '暂无描述'}}
        </view>
        <view class="hospital-services" wx:if="{{hospital.services && hospital.services.length > 0}}">
          <view class="hospital-services-title">提供服务</view>
          <view class="hospital-services-list">
            <view 
              class="hospital-service-tag"
              wx:for="{{hospital.services}}"
              wx:key="index"
            >{{item}}</view>
          </view>
        </view>
        <view class="hospital-actions-detailed">
          <view 
            class="hospital-action-button primary" 
            catchtap="onTapAppointment"
          >
            <image 
              class="action-icon" 
              src="/assets/images/calendar.png" 
              mode="aspectFit"
            ></image>
            <text class="action-text">预约就诊</text>
          </view>
          <view 
            class="hospital-action-button" 
            catchtap="onTapPhone"
          >
            <image 
              class="action-icon" 
              src="/assets/images/phone.png" 
              mode="aspectFit"
            ></image>
            <text class="action-text">电话咨询</text>
          </view>
        </view>
      </view>
    </view>
  </block>
</view>