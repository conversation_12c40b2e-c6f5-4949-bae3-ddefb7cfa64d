/* components/filter/filter-bar.wxss */

.filter-bar {
  position: relative;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  z-index: 100;
}

/* 筛选项列表 */
.filter-items {
  display: flex;
  height: 80rpx;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.filter-item-text {
  font-size: 28rpx;
  color: #333;
  padding-right: 16rpx;
}

.filter-item-arrow {
  width: 0;
  height: 0;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-top: 8rpx solid #999;
  transition: transform 0.3s;
}

.filter-item.active {
  color: #4eaaa8;
}

.filter-item.active .filter-item-text {
  color: #4eaaa8;
}

.filter-item.active .filter-item-arrow {
  border-top-color: #4eaaa8;
  transform: rotate(180deg);
}

.filter-item.has-value .filter-item-text {
  color: #4eaaa8;
}

/* 筛选下拉面板 */
.filter-dropdown {
  position: absolute;
  left: 0;
  right: 0;
  top: 80rpx;
  z-index: 101;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s;
}

.filter-dropdown.show {
  visibility: visible;
  opacity: 1;
}

.filter-dropdown-mask {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.filter-dropdown-content {
  position: relative;
  background-color: #fff;
  z-index: 2;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 选项列表 */
.filter-options {
  max-height: 600rpx;
}

.filter-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.filter-option.active .filter-option-text {
  color: #4eaaa8;
}

.filter-option-icon {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 1rpx solid #4eaaa8;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-option-icon-checked {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #4eaaa8;
}

/* 多选操作按钮 */
.filter-dropdown-actions {
  display: flex;
  border-top: 1rpx solid #eee;
}

.filter-dropdown-action {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.filter-dropdown-action.reset {
  color: #666;
  background-color: #f5f5f5;
}

.filter-dropdown-action.confirm {
  color: #fff;
  background-color: #4eaaa8;
}

/* 更多筛选面板 */
.more-panel {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 200;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s;
}

.more-panel.show {
  visibility: visible;
  opacity: 1;
}

.more-panel-mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.more-panel-content {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 80%;
  background-color: #fff;
  z-index: 2;
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.3s;
}

.more-panel.show .more-panel-content {
  transform: translateX(0);
}

.more-panel-header {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #eee;
}

.more-panel-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.more-panel-close {
  padding: 10rpx;
}

.close-icon {
  width: 32rpx;
  height: 32rpx;
}

.more-panel-body {
  flex: 1;
  padding: 20rpx 30rpx;
}

.more-filter-group {
  margin-bottom: 30rpx;
}

.more-filter-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.more-filter-options {
  display: flex;
  flex-wrap: wrap;
}

.more-filter-option {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 60rpx;
  padding: 0 20rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  font-size: 26rpx;
  color: #333;
}

.more-filter-option.active {
  background-color: rgba(78, 170, 168, 0.1);
  color: #4eaaa8;
}

.more-panel-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.more-panel-action {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.more-panel-action.reset {
  color: #666;
  background-color: #f5f5f5;
}

.more-panel-action.confirm {
  color: #fff;
  background-color: #4eaaa8;
}