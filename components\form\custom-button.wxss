/* components/form/custom-button.wxss */

.custom-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  line-height: 1;
  transition: all 0.3s;
}

/* 按钮尺寸 */
.custom-button.large {
  height: 88rpx;
  font-size: 32rpx;
}

.custom-button.normal {
  height: 80rpx;
}

.custom-button.small {
  height: 64rpx;
  font-size: 26rpx;
  padding: 0 20rpx;
}

.custom-button.mini {
  height: 56rpx;
  font-size: 22rpx;
  padding: 0 16rpx;
}

/* 块级按钮 */
.custom-button.block {
  display: flex;
  width: 100%;
  border-radius: 44rpx;
}

/* 圆角按钮 */
.custom-button.round {
  border-radius: 100rpx;
}

/* 按钮类型 */
.custom-button.default {
  background-color: #fff;
  color: #333;
  border: 1rpx solid #ddd;
}

.custom-button.primary {
  background-color: #4eaaa8;
  color: #fff;
  border: 1rpx solid #4eaaa8;
}

.custom-button.info {
  background-color: #409eff;
  color: #fff;
  border: 1rpx solid #409eff;
}

.custom-button.success {
  background-color: #67c23a;
  color: #fff;
  border: 1rpx solid #67c23a;
}

.custom-button.warning {
  background-color: #e6a23c;
  color: #fff;
  border: 1rpx solid #e6a23c;
}

.custom-button.danger {
  background-color: #f56c6c;
  color: #fff;
  border: 1rpx solid #f56c6c;
}

/* 朴素按钮 */
.custom-button.plain {
  background-color: transparent;
}

.custom-button.plain.primary {
  color: #4eaaa8;
}

.custom-button.plain.info {
  color: #409eff;
}

.custom-button.plain.success {
  color: #67c23a;
}

.custom-button.plain.warning {
  color: #e6a23c;
}

.custom-button.plain.danger {
  color: #f56c6c;
}

/* 禁用状态 */
.custom-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 按钮内容 */
.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 加载动画 */
.loading {
  display: flex;
  align-items: center;
  margin-right: 10rpx;
}

.loading-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: currentColor;
  margin: 0 2rpx;
  opacity: 0.6;
  animation: loading 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}