/* pages/adopt/apply/apply.wxss */

.container {
  padding: 30rpx;
  padding-bottom: 120rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

/* 表单区域样式 */
.form-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 6rpx;
  height: 26rpx;
  background-color: #4eaaa8;
  border-radius: 3rpx;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 24rpx;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  height: 180rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

/* 选择器样式 */
.picker {
  width: 100%;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.picker.placeholder {
  color: #999;
}

/* 单选框样式 */
.radio-group {
  display: flex;
  align-items: center;
}

.radio {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  font-size: 28rpx;
  color: #333;
}

/* 复选框样式 */
.checkbox-group {
  margin-top: 10rpx;
}

.checkbox {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.checkbox-text {
  font-size: 28rpx;
  color: #333;
  margin-left: 10rpx;
  flex: 1;
}

/* 提交按钮样式 */
.btn-container {
  margin-top: 40rpx;
  padding-bottom: 40rpx;
}

.submit-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #4eaaa8;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
}

/* 禁用按钮样式 */
.submit-btn[disabled] {
  background-color: #cccccc !important;
  color: #ffffff !important;
}