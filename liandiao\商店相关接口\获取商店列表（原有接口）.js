import { getShopList } from '../../services/shopService';

const params = {
    // 查询参数
};

getShopList(params)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('商店列表:', res.data);
        } else {
            wx.showToast({
                title: res.message || '获取商店列表失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('获取商店列表出错', err);
        wx.showToast({
            title: '获取商店列表出错，请重试',
            icon: 'none'
        });
    });