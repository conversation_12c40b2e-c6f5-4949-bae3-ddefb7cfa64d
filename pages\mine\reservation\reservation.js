// pages/mine/reservation/reservation.js
import hospitalService from '../../../services/hospitalService';
import userService from '../../../services/userService';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    reservations: [],
    loading: true,
    page: 1,
    pageSize: 10,
    hasMore: true,
    activeTab: 'medical', // medical: 医疗预约, pet: 宠物预购
    tabs: [
      { key: 'medical', name: '医疗预约' },
      { key: 'pet', name: '宠物预购' }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.checkLoginAndLoad();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.checkLoginAndLoad();
  },

  /**
   * 检查登录状态并加载数据
   */
  checkLoginAndLoad() {
    if (!userService.isLoggedIn()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }, 1500);
      return;
    }

    this.loadReservations();
  },

  /**
   * 切换标签
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab !== this.data.activeTab) {
      this.setData({
        activeTab: tab,
        page: 1,
        reservations: [],
        hasMore: true
      });
      this.loadReservations();
    }
  },

  /**
   * 加载预约列表
   */
  loadReservations(isRefresh = false) {
    if (isRefresh) {
      this.setData({
        page: 1,
        reservations: [],
        hasMore: true
      });
    }

    this.setData({ loading: true });

    const loadPromise = this.data.activeTab === 'medical' 
      ? this.loadMedicalReservations()
      : this.loadPetOrders();

    loadPromise.then(data => {
      const currentReservations = isRefresh ? [] : this.data.reservations;
      
      this.setData({
        reservations: [...currentReservations, ...data],
        loading: false,
        page: this.data.page + 1,
        hasMore: data.length >= this.data.pageSize
      });
    }).catch(error => {
      console.error('加载预约列表失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
    });
  },

  /**
   * 加载医疗预约
   */
  loadMedicalReservations() {
    return hospitalService.getMedicalReserveStatus({
      page: this.data.page,
      pageSize: this.data.pageSize
    }).then(result => {
      return Array.isArray(result.data) ? result.data : [];
    });
  },

  /**
   * 加载宠物预购订单
   */
  loadPetOrders() {
    // 使用shopService获取预购状态
    return import('../../../services/shopService').then(shopService => {
      return shopService.default.getPetOrderStatus({
        page: this.data.page,
        pageSize: this.data.pageSize
      }).then(result => {
        return Array.isArray(result.data) ? result.data : [];
      });
    });
  },

  /**
   * 取消预约
   */
  cancelReservation(e) {
    const id = e.currentTarget.dataset.id;
    const type = e.currentTarget.dataset.type;
    const item = this.data.reservations.find(r => r.id === id);

    const title = type === 'medical' ? '取消医疗预约' : '取消宠物预购';
    const content = `确定要取消"${item.hospitalName || item.shopName}"的预约吗？`;

    wx.showModal({
      title,
      content,
      success: (res) => {
        if (res.confirm) {
          this.performCancel(id, type);
        }
      }
    });
  },

  /**
   * 执行取消操作
   */
  performCancel(id, type) {
    wx.showLoading({
      title: '处理中...',
      mask: true
    });

    const cancelPromise = type === 'medical' 
      ? hospitalService.deleteMedicalReserve(id)
      : import('../../../services/shopService').then(shopService => 
          shopService.default.deletePetOrder(id)
        );

    cancelPromise.then(() => {
      wx.hideLoading();
      wx.showToast({
        title: '取消成功',
        icon: 'success'
      });

      // 从列表中移除
      const reservations = this.data.reservations.filter(item => item.id !== id);
      this.setData({ reservations });
    }).catch(error => {
      wx.hideLoading();
      console.error('取消预约失败:', error);
      wx.showToast({
        title: error.message || '取消失败',
        icon: 'none'
      });
    });
  },

  /**
   * 查看详情
   */
  viewDetail(e) {
    const id = e.currentTarget.dataset.id;
    const type = e.currentTarget.dataset.type;
    
    if (type === 'medical') {
      wx.navigateTo({
        url: `/pages/hospital/detail/detail?id=${id}`
      });
    } else {
      wx.navigateTo({
        url: `/pages/shop/detail/detail?id=${id}`
      });
    }
  },

  /**
   * 联系商家/医院
   */
  contact(e) {
    const phone = e.currentTarget.dataset.phone;
    if (phone) {
      wx.makePhoneCall({
        phoneNumber: phone,
        fail: () => {
          wx.showToast({
            title: '拨号失败',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 获取状态颜色
   */
  getStatusColor(status) {
    const colorMap = {
      'pending': '#FF6F61',
      'confirmed': '#4CAF50',
      'completed': '#2196F3',
      'cancelled': '#999'
    };
    return colorMap[status] || '#999';
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadReservations(true);
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadReservations();
    }
  }
});