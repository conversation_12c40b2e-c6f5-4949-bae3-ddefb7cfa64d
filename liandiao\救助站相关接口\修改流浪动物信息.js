import { updateAnimalInfo } from '../../services/rescueService';

const animalData = {
    id: 1,
    breed: '品种',
    gender: '男',
    status: '待领养',
    // 其他可更新字段
};

updateAnimalInfo(animalData)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            wx.showToast({
                title: '修改动物信息成功',
                icon: 'success'
            });
        } else {
            wx.showToast({
                title: res.message || '修改动物信息失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('修改动物信息出错', err);
        wx.showToast({
            title: '修改动物信息出错，请重试',
            icon: 'none'
        });
    });