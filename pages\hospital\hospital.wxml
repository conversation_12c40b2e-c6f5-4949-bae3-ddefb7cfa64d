<!-- pages/hospital/hospital.wxml -->
<view class="hospital-container">
  <!-- 搜索头部区域 -->
  <view class="search-header">
    <view class="header-decoration"></view>
    <view class="header-decoration-2"></view>
    
    <view class="header-content">
      <text class="header-title">🏥 宠物医院</text>
      <text class="header-subtitle">专业的宠物医疗服务</text>
      
      <!-- 位置选择器 -->
      <view class="location-selector" bindtap="changeLocation">
        <image class="location-icon" src="/assets/images/location-pin.png"></image>
        <text class="location-text">{{userAddress}}</text>
        <view class="location-loading" wx:if="{{locationLoading}}">
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
        </view>
        <image class="location-arrow" src="/assets/images/search.png" wx:else></image>
      </view>
      
      <!-- 搜索框 -->
      <view class="search-wrapper">
        <view class="search-input-container">
          <image class="search-icon" src="/assets/images/search.png"></image>
          <input 
            class="search-input" 
            placeholder="搜索医院名称、地址..." 
            value="{{searchKeyword}}"
            bindinput="onSearchInput"
            confirm-type="search"
          />
          <view class="search-loading" wx:if="{{isLoading}}">
            <view class="loading-dot"></view>
            <view class="loading-dot"></view>
            <view class="loading-dot"></view>
          </view>
        </view>
      </view>

      <!-- 服务类型筛选 -->
      <scroll-view scroll-x="true" class="filter-scroll" show-scrollbar="{{false}}">
        <view class="filter-container">
          <view 
            wx:for="{{serviceTypes}}" 
            wx:key="index" 
            class="filter-chip {{serviceType === item ? 'active' : ''}}" 
            bindtap="changeServiceType" 
            data-type="{{item}}"
          >
            <text class="filter-text">{{item}}</text>
            <view class="filter-indicator" wx:if="{{serviceType === item}}"></view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 医院列表内容 -->
  <view class="content-section">
    <!-- 统计信息 -->
    <view class="stats-info" wx:if="{{hospitals.length > 0 && !isLoading}}">
      <view class="stats-content">
        <text class="stats-title">找到 {{totalCount}} 家医院</text>
        <text class="stats-subtitle">{{userAddress}}附近的专业宠物医疗机构</text>
      </view>
    </view>

    <!-- 医院卡片列表 -->
    <view class="hospital-list" wx:if="{{hospitals.length > 0}}">
      <view 
        wx:for="{{hospitals}}" 
        wx:key="id" 
        class="hospital-card"
        data-index="{{index}}"
      >
        <!-- 医院图片和标签 -->
        <view class="card-image-container" bindtap="goToDetail" data-id="{{item.id}}">
          <image class="hospital-image" src="{{item.image}}" mode="aspectFill"></image>
          
          <!-- 评分标签 -->
          <view class="rating-badge">
            <image class="star-icon" src="/assets/images/heart-filled.png"></image>
            <text class="rating-value">{{item.rating}}</text>
          </view>
          
          <!-- 服务类型标签 -->
          <view class="type-badge">
            <text class="type-text">{{item.type}}</text>
          </view>
          
          <!-- 距离标签 -->
          <view class="distance-badge" wx:if="{{item.distance}}">
            <text class="distance-text">{{item.distance}}</text>
          </view>
          
          <!-- 悬停效果 -->
          <view class="image-overlay">
            <view class="overlay-content">
              <image class="overlay-icon" src="/assets/images/cat-hospital.png"></image>
              <text class="overlay-text">查看详情</text>
            </view>
          </view>
        </view>

        <!-- 医院信息 -->
        <view class="card-content">
          <view class="hospital-header">
            <text class="hospital-name">{{item.name}}</text>
            <view class="hospital-status online">营业中</view>
          </view>

          <view class="hospital-address">
            <image class="location-icon" src="/assets/images/location-pin.png"></image>
            <text class="address-text">{{item.address}}</text>
          </view>

          <view class="hospital-description">
            <text class="desc-text">{{item.description}}</text>
          </view>

          <!-- 医院特色信息 -->
          <view class="hospital-features" wx:if="{{item.features && item.features.length > 0}}">
            <view 
              wx:for="{{item.features}}" 
              wx:for-item="feature" 
              wx:key="index" 
              class="feature-tag"
            >
              <text class="feature-text">{{feature}}</text>
            </view>
          </view>

          <view class="hospital-meta">
            <view class="meta-item">
              <image class="meta-icon" src="/assets/images/cat-hospital.png"></image>
              <text class="meta-text">{{item.businessHours}}</text>
            </view>
            <view class="contact-info">
              <image class="phone-icon" src="/assets/images/heart.png"></image>
              <text class="contact-text">{{item.contact}}</text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="card-actions">
            <button 
              class="action-btn detail-btn" 
              bindtap="goToDetail" 
              data-id="{{item.id}}"
            >
              <image class="btn-icon" src="/assets/images/cat-hospital.png"></image>
              <text>详情</text>
            </button>
            
            <button 
              class="action-btn appointment-btn" 
              bindtap="goToAppointment" 
              data-id="{{item.id}}"
            >
              <image class="btn-icon" src="/assets/images/heart-filled.png"></image>
              <text>立即预约</text>
            </button>
          </view>
        </view>

        <!-- 卡片装饰元素 -->
        <view class="card-decoration">
          <view class="decoration-line"></view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-container" wx:elif="{{!isLoading}}">
      <view class="empty-content">
        <image class="empty-image" src="/assets/images/empty.png"></image>
        <view class="empty-text">
          <text class="empty-title">暂无医院数据</text>
          <text class="empty-subtitle">试试调整搜索条件或切换位置</text>
        </view>
        <view class="empty-actions">
          <button class="refresh-btn" bindtap="onPullDownRefresh">
            <image class="refresh-icon" src="/assets/images/search.png"></image>
            <text>刷新重试</text>
          </button>
          <button class="location-btn" bindtap="changeLocation">
            <image class="location-icon" src="/assets/images/location-pin.png"></image>
            <text>切换位置</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 初始加载状态 -->
    <view class="loading-container" wx:if="{{isLoading && hospitals.length === 0}}">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在获取{{userAddress}}的医院信息...</text>
      </view>
    </view>

    <!-- 加载更多状态 -->
    <view class="load-more-section" wx:if="{{isLoading && hospitals.length > 0}}">
      <view class="load-more-content">
        <view class="loading-dots">
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
          <view class="loading-dot"></view>
        </view>
        <text class="load-more-text">正在加载更多...</text>
      </view>
    </view>

    <!-- 加载完成提示 -->
    <view class="end-section" wx:if="{{!hasMore && !isLoading && hospitals.length > 0}}">
      <view class="end-content">
        <view class="end-line"></view>
        <text class="end-text">已显示全部 {{totalCount}} 家医院</text>
        <view class="end-line"></view>
      </view>
    </view>
  </view>

  <!-- 悬浮刷新按钮 -->
  <view class="fab-container">
    <view class="fab-button" bindtap="onPullDownRefresh">
      <image class="fab-icon" src="/assets/images/search.png"></image>
      <view class="fab-ripple"></view>
    </view>
  </view>
</view>