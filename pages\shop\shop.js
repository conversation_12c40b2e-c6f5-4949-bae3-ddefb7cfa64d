// pages/shop/shop.js - 页面端修复版本
import shopService from '../../services/shopService';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    shops: [],                    // 商店列表数据
    searchAddress: '全国',        // 搜索地址，默认全国
    isLoading: false,             // 加载状态
    hasMore: true,                // 是否还有更多数据
    currentPage: 1,               // 当前页码
    pageSize: 10,                 // 每页数量
    total: 0,                     // 总数量
    
    // 常用地址选择
    commonAddresses: [
      '全国',
      '北京',
      '上海', 
      '广州',
      '深圳',
      '武汉',
      '成都',
      '杭州'
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('🏪 商店列表页面加载');
    // 初始加载商店数据
    this.loadShops();
  },

  /**
   * 加载商店数据
   */
  loadShops(loadMore = false) {
    const { currentPage, pageSize, searchAddress, shops } = this.data;

    console.log('🏪 开始调用shopService.getShops');
    console.log('📋 调用参数:', {
      page: loadMore ? currentPage + 1 : 1,
      pageSize: pageSize,
      address: searchAddress
    });

    // 设置加载状态
    this.setData({ isLoading: true });

    // 计算请求页码
    const targetPage = loadMore ? currentPage + 1 : 1;
    
    // 修复：如果搜索地址是"全国"，传空字符串给服务
    const actualAddress = searchAddress === '全国' ? '' : searchAddress;

    // 调用商店服务接口
    shopService.getShops(targetPage, pageSize, actualAddress)
      .then(res => {
        console.log('📥 shopService.getShops 响应:', res);
        console.log('📊 响应码:', res?.code);
        console.log('📊 响应数据长度:', res?.data?.length);
        
        // 同时支持 20 和 200 作为成功状态码
        const isSuccess = res && (res.code === 200 || res.code === 20 || res.code === '200' || res.code === '20');
        
        if (isSuccess) {
          console.log('✅ 响应成功，处理数据');
          
          if (res.data && Array.isArray(res.data)) {
            console.log('✅ data是数组，长度:', res.data.length);
            
            // 处理商店数据
            const newShops = res.data;
            const processedShops = newShops.map(shop => ({
              id: shop.ID || shop.id || Math.random().toString(36).substr(2, 9),
              name: shop.name || '宠物商店',
              address: shop.address || '地址未知',
              contact: shop.contact || '联系方式未知',
              license: shop.license || '',
              image: shop.image || shop.storePhoto || '/assets/images/default-pet.png',
              description: shop.description || '专业的宠物服务商店'
            }));
            
            console.log('🔄 处理后的商店数据:', processedShops);
            
            this.setData({
              shops: loadMore ? [...shops, ...processedShops] : processedShops,
              hasMore: processedShops.length >= pageSize,
              currentPage: targetPage, 
              total: res.total || processedShops.length,
              isLoading: false
            });
            
            console.log('✅ 页面数据更新完成，商店数量:', processedShops.length);
            
            // 如果没有数据，显示提示
            if (processedShops.length === 0) {
              const message = searchAddress === '全国' ? '暂无商店数据' : `${searchAddress}地区暂无商店`;
              wx.showToast({
                title: message,
                icon: 'none',
                duration: 2000
              });
            }
          } else {
            console.error('❌ data不是数组或为空:', res.data);
            this.handleError('数据格式错误');
          }
        } else {
          console.error('❌ 响应码错误，实际为:', res?.code);
          this.handleError('获取数据失败：' + (res?.message || '未知错误'));
        }
      })
      .catch(err => {
        console.error('🔥 shopService.getShops 调用失败:', err);
        this.handleError('网络请求失败: ' + (err.message || '未知错误'));
      });
  },

  /**
   * 搜索输入事件
   */
  onSearchInput(e) {
    const { value } = e.detail;
    this.setData({ searchAddress: value });
    
    // 防抖处理，延迟搜索
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
    
    this.searchTimer = setTimeout(() => {
      this.refreshData();
    }, 500);
  },

  /**
   * 选择常用地址
   */
  selectCommonAddress(e) {
    const { address } = e.currentTarget.dataset;
    console.log('🔍 选择地址:', address);
    this.setData({ searchAddress: address });
    this.refreshData();
  },

  /**
   * 刷新数据
   */
  refreshData() {
    console.log('🔄 刷新数据，搜索地址:', this.data.searchAddress);
    this.setData({
      currentPage: 1,
      shops: [],
      hasMore: true
    });
    this.loadShops();
  },

  /**
   * 跳转到商店详情页
   */
  goToDetail(e) {
    const { id } = e.currentTarget.dataset;
    
    if (!id) {
      wx.showToast({
        title: '商店ID无效',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    console.log('🔗 跳转到商店详情，ID:', id);
    wx.navigateTo({
      url: `/pages/shop/detail/detail?id=${id}`
    });
  },

  /**
   * 拨打电话
   */
  makePhoneCall(e) {
    const { contact } = e.currentTarget.dataset;
    
    if (!contact || contact === '联系方式未知') {
      wx.showToast({
        title: '联系方式无效',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 清理电话号码格式
    const phoneNumber = contact.replace(/[^\d-]/g, '');
    
    wx.makePhoneCall({
      phoneNumber: phoneNumber,
      success: () => {
        console.log('📞 拨打电话成功:', phoneNumber);
      },
      fail: (err) => {
        console.error('📞 拨打电话失败:', err);
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  /**
   * 跳转到宠物管理页面
   */
  goToPetManage() {
    wx.navigateTo({
      url: '/pages/shop/pet-manage/pet-manage'
    });
  },

  /**
   * 处理错误
   */
  handleError(message) {
    console.error('🚨 处理错误:', message);
    
    this.setData({ 
      isLoading: false,
      hasMore: false 
    });
    
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 3000
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('🔄 下拉刷新');
    this.refreshData();
    
    // 延迟停止下拉刷新
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('📄 触底加载更多');
    if (this.data.hasMore && !this.data.isLoading) {
      this.loadShops(true);
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '宠物商店大全',
      path: '/pages/shop/shop',
      imageUrl: '/assets/images/default-pet.png'
    };
  }
});