import { getShops } from '../../services/shopService';

const page = 1;
const pageSize = 10;
const address = '武汉市';

getShops(page, pageSize, address)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('商店列表:', res.data);
        } else {
            wx.showToast({
                title: res.message || '获取商店列表失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('获取商店列表出错', err);
        wx.showToast({
            title: '获取商店列表出错，请重试',
            icon: 'none'
        });
    });