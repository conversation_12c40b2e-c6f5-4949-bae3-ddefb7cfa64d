<!-- pages/register/register.wxml -->
<view class="container">
  <view class="logo-container">
    <image class="logo" src="/assets/images/logo.png" mode="aspectFit"></image>
    <view class="title">宠物领养平台</view>
    <view class="subtitle">欢迎注册，开启您的宠物领养之旅</view>
  </view>
  
  <view class="form-container">
    <view class="form-item">
      <view class="form-label">用户名</view>
      <input 
        class="input" 
        placeholder="请输入用户名" 
        value="{{username}}" 
        bindinput="onInputUsername"
        maxlength="20"
      />
      <view class="form-error" wx:if="{{errors.username}}">{{errors.username}}</view>
    </view>
    
    <view class="form-item">
      <view class="form-label">账号</view>
      <input 
        class="input" 
        placeholder="请输入账号" 
        value="{{account}}" 
        bindinput="onInputAccount"
        maxlength="20"
      />
      <view class="form-error" wx:if="{{errors.account}}">{{errors.account}}</view>
    </view>
    
    <view class="form-item">
      <view class="form-label">密码</view>
      <input 
        class="input" 
        placeholder="请输入密码" 
        password="{{!showPassword}}" 
        value="{{password}}" 
        bindinput="onInputPassword"
        maxlength="20"
      />
      <view class="password-toggle" catchtap="togglePasswordVisibility">
        <image 
          class="password-icon" 
          src="{{showPassword ? '/assets/images/eye-open.png' : '/assets/images/eye-close.png'}}" 
          mode="aspectFit"
        ></image>
      </view>
      <view class="form-error" wx:if="{{errors.password}}">{{errors.password}}</view>
    </view>
    
    <view class="form-item">
      <view class="form-label">确认密码</view>
      <input 
        class="input" 
        placeholder="请再次输入密码" 
        password="{{!showConfirmPassword}}" 
        value="{{confirmPassword}}" 
        bindinput="onInputConfirmPassword"
        maxlength="20"
      />
      <view class="password-toggle" catchtap="toggleConfirmPasswordVisibility">
        <image 
          class="password-icon" 
          src="{{showConfirmPassword ? '/assets/images/eye-open.png' : '/assets/images/eye-close.png'}}" 
          mode="aspectFit"
        ></image>
      </view>
      <view class="form-error" wx:if="{{errors.confirmPassword}}">{{errors.confirmPassword}}</view>
    </view>
    
    <view class="form-item">
      <view class="form-label">用户类型</view>
      <picker 
        class="picker" 
        bindchange="onChangeUserType" 
        value="{{userTypeIndex}}" 
        range="{{userTypes}}"
      >
        <view class="picker-text">{{userTypes[userTypeIndex]}}</view>
      </picker>
      <view class="form-error" wx:if="{{errors.usertype}}">{{errors.usertype}}</view>
    </view>
    
    <view class="form-item">
      <view class="form-label">地址</view>
      <input 
        class="input" 
        placeholder="请输入地址" 
        value="{{address}}" 
        bindinput="onInputAddress"
      />
      <view class="form-error" wx:if="{{errors.address}}">{{errors.address}}</view>
    </view>
    
    <button 
      class="btn btn-primary btn-block margin-top-xl" 
      disabled="{{isSubmitting}}" 
      bindtap="handleRegister"
    >
      {{isSubmitting ? '注册中...' : '注册'}}
    </button>
    
    <view class="text-center margin-top">
      已有账号？<text class="text-primary" bindtap="navigateToLogin">立即登录</text>
    </view>
  </view>
</view>