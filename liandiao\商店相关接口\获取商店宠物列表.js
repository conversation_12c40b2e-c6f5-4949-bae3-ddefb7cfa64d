import { getShopPets } from '../../services/shopService';

const shopId = 1;
const params = {
    page: 1,
    pageSize: 10
};

getShopPets(shopId, params)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('商店宠物列表:', res.data);
        } else {
            wx.showToast({
                title: res.message || '获取商店宠物列表失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('获取商店宠物列表出错', err);
        wx.showToast({
            title: '获取商店宠物列表出错，请重试',
            icon: 'none'
        });
    });