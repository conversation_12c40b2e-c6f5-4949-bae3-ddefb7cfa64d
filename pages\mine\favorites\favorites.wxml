<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading && favorites.length === 0}}" class="loading-container">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 收藏列表 -->
  <view wx:elif="{{favorites.length > 0}}" class="favorites-list">
    <view 
      wx:for="{{favorites}}" 
      wx:key="id"
      class="favorite-item"
      bindtap="viewDetail"
      data-id="{{item.id}}"
    >
      <view class="pet-card">
        <image class="pet-image" src="{{item.image}}" mode="aspectFill" />
        
        <view class="pet-info">
          <view class="pet-header">
            <text class="pet-name">{{item.name}}</text>
            <view class="pet-tags">
              <text class="tag">{{item.type}}</text>
              <text class="tag">{{item.breed}}</text>
            </view>
          </view>
          
          <view class="pet-details">
            <view class="detail-row">
              <text class="detail-label">年龄：</text>
              <text class="detail-value">{{item.age}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">位置：</text>
              <text class="detail-value">{{item.location}}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">收藏时间：</text>
              <text class="detail-value">{{item.addTime}}</text>
            </view>
          </view>
        </view>

        <view class="card-actions">
          <button 
            class="action-btn remove-btn"
            catchtap="removeFavorite"
            data-id="{{item.id}}"
            data-name="{{item.name}}"
          >
            取消收藏
          </button>
          <button 
            class="action-btn contact-btn"
            catchtap="contactShelter"
            data-id="{{item.id}}"
          >
            联系领养
          </button>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{loading}}" class="loading-more">
      <text>加载中...</text>
    </view>
    
    <!-- 没有更多 -->
    <view wx:elif="{{!hasMore}}" class="no-more">
      <text>没有更多了</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:else class="empty-container">
    <image class="empty-image" src="/assets/images/empty.png" mode="aspectFit" />
    <text class="empty-text">还没有收藏任何宠物</text>
    <text class="empty-desc">去看看有哪些可爱的小家伙等待领养吧</text>
    <button class="goto-btn" bindtap="goToAdopt">
      去领养页面
    </button>
  </view>
</view>