/* pages/rescue/animal-manage/animal-manage.wxss */

.container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 40rpx;
}

/* 加载状态 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #FF6F61 0%, #FF8A75 100%);
  padding: 40rpx 30rpx;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  flex: 1;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.page-subtitle {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

.header-actions {
  display: flex;
  gap: 15rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  min-width: 70rpx;
  transition: all 0.2s;
}

.action-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.action-icon {
  font-size: 24rpx;
  margin-bottom: 4rpx;
}

.action-text {
  font-size: 20rpx;
}

/* 表单容器 */
.form-container {
  padding: 20rpx;
}

/* 表单区块 */
.form-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.title-text {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.required-mark {
  color: #ff4444;
  font-size: 28rpx;
  margin-left: 5rpx;
}

.optional-mark {
  color: #999;
  font-size: 24rpx;
  margin-left: 10rpx;
  font-weight: normal;
}

/* 照片上传区域 */
.photo-upload-area {
  margin: 20rpx 0;
}

.photo-upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  background: #f8f9fa;
  transition: all 0.2s;
}

.photo-upload-btn:active {
  background: #e9ecef;
  border-color: #FF6F61;
}

.upload-icon {
  font-size: 60rpx;
  margin-bottom: 15rpx;
  color: #999;
}

.upload-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.upload-hint {
  font-size: 22rpx;
  color: #999;
}

/* 照片预览 */
.photo-preview {
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 300rpx;
  object-fit: cover;
  border-radius: 12rpx;
}

.photo-actions {
  position: absolute;
  top: 15rpx;
  right: 15rpx;
  display: flex;
  gap: 10rpx;
}

.photo-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 8rpx;
  min-width: 60rpx;
}

.photo-action-btn .action-icon {
  font-size: 20rpx;
  margin-bottom: 2rpx;
  color: white;
}

.photo-action-btn .action-text {
  font-size: 18rpx;
  color: white;
}

.delete-btn {
  background: rgba(255, 68, 68, 0.8);
}

.photo-action-btn:active {
  transform: scale(0.95);
}

/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.item-label {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

/* 输入框样式 */
.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  background: #f8f9fa;
  transition: all 0.2s;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #FF6F61;
  background: white;
}

.form-input.error {
  border-color: #ff4444;
  background: #fff5f5;
}

/* 选择器样式 */
.form-picker {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  background: #f8f9fa;
  transition: all 0.2s;
}

.form-picker.error {
  border-color: #ff4444;
  background: #fff5f5;
}

.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 20rpx;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.picker-arrow {
  font-size: 20rpx;
  color: #999;
  transform: rotate(0deg);
  transition: transform 0.2s;
}

/* 文本域样式 */
.form-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  background: #f8f9fa;
  transition: all 0.2s;
  box-sizing: border-box;
}

.form-textarea:focus {
  border-color: #FF6F61;
  background: white;
}

.form-textarea.error {
  border-color: #ff4444;
  background: #fff5f5;
}

/* 字符计数 */
.char-count-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 10rpx;
}

.char-count {
  font-size: 22rpx;
  color: #999;
}

.char-count.char-limit {
  color: #ff4444;
}

/* 错误提示 */
.error-text {
  font-size: 24rpx;
  color: #ff4444;
  margin-top: 8rpx;
  display: flex;
  align-items: center;
}

.error-text::before {
  content: '⚠️';
  margin-right: 5rpx;
}

/* 提交说明 */
.submit-notice {
  background: white;
  border-radius: 16rpx;
  padding: 25rpx;
  margin-bottom: 30rpx;
  border-left: 4rpx solid #FF6F61;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.notice-content {
  display: flex;
  align-items: flex-start;
  gap: 15rpx;
}

.notice-icon {
  font-size: 32rpx;
  margin-top: 5rpx;
}

.notice-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.notice-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.notice-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 操作按钮 */
.form-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
  padding: 0 20rpx;
}

.action-button {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: bold;
  transition: all 0.2s;
  position: relative;
}

.cancel-button {
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e9ecef;
}

.cancel-button:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.submit-button {
  background: linear-gradient(135deg, #FF6F61 0%, #FF8A75 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(255, 111, 97, 0.3);
}

.submit-button:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(255, 111, 97, 0.4);
}

.submit-button.loading {
  background: #cccccc;
  box-shadow: none;
  pointer-events: none;
}

.submit-button.disabled {
  background: #cccccc;
  color: #999;
  box-shadow: none;
  pointer-events: none;
}

.button-text {
  font-size: 30rpx;
  font-weight: bold;
}

/* 提交加载状态 */
.submit-loading {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6F61;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background-color: #f8f9fa;
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .page-header {
    padding: 30rpx 20rpx;
  }
  
  .page-title {
    font-size: 32rpx;
  }
  
  .form-container {
    padding: 15rpx;
  }
  
  .form-section {
    padding: 25rpx 20rpx;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 15rpx;
    padding: 0 15rpx;
  }
  
  .action-button {
    height: 80rpx;
  }
  
  .button-text {
    font-size: 28rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .container {
    background-color: #1a1a1a;
  }
  
  .loading-container {
    background-color: #1a1a1a;
  }
  
  .loading-text {
    color: #ccc;
  }
  
  .form-section {
    background: #2d2d2d;
    color: #fff;
  }
  
  .title-text,
  .label-text {
    color: #fff;
  }
  
  .form-input,
  .form-picker,
  .form-textarea {
    background: #3d3d3d;
    border-color: #555;
    color: #fff;
  }
  
  .form-input:focus,
  .form-picker:focus,
  .form-textarea:focus {
    background: #4d4d4d;
  }
  
  .picker-text {
    color: #fff;
  }
  
  .submit-notice {
    background: #2d2d2d;
    color: #fff;
  }
  
  .notice-title,
  .notice-desc {
    color: #fff;
  }
}