/* pages/rescue/rescue.wxss */

.container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 头部区域 */
.header {
  background: linear-gradient(135deg, #FF6F61 0%, #FF8A75 100%);
  padding: 30rpx 30rpx 40rpx;
  color: white;
}

.title-section {
  margin-bottom: 30rpx;
}

.page-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
  margin-bottom: 8rpx;
}

.location-text {
  display: block;
  font-size: 22rpx;
  opacity: 0.8;
}

/* 搜索区域 */
.search-section {
  display: flex;
  align-items: center;
}

.search-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  font-size: 28rpx;
  margin-left: auto;
}

.search-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

.search-input-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.search-input {
  flex: 1;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 30rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}

.search-actions {
  display: flex;
  gap: 15rpx;
}

.search-confirm,
.search-cancel {
  padding: 10rpx 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  font-size: 24rpx;
}

.search-confirm:active,
.search-cancel:active {
  background: rgba(255, 255, 255, 0.3);
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  padding: 20rpx 30rpx;
  background: white;
  margin-bottom: 20rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  transition: background 0.2s;
}

.action-item:active {
  background: #e9ecef;
}

.action-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.action-text {
  font-size: 24rpx;
  color: #666;
}

/* 骨架屏样式 */
.skeleton-container {
  padding: 0 20rpx;
}

.skeleton-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.skeleton-image {
  width: 100%;
  height: 300rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.skeleton-content {
  padding: 30rpx;
}

.skeleton-title {
  height: 40rpx;
  width: 60%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4rpx;
  margin-bottom: 20rpx;
}

.skeleton-text {
  height: 28rpx;
  width: 100%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4rpx;
  margin-bottom: 15rpx;
}

.skeleton-text.short {
  width: 80%;
}

@keyframes loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6F61;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 救助站列表 */
.stations-list {
  padding: 0 20rpx;
}

.station-item {
  margin-bottom: 20rpx;
}

.station-card {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s, box-shadow 0.2s;
  position: relative;
}

.station-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.station-photo {
  width: 100%;
  height: 300rpx;
  object-fit: cover;
}

.station-info {
  padding: 30rpx;
  padding-bottom: 20rpx;
}

.station-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15rpx;
}

.station-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-right: 20rpx;
}

.station-rating {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.rating-stars {
  font-size: 24rpx;
  color: #FFD700;
  margin-bottom: 5rpx;
}

.distance {
  font-size: 22rpx;
  color: #999;
}

.station-address {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.station-description {
  display: block;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 20rpx;
  word-break: break-word;
}

/* 收容情况 */
.capacity-info {
  margin-bottom: 15rpx;
}

.capacity-text {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.capacity-bar {
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.capacity-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50 0%, #8BC34A 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 许可证信息 */
.license-text {
  display: block;
  font-size: 22rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 操作按钮 */
.station-actions {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  display: flex;
  gap: 10rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 30rpx;
  font-size: 28rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
}

.action-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 1);
}

.phone-btn {
  color: #4CAF50;
}

.nav-btn {
  color: #2196F3;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.empty-hint {
  font-size: 24rpx;
  color: #ccc;
}

/* 加载更多/没有更多 */
.load-more,
.no-more {
  text-align: center;
  padding: 40rpx 0;
  font-size: 26rpx;
  color: #999;
}

/* 刷新提示 */
.refresh-hint {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #FF6F61;
  color: white;
  text-align: center;
  padding: 20rpx;
  font-size: 28rpx;
  z-index: 1000;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .station-name {
    font-size: 28rpx;
  }
  
  .station-description {
    font-size: 26rpx;
  }
  
  .station-info {
    padding: 25rpx;
  }
  
  .action-btn {
    width: 50rpx;
    height: 50rpx;
    font-size: 24rpx;
  }
}