<!--pages/rescue/animal-list/animal-list.wxml-->
<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">动物信息管理</text>
      <text class="page-subtitle">共 {{total}} 只动物 · 用心呵护每一个生命</text>
    </view>
    <view class="header-actions">
      <view class="action-btn" bindtap="toggleSearch">
        <text class="action-icon">🔍</text>
      </view>
      <view class="action-btn" bindtap="goToUploadAnimal">
        <text class="action-icon">➕</text>
      </view>
    </view>
  </view>

  <!-- 搜索和筛选栏 -->
  <view class="filter-bar {{showSearch ? 'show-search' : ''}}">
    <!-- 搜索框 -->
    <view wx:if="{{showSearch}}" class="search-container">
      <view class="search-box">
        <input 
          class="search-input"
          placeholder="搜索品种、来源..."
          value="{{searchKeyword}}"
          bindinput="onSearchInput"
          bindconfirm="performSearch"
        />
        <view class="search-btn" bindtap="performSearch">
          <text class="search-icon">🔍</text>
        </view>
      </view>
    </view>
    
    <!-- 状态筛选 -->
    <view class="filter-container">
      <picker 
        class="status-filter"
        range="{{statusOptions}}"
        range-key="text"
        value="{{statusFilterIndex}}"
        bindchange="onStatusFilterChange"
      >
        <view class="filter-content">
          <text class="filter-text">{{currentStatusText}}</text>
          <text class="filter-arrow">▼</text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <view class="error-content">
      <text class="error-icon">😕</text>
      <text class="error-text">{{error}}</text>
      <view class="retry-btn" bindtap="retryLoad">
        <text class="retry-text">重试</text>
      </view>
    </view>
  </view>

  <!-- 空数据状态 -->
  <view wx:elif="{{isEmpty}}" class="empty-container">
    <view class="empty-content">
      <text class="empty-icon">🐾</text>
      <text class="empty-title">暂无动物信息</text>
      <text class="empty-desc">还没有上传任何动物信息，快去添加第一只小动物吧</text>
      <view class="empty-action" bindtap="goToUploadAnimal">
        <text class="action-text">上传动物信息</text>
      </view>
    </view>
  </view>

  <!-- 动物列表 -->
  <view wx:else class="animal-list">
    <view 
      wx:for="{{animalList}}" 
      wx:key="id" 
      class="animal-card"
      data-id="{{item.id}}"
      bindtap="viewAnimalDetail"
    >
      <!-- 动物照片 -->
      <view class="animal-photo">
        <image 
          class="photo-image" 
          src="{{item.photo}}" 
          mode="aspectFill"
          data-photo="{{item.photo}}"
          bindtap="previewAnimalPhoto"
          lazy-load="{{true}}"
        />
        <view class="photo-overlay">
          <view class="animal-status {{item.statusClass}}">
            <text class="status-text">{{item.status}}</text>
          </view>
        </view>
      </view>

      <!-- 动物信息 -->
      <view class="animal-info">
        <view class="info-header">
          <text class="animal-breed">{{item.breed}}</text>
          <view class="animal-gender {{item.gender === '男' ? 'gender-male' : 'gender-female'}}">
            <text class="gender-icon">{{item.gender === '男' ? '♂' : '♀'}}</text>
            <text class="gender-text">{{item.gender}}</text>
          </view>
        </view>

        <view class="info-details">
          <view wx:if="{{item.ageText}}" class="info-item">
            <text class="info-label">年龄：</text>
            <text class="info-value">{{item.ageText}}</text>
          </view>
          <view wx:if="{{item.birthDate}}" class="info-item">
            <text class="info-label">出生：</text>
            <text class="info-value">{{item.birthDate}}</text>
          </view>
          <view wx:if="{{item.source}}" class="info-item">
            <text class="info-label">来源：</text>
            <text class="info-value">{{item.source}}</text>
          </view>
        </view>

        <view wx:if="{{item.medicalRecord}}" class="medical-record">
          <text class="record-label">医疗记录：</text>
          <text class="record-content">{{item.medicalRecord}}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="animal-actions" catchtap="stopPropagation">
        <view 
          class="action-button edit-btn"
          data-id="{{item.id}}"
          bindtap="editAnimal"
        >
          <text class="action-icon">✏️</text>
          <text class="action-text">编辑</text>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{hasMore && !loadingMore}}" class="load-more" bindtap="loadMoreAnimals">
      <text class="load-more-text">点击加载更多</text>
    </view>

    <!-- 加载更多中 -->
    <view wx:if="{{loadingMore}}" class="loading-more">
      <view class="loading-spinner small"></view>
      <text class="loading-text">加载更多中...</text>
    </view>

    <!-- 没有更多数据 -->
    <view wx:if="{{!hasMore && animalList.length > 0}}" class="no-more">
      <text class="no-more-text">已显示全部 {{total}} 条记录</text>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>