/**
 * 救助站相关接口服务
 */
import request from './request';
import { CONFIG } from './config';

/**
 * 查询救助站（普通用户）
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码（从1开始）
 * @param {number} params.pageSize - 每页数量
 * @param {string} params.address - 查询地址（必需）
 * @returns {Promise<Object>} 救助站列表
 */
function getRescueStationList(params = {}) {
  console.log('📍 获取救助站列表，参数:', params);

  const { page = 1, pageSize = 10, address } = params;

  // 参数验证
  if (!Number.isInteger(page) || page < 1) {
    return Promise.reject(new Error('页码必须是大于0的整数'));
  }

  if (!Number.isInteger(pageSize) || pageSize < 1 || pageSize > 100) {
    return Promise.reject(new Error('每页数量必须是1-100之间的整数'));
  }

  if (!address || typeof address !== 'string') {
    return Promise.reject(new Error('地址(address)为必填项，且必须是字符串'));
  }

  const requestData = { page, pageSize, address };

  const options = {
    useMock: CONFIG.USE_MOCK,
    headers: {}
  };

  const token = wx.getStorageSync('token');
  if (token) {
    options.headers['Authorization'] = token;
  }

  return request.post(API_PATH.SEARCH_RESCUE_STATIONS, requestData, options)
    .then(response => {
      console.log('📥 救助站列表响应:', response);

      if (response && response.code === 200) {
        const result = {
          code: response.code,
          message: response.message || '查询成功',
          total: response.total || 0,
          data: Array.isArray(response.data)
            ? response.data.map(item => ({
                id: item.ID,
                name: item.name || '',
                address: item.address || '',
                contact: item.contact || '',
                license: item.license || '',
                photo: item.photo || ''
              }))
            : []
        };

        return result;
      } else {
        throw new Error(response.message || '获取救助站失败');
      }
    })
    .catch(error => {
      console.error('❌ 获取救助站列表失败:', error);

      if (error.message && error.message.includes('网络')) {
        return {
          code: 200,
          message: '网络连接失败，请检查网络后重试',
          total: 0,
          data: []
        };
      }

      throw error;
    });
}


/**
 * 搜索救助站
 * @param {Object} params - 搜索参数
 * @param {number} params.page - 页码，从1开始
 * @param {number} params.pageSize - 每页数量，建议5-100
 * @param {string} params.address - 地址关键词
 * @returns {Promise} 救助站列表
 */
function searchStations(params = {}) {
  // 参数验证
  const { page = 1, pageSize = 10, address = '' } = params;
  
  // 验证页码参数
  if (!Number.isInteger(page) || page < 1) {
    return Promise.reject(new Error('页码必须是大于0的整数'));
  }
  
  // 验证每页数量参数
  if (!Number.isInteger(pageSize) || pageSize < 5 || pageSize > 100) {
    return Promise.reject(new Error('每页数量必须是5-100之间的整数'));
  }

  const queryParams = {
    page,
    pageSize,
    address
  };

  return request.get('/api/rescue-stations/search', queryParams, { 
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || ''
    }
  });
}

/**
 * 评价救助站（普通用户）
 * @param {Object} data - 评价数据
 * @returns {Promise} 评价结果
 */
function evaluateRescueStation(data) {
  return request.post(API_PATHS.EVALUATE_RESCUE_STATION, data, { 
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || ''
    }
  });
}

/**
 * 查看用户对救助站的评价（普通用户）
 * @param {Object} data - 查询参数 { page, pageSize, id }
 * @returns {Promise} 评价列表
 */
function getRescueStationEvaluations(data = {}) {
  return request.post(API_PATHS.GET_RESCUE_EVALUATIONS, data, {
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || ''
    }
  });
}

/**
 * 删除对救助站的评价（普通用户）
 * @param {string} evaluationId - 评价ID
 * @returns {Promise} 删除结果
 */
function deleteRescueStationEvaluation(evaluationId) {
  if (!evaluationId || !Number.isInteger(Number(evaluationId))) {
    return Promise.reject(new Error('评价ID必须是整数'));
  }

  return request.delete(`${API_PATHS.DELETE_RESCUE_EVALUATION}/${evaluationId}`, {}, {
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || ''
    }
  });
}


/**
 * 上传流浪动物信息（救助站用户）
 * @param {Object} data - 动物信息
 * @returns {Promise} 上传结果
 */
function uploadAnimalInfo(data) {
  return request.post(API_PATH.UPLOAD_ANIMAL_INFO, data, { 
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || ''
    }
  });
}

/**
 * 获取单个动物信息（救助站）
 * @param {number} animalId - 动物ID
 * @returns {Promise} 动物信息
 */
function getAnimalInfo(animalId) {
  if (!animalId || !Number.isInteger(Number(animalId)) || Number(animalId) <= 0) {
    return Promise.reject(new Error('动物ID必须是大于0的整数'));
  }
  
  return request.get(`/api/users/rescuestation/animals/${animalId}`, {}, { 
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || ''
    }
  });
}

/**
 * 获取流浪动物信息（救助站用户）
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，从1开始
 * @param {number} params.pageSize - 每页数量，建议5-100
 * @returns {Promise} 动物信息列表
 */
function getAnimalsForRescue(params = {}) {
  const { page = 1, pageSize = 10 } = params;

  // 校验页码
  if (!Number.isInteger(page) || page < 1) {
    return Promise.reject(new Error('页码必须是大于0的整数'));
  }

  // 校验每页数量
  if (!Number.isInteger(pageSize) || pageSize < 5 || pageSize > 100) {
    return Promise.reject(new Error('每页数量必须是5-100之间的整数'));
  }

  // 使用POST请求并传递请求体参数
  return request.post(API_PATH.GET_ANIMALS, { page, pageSize }, { 
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || '' // 获取授权token
    }
  });
}

/**
 * 修改流浪动物信息（救助站用户）
 * @param {Object} data - 动物信息
 * @returns {Promise} 修改结果
 */
function updateAnimalInfo(data) {

  // 校验数据
  if (!data || typeof data !== 'object') {
    return Promise.reject(new Error('动物信息数据不能为空'));
  }

  const { id, breed, gender, status } = data;

  // 校验动物ID
  if (!id || !Number.isInteger(Number(id)) || Number(id) <= 0) {
    return Promise.reject(new Error('动物ID必须是大于0的整数'));
  }

  // 校验动物品种
  if (!breed || typeof breed !== 'string' || breed.trim().length === 0) {
    return Promise.reject(new Error('动物品种不能为空'));
  }

  if (breed.length > 50) {
    return Promise.reject(new Error('动物品种不能超过50字符'));
  }

  // 校验性别
  if (!gender || (gender !== '男' && gender !== '女')) {
    return Promise.reject(new Error('性别必须是"男"或"女"'));
  }

  // 校验动物状态
  if (!status || typeof status !== 'string' || status.trim().length === 0) {
    return Promise.reject(new Error('动物状态不能为空'));
  }

  // 校验来源
  if (data.source && data.source.length > 100) {
    return Promise.reject(new Error('动物来源不能超过100字符'));
  }

  // 校验医疗记录
  if (data.medicalRecord && data.medicalRecord.length > 500) {
    return Promise.reject(new Error('医疗记录不能超过500字符'));
  }

  // 校验出生日期格式
  if (data.birthDate) {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(data.birthDate)) {
      return Promise.reject(new Error('出生日期格式必须是YYYY-MM-DD'));
    }
  }

  // 发送PUT请求更新动物信息
  return request.put(API_PATH.UPDATE_ANIMAL_INFO, data, { 
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || ''
    }
  });
}

/**
 * @deprecated 请使用 updateAnimalInfo 方法
 */
function updateAnimal(data) {
  console.warn('updateAnimal 方法已废弃，请使用 updateAnimalInfo 方法');
  return updateAnimalInfo(data);
}

/**
 * 查看领养匹配状态（救助站用户）
 * @param {Object} params - 查询参数
 * @returns {Promise} 领养匹配状态列表
 */
function getAdoptStatusForRescue(params = {}) {
  return request.get(API_PATH.VIEW_ADOPT_STATUS, params, { 
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || ''
    }
  });
}

/**
 * 修改领养匹配状态（救助站）
 * @param {Object} data - 更新数据
 * @returns {Promise} 更新结果
 */
function updateAdoptStatus(data, id, status) {
  // 替换路径中的 ID 和 status 参数
  const url = API_PATH.UPDATE_ADOPT_STATUS.replace('{ID}', id).replace('{status}', status);
  
  return request.put(url, data, { 
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || ''  // 获取授权token
    }
  });
}

/**
 * 查看用户评价（救助站用户）
 * @param {Object} params - 查询参数
 * @returns {Promise} 评价列表
 */
function getEvaluationsForRescue(params = { page: 1, pageSize: 10 }) {
  return request.post(API_PATH.VIEW_EVALUATIONS_FOR_RESCUE, params, { 
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || ''  // 获取授权token
    }
  });
}

export default {
  getRescueStationList,
  searchStations,        
  evaluateRescueStation,
  getRescueStationEvaluations,
  deleteRescueStationEvaluation,
  uploadAnimalInfo,
  getAnimalInfo,
  getAnimalsForRescue,
  updateAnimalInfo,
  updateAnimal,
  getAdoptStatusForRescue,
  updateAdoptStatus,
  getEvaluationsForRescue
};