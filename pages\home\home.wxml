<!-- pages/home/<USER>
<view class="container">
  <!-- 轮播图 -->
  <swiper class="banner" indicator-dots="true" autoplay="true" interval="5000" duration="500" circular="true">
    <swiper-item wx:for="{{banners}}" wx:key="id">
      <image class="banner-image" src="{{item.image}}" mode="aspectFill" data-url="{{item.url}}" bindtap="goToPage"></image>
    </swiper-item>
  </swiper>
  
  <!-- 功能菜单 -->
  <view class="menu-container">
    <view class="menu-item" wx:for="{{menus}}" wx:key="id" data-url="{{item.url}}" bindtap="goToPage">
      <image class="menu-icon" src="{{item.icon}}" mode="aspectFit"></image>
      <view class="menu-name">{{item.name}}</view>
    </view>
  </view>
  
  <!-- 推荐宠物 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">推荐宠物</text>
      <text class="section-more" data-url="/pages/adopt/adopt" bindtap="goToPage">查看更多</text>
    </view>
    
    <view class="pet-list">
      <view class="pet-card" wx:for="{{pets}}" wx:key="id" data-id="{{item.id}}" bindtap="goToPetDetail">
        <image class="pet-image" src="{{item.image}}" mode="aspectFill"></image>
        <view class="pet-info">
          <view class="pet-name">{{item.name}}</view>
          <view class="pet-desc">{{item.breed}} · {{item.age}} · {{item.gender}}</view>
          <view class="pet-status">{{item.status}}</view>
        </view>
      </view>
    </view>
  </view>
</view>