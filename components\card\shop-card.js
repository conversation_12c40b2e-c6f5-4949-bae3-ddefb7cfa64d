// components/card/shop-card.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 商店数据
    shop: {
      type: Object,
      value: {}
    },
    // 显示模式：compact(紧凑), normal(普通), detailed(详细)
    mode: {
      type: String,
      value: 'normal'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 默认图片
    defaultImage: '/assets/images/default-shop.png'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 点击卡片
    onTapCard: function() {
      const { shop } = this.properties;
      this.triggerEvent('tap', { shopId: shop.id });
    },
    
    // 点击收藏按钮
    onTapFavorite: function(e) {
      e.stopPropagation(); // 阻止事件冒泡
      const { shop } = this.properties;
      this.triggerEvent('favorite', { shopId: shop.id, isFavorite: !shop.isFavorite });
    },
    
    // 点击电话按钮
    onTapPhone: function(e) {
      e.stopPropagation(); // 阻止事件冒泡
      const { shop } = this.properties;
      if (shop.phone) {
        wx.makePhoneCall({
          phoneNumber: shop.phone,
          fail: (err) => {
            console.error('拨打电话失败:', err);
            wx.showToast({
              title: '拨打电话失败',
              icon: 'none'
            });
          }
        });
      } else {
        wx.showToast({
          title: '无电话号码',
          icon: 'none'
        });
      }
    },
    
    // 点击导航按钮
    onTapNavigation: function(e) {
      e.stopPropagation(); // 阻止事件冒泡
      const { shop } = this.properties;
      if (shop.latitude && shop.longitude) {
        wx.openLocation({
          latitude: shop.latitude,
          longitude: shop.longitude,
          name: shop.name,
          address: shop.address
        });
      } else {
        wx.showToast({
          title: '无位置信息',
          icon: 'none'
        });
      }
    },
    
    // 图片加载失败
    onImageError: function() {
      this.setData({
        'shop.imageUrl': this.data.defaultImage
      });
    }
  }
})