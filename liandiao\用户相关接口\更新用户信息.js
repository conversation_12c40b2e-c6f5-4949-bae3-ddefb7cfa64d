import { updateUserInfo } from '../../services/userService';

const userInfoData = {
    // 需要更新的用户信息字段
};

updateUserInfo(userInfoData)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            wx.showToast({
                title: '用户信息更新成功',
                icon: 'success'
            });
        } else {
            wx.showToast({
                title: res.message || '用户信息更新失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('用户信息更新出错', err);
        wx.showToast({
            title: '用户信息更新出错，请重试',
            icon: 'none'
        });
    });