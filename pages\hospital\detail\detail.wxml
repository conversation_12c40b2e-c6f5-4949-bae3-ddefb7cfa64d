<!-- pages/hospital/detail/detail.wxml - 删除缺失接口的界面代码 -->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 医院评价页面 -->
  <view class="hospital-detail" wx:else>
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">医院评价</text>
      <text class="hospital-id-info">医院ID: {{hospitalId}}</text>
    </view>

    <!-- 评价列表 -->
    <view class="tab-content">
      <view class="evaluation-header">
        <view class="evaluation-title-section">
          <text class="evaluation-title">用户评价</text>
          <text class="evaluation-count" wx:if="{{evalTotal > 0}}">共{{evalTotal}}条评价</text>
        </view>
        <button class="btn-evaluate" bindtap="evaluateHospital">我要评价</button>
      </view>
      
      <view class="evaluation-list" wx:if="{{evaluations.length > 0}}">
        <view class="evaluation-item" wx:for="{{evaluations}}" wx:key="id">
          <view class="evaluation-user">
            <image class="user-avatar" src="{{item.userAvatar || '/assets/images/default-pet.png'}}" mode="aspectFill"></image>
            <view class="user-info">
              <text class="user-name">{{item.userName || '匿名用户'}}</text>
              <view class="evaluation-rating">
                <view class="rating-stars">
                  <block wx:for="{{5}}" wx:key="index" wx:for-item="star">
                    <image 
                      class="star-icon" 
                      src="{{star <= item.rating ? '/assets/images/heart-filled.png' : '/assets/images/heart.png'}}"
                    ></image>
                  </block>
                </view>
                <text class="rating-value">{{item.rating}}分</text>
              </view>
            </view>
          </view>
          <view class="evaluation-content">{{item.content || '用户未填写评价内容'}}</view>
          <view class="evaluation-time">{{item.createTime || '未知时间'}}</view>
        </view>
      </view>
      
      <view class="empty-state" wx:else>
        <image class="empty-image" src="/assets/images/empty.png"></image>
        <text class="empty-text">暂无评价信息</text>
        <text class="empty-tip">快来成为第一个评价的用户吧~</text>
      </view>
      
      <!-- 加载更多 -->
      <view class="loading-more" wx:if="{{loading}}">
        <view class="loading-spinner"></view>
        <text>加载中...</text>
      </view>
      <view class="no-more" wx:if="{{!evalHasMore && evaluations.length > 0}}">
        <text>没有更多评价了</text>
      </view>
    </view>
  </view>

  <!-- 评价弹窗 -->
  <view class="evaluation-modal-overlay" wx:if="{{showEvaluationModal}}" bindtap="closeEvaluationModal">
    <view class="evaluation-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">评价医院</text>
        <view class="modal-close" bindtap="closeEvaluationModal">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="modal-content">
        <!-- 医院信息简化 -->
        <view class="hospital-info-card">
          <view class="hospital-name-text">医院ID: {{hospitalId}}</view>
        </view>
        
        <!-- 评分选择 -->
        <view class="rating-section">
          <text class="section-title">请给医院评分</text>
          <view class="star-rating">
            <block wx:for="{{5}}" wx:key="index" wx:for-item="star">
              <image 
                class="star-item {{star <= evaluationForm.rating ? 'active' : ''}}" 
                src="{{star <= evaluationForm.rating ? '/assets/images/heart-filled.png' : '/assets/images/heart.png'}}"
                bindtap="onRatingChange"
                data-rating="{{star}}"
              ></image>
            </block>
          </view>
          <text class="rating-text">{{evaluationForm.rating > 0 ? evaluationForm.rating + ' 分' : '点击星星评分'}}</text>
        </view>
        
        <!-- 评价内容 -->
        <view class="content-section">
          <text class="section-title">写下您的评价</text>
          <textarea 
            class="content-textarea"
            placeholder="请分享您在这家医院的体验，您的评价对其他用户很有帮助~"
            value="{{evaluationForm.content}}"
            bindinput="onContentInput"
            maxlength="500"
            auto-height
          ></textarea>
          <view class="content-count">{{evaluationForm.content.length}}/500</view>
        </view>
      </view>
      
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="closeEvaluationModal">取消</button>
        <button 
          class="btn-submit" 
          bindtap="submitEvaluation"
          disabled="{{submittingEvaluation || !evaluationForm.rating || !evaluationForm.content.trim()}}"
        >
          {{submittingEvaluation ? '提交中...' : '提交评价'}}
        </button>
      </view>
    </view>
  </view>
</view>