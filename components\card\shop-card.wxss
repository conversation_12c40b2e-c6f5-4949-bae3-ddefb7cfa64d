/* components/card/shop-card.wxss */

/* 通用卡片样式 */
.shop-card {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 紧凑模式 */
.shop-card-compact {
  width: 100%;
  height: 240rpx;
  display: flex;
  flex-direction: column;
}

.shop-image-compact {
  width: 100%;
  height: 180rpx;
}

.shop-info-compact {
  padding: 8rpx 12rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.shop-name-compact {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}

.shop-category-compact {
  font-size: 22rpx;
  color: #666;
  margin-top: 4rpx;
}

/* 普通模式 */
.shop-card-normal {
  display: flex;
  padding: 20rpx;
}

.shop-image-normal {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.shop-info-normal {
  margin-left: 20rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.shop-name-normal {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.shop-rating-normal {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.rating-stars {
  display: flex;
  align-items: center;
}

.star-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}

.rating-value {
  font-size: 24rpx;
  color: #ff9800;
  margin-left: 8rpx;
}

.rating-count {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}

.shop-address-normal {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.shop-actions-normal {
  display: flex;
  margin-top: auto;
}

.shop-action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
}

.action-icon {
  width: 36rpx;
  height: 36rpx;
  margin-bottom: 4rpx;
}

.action-text {
  font-size: 22rpx;
  color: #666;
}

/* 详细模式 */
.shop-card-detailed {
  display: flex;
  flex-direction: column;
}

.shop-image-detailed {
  width: 100%;
  height: 300rpx;
}

.shop-info-detailed {
  padding: 24rpx;
}

.shop-header-detailed {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
}

.shop-name-detailed {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.shop-rating-detailed {
  display: flex;
  align-items: center;
}

.shop-meta-detailed {
  margin-bottom: 20rpx;
}

.shop-meta-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.meta-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.meta-text {
  font-size: 28rpx;
  color: #666;
}

.shop-description-detailed {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 24rpx;
}

.shop-actions-detailed {
  display: flex;
  justify-content: space-between;
}

.shop-action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  margin: 0 10rpx;
}

.shop-action-button:first-child {
  margin-left: 0;
}

.shop-action-button:last-child {
  margin-right: 0;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-secondary {
  color: #666;
}