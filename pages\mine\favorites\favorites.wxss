.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6F61;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 收藏列表 */
.favorites-list {
  padding: 20rpx;
}

.favorite-item {
  margin-bottom: 30rpx;
}

.pet-card {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s, box-shadow 0.2s;
}

.pet-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 1rpx 8rpx rgba(0, 0, 0, 0.12);
}

.pet-image {
  width: 100%;
  height: 400rpx;
  object-fit: cover;
}

.pet-info {
  padding: 30rpx;
}

.pet-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.pet-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.pet-tags {
  display: flex;
  gap: 10rpx;
}

.tag {
  background-color: #FF6F61;
  color: white;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  white-space: nowrap;
}

.pet-details {
  margin-bottom: 25rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  min-width: 120rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* 操作按钮 */
.card-actions {
  padding: 0 30rpx 30rpx;
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-btn {
  background-color: #f0f0f0;
  color: #666;
}

.remove-btn:active {
  background-color: #e0e0e0;
}

.contact-btn {
  background-color: #FF6F61;
  color: white;
}

.contact-btn:active {
  background-color: #e55a4e;
}

/* 加载更多和没有更多 */
.loading-more, .no-more {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 150rpx 40rpx;
}

.empty-image {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 50rpx;
  text-align: center;
  line-height: 1.4;
}

.goto-btn {
  width: 300rpx;
  height: 70rpx;
  background: linear-gradient(135deg, #FF6F61 0%, #FF8A75 100%);
  color: white;
  border-radius: 35rpx;
  font-size: 28rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(255, 111, 97, 0.3);
}

.goto-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 111, 97, 0.3);
}