import { getAnimalsForRescue } from '../../services/rescueService';

const params = {
    page: 1,
    pageSize: 10
};

getAnimalsForRescue(params)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('流浪动物信息:', res.data);
        } else {
            wx.showToast({
                title: res.message || '获取流浪动物信息失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('获取流浪动物信息出错', err);
        wx.showToast({
            title: '获取流浪动物信息出错，请重试',
            icon: 'none'
        });
    });