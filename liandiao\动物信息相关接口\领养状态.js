import { getAdoptStatus } from '../../services/animalService';

const params = {
    // 查询参数
};

getAdoptStatus(params)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('领养状态:', res.data);
        } else {
            wx.showToast({
                title: res.message || '获取领养状态失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('获取领养状态出错', err);
        wx.showToast({
            title: '获取领养状态出错，请重试',
            icon: 'none'
        });
    });