// services/fileService.js
import request from './request';
import { CONFIG } from './config';

// 上传文件的函数
export function uploadFile(filePath) {
    return new Promise((resolve, reject) => {
        // 获取用户信息以获取userType
        const userInfo = wx.getStorageSync(CONFIG.STORAGE_KEYS.USER_INFO);
        const token = wx.getStorageSync(CONFIG.STORAGE_KEYS.TOKEN);

        if (!userInfo || !userInfo.usertype) {
            reject(new Error('无法获取用户类型，请重新登录'));
            return;
        }

        if (!token) {
            reject(new Error('登录信息已过期，请重新登录'));
            return;
        }

        if (!filePath) {
            reject(new Error('请选择要上传的图片'));
            return;
        }

        // 使用配置工具方法构造URL
        const uploadUrl = CONFIG.buildUploadUrl(userInfo.usertype, 'image'); // 假设图片类型为image
        console.log('🔗 上传图片URL:', uploadUrl);
        console.log('👤 用户类型:', userInfo.usertype);
        console.log('🖼️ 图片类型:', 'image');

        // 检查文件大小
        wx.getFileInfo({
            filePath,
            success: (fileInfo) => {
                // 使用配置工具方法验证文件大小
                if (!CONFIG.validateFileSize(fileInfo.size)) {
                    reject(new Error(`图片大小不能超过${CONFIG.UPLOAD_FILE_SIZE_LIMIT}MB`));
                    return;
                }

                // 验证文件格式
                const fileName = filePath.split('/').pop() || '';
                if (!CONFIG.validateImageFormat(fileName)) {
                    reject(new Error('不支持的图片格式'));
                    return;
                }

                // 执行上传
                performUpload();
            },
            fail: () => {
                // 如果获取文件信息失败，直接上传
                performUpload();
            }
        });

        function performUpload() {
            // 使用 request.js 的 uploadFile 方法
            request.uploadFile(
                uploadUrl,
                filePath,
                'images', // 根据接口文档，参数名是 images
                {}, // 额外的表单数据
                CONFIG.USE_MOCK,
                { // 自定义headers
                    'Authorization': token
                }
            ).then(result => {
                console.log('✅ 图片上传成功:', result);

                // 使用新配置的成功状态码
                if (result && result.code === CONFIG.ERROR_CODES.SUCCESS) {
                    // 处理返回的图片URL
                    let imageUrl = result.data?.url || '';

                    if (imageUrl) {
                        // 使用配置工具方法处理图片URL
                        imageUrl = CONFIG.getFullImageUrl(imageUrl);

                        console.log('🖼️ 完整图片URL:', imageUrl);
                        resolve({ data: { url: imageUrl } });
                    } else {
                        reject(new Error('上传返回数据格式错误'));
                    }
                } else {
                    reject(new Error('上传失败，请稍后重试'));
                }
            }).catch(error => {
                console.error('❌ 图片上传失败:', error);
                reject(error);
            });
        }
    });
}