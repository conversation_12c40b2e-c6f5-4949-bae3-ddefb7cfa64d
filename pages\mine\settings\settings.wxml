<view class="container">
  <!-- 个人信息设置 -->
  <view wx:if="{{isLoggedIn}}" class="section">
    <view class="section-title">个人信息</view>
    
    <!-- 头像设置 -->
    <view class="setting-item" bindtap="uploadAvatar">
      <view class="setting-content">
        <view class="setting-icon">👤</view>
        <view class="setting-text">
          <text class="setting-label">头像</text>
          <text class="setting-desc">点击更换头像</text>
        </view>
      </view>
      <view class="setting-action">
        <image 
          class="avatar-preview" 
          src="{{userInfo.avatar || '/assets/images/default-avatar.png'}}" 
          mode="aspectFill"
        />
        <text class="arrow">></text>
      </view>
    </view>

    <!-- 用户信息编辑 -->
    <view wx:if="{{!editMode}}" class="info-display">
      <view class="setting-item" bindtap="startEdit">
        <view class="setting-content">
          <view class="setting-icon">✏️</view>
          <view class="setting-text">
            <text class="setting-label">用户名</text>
            <text class="setting-desc">{{userInfo.username || '未设置'}}</text>
          </view>
        </view>
        <view class="setting-action">
          <text class="arrow">></text>
        </view>
      </view>

      <view class="setting-item">
        <view class="setting-content">
          <view class="setting-icon">📱</view>
          <view class="setting-text">
            <text class="setting-label">手机号</text>
            <text class="setting-desc">{{userInfo.account || '未绑定'}}</text>
          </view>
        </view>
      </view>

      <view class="setting-item">
        <view class="setting-content">
          <view class="setting-icon">🏷️</view>
          <view class="setting-text">
            <text class="setting-label">用户类型</text>
            <text class="setting-desc">{{userInfo.usertype || '普通用户'}}</text>
          </view>
        </view>
      </view>

      <view class="setting-item" bindtap="changeAddress">
        <view class="setting-content">
          <view class="setting-icon">📍</view>
          <view class="setting-text">
            <text class="setting-label">所在地址</text>
            <text class="setting-desc">{{userInfo.address || '未设置'}}</text>
          </view>
        </view>
        <view class="setting-action">
          <text class="arrow">></text>
        </view>
      </view>
    </view>

    <!-- 编辑模式 -->
    <view wx:else class="edit-mode">
      <view class="edit-form">
        <view class="form-item">
          <text class="form-label">用户名</text>
          <input 
            class="form-input" 
            value="{{tempUserInfo.username}}" 
            bindinput="onUsernameInput"
            placeholder="请输入用户名"
            maxlength="20"
          />
        </view>

        <view class="form-item">
          <text class="form-label">个人简介</text>
          <textarea 
            class="form-textarea" 
            value="{{tempUserInfo.bio}}" 
            bindinput="onBioInput"
            placeholder="写点什么介绍一下自己吧..."
            maxlength="100"
          />
        </view>
      </view>

      <view class="edit-actions">
        <button class="cancel-btn" bindtap="cancelEdit">取消</button>
        <button class="save-btn" bindtap="saveUserInfo" loading="{{loading}}">保存</button>
      </view>
    </view>
  </view>

  <!-- 系统设置 -->
  <view class="section">
    <view class="section-title">系统设置</view>
    
    <view class="setting-item">
      <view class="setting-content">
        <view class="setting-icon">🔔</view>
        <view class="setting-text">
          <text class="setting-label">消息通知</text>
          <text class="setting-desc">接收领养、评价等消息通知</text>
        </view>
      </view>
      <view class="setting-action">
        <switch 
          checked="{{systemSettings.notificationEnabled}}" 
          bindchange="toggleNotification"
          color="#FF6F61"
        />
      </view>
    </view>

    <view class="setting-item">
      <view class="setting-content">
        <view class="setting-icon">📍</view>
        <view class="setting-text">
          <text class="setting-label">位置服务</text>
          <text class="setting-desc">用于推荐附近的宠物和服务</text>
        </view>
      </view>
      <view class="setting-action">
        <switch 
          checked="{{systemSettings.locationEnabled}}" 
          bindchange="toggleLocation"
          color="#FF6F61"
        />
      </view>
    </view>

    <view class="setting-item">
      <view class="setting-content">
        <view class="setting-icon">🔄</view>
        <view class="setting-text">
          <text class="setting-label">自动更新</text>
          <text class="setting-desc">自动检查并下载应用更新</text>
        </view>
      </view>
      <view class="setting-action">
        <switch 
          checked="{{systemSettings.autoUpdate}}" 
          bindchange="toggleAutoUpdate"
          color="#FF6F61"
        />
      </view>
    </view>

    <view class="setting-item">
      <view class="setting-content">
        <view class="setting-icon">🌙</view>
        <view class="setting-text">
          <text class="setting-label">暗黑模式</text>
          <text class="setting-desc">开启后界面将变为深色主题</text>
        </view>
      </view>
      <view class="setting-action">
        <switch 
          checked="{{systemSettings.darkMode}}" 
          bindchange="toggleDarkMode"
          color="#FF6F61"
        />
      </view>
    </view>
  </view>

  <!-- 存储和缓存 -->
  <view class="section">
    <view class="section-title">存储和缓存</view>
    
    <view class="setting-item" bindtap="clearCache">
      <view class="setting-content">
        <view class="setting-icon">🗑️</view>
        <view class="setting-text">
          <text class="setting-label">清理缓存</text>
          <text class="setting-desc">当前缓存：{{cacheSize}}</text>
        </view>
      </view>
      <view class="setting-action">
        <text class="arrow">></text>
      </view>
    </view>
  </view>

  <!-- 应用信息 -->
  <view class="section">
    <view class="section-title">应用信息</view>
    
    <view class="setting-item" bindtap="checkUpdate">
      <view class="setting-content">
        <view class="setting-icon">⬆️</view>
        <view class="setting-text">
          <text class="setting-label">检查更新</text>
          <text class="setting-desc">当前版本：{{appInfo.version}}</text>
        </view>
      </view>
      <view class="setting-action">
        <text class="arrow">></text>
      </view>
    </view>

    <view class="setting-item" bindtap="aboutUs">
      <view class="setting-content">
        <view class="setting-icon">ℹ️</view>
        <view class="setting-text">
          <text class="setting-label">关于我们</text>
          <text class="setting-desc">应用介绍和开发团队</text>
        </view>
      </view>
      <view class="setting-action">
        <text class="arrow">></text>
      </view>
    </view>
  </view>

  <!-- 帮助和支持 -->
  <view class="section">
    <view class="section-title">帮助和支持</view>
    
    <view class="setting-item" bindtap="contactService">
      <view class="setting-content">
        <view class="setting-icon">🎧</view>
        <view class="setting-text">
          <text class="setting-label">联系客服</text>
          <text class="setting-desc">获取帮助和技术支持</text>
        </view>
      </view>
      <view class="setting-action">
        <text class="arrow">></text>
      </view>
    </view>

    <view class="setting-item" bindtap="privacyPolicy">
      <view class="setting-content">
        <view class="setting-icon">🔒</view>
        <view class="setting-text">
          <text class="setting-label">隐私政策</text>
          <text class="setting-desc">了解我们如何保护您的隐私</text>
        </view>
      </view>
      <view class="setting-action">
        <text class="arrow">></text>
      </view>
    </view>

    <view class="setting-item" bindtap="userAgreement">
      <view class="setting-content">
        <view class="setting-icon">📋</view>
        <view class="setting-text">
          <text class="setting-label">用户协议</text>
          <text class="setting-desc">服务条款和使用规范</text>
        </view>
      </view>
      <view class="setting-action">
        <text class="arrow">></text>
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">城市流浪动物救助平台</text>
    <text class="build-text">版本 {{appInfo.version}} ({{appInfo.buildNumber}})</text>
    <text class="update-text">更新时间：{{appInfo.updateTime}}</text>
  </view>
</view>