import { getRescueStationList } from '../../services/rescueService';

const params = {
    // 查询参数
};

getRescueStationList(params)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('救助站列表:', res.data);
        } else {
            wx.showToast({
                title: res.message || '获取救助站列表失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('获取救助站列表出错', err);
        wx.showToast({
            title: '获取救助站列表出错，请重试',
            icon: 'none'
        });
    });