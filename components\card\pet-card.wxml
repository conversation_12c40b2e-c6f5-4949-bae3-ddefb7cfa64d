<!-- components/card/pet-card.wxml -->
<view class="pet-card {{mode}}" bindtap="onTapCard">
  <!-- 紧凑模式 -->
  <block wx:if="{{mode === 'compact'}}">
    <view class="pet-card-compact">
      <image 
        class="pet-image-compact" 
        src="{{pet.imageUrl || defaultImage}}" 
        mode="aspectFill"
        binderror="onImageError"
      ></image>
      <view class="pet-info-compact">
        <view class="pet-name-compact text-ellipsis">{{pet.name || '未命名'}}</view>
        <view class="pet-breed-compact text-ellipsis text-secondary">{{pet.breed || '未知品种'}}</view>
      </view>
    </view>
  </block>

  <!-- 普通模式 -->
  <block wx:elif="{{mode === 'normal'}}">
    <view class="pet-card-normal">
      <image 
        class="pet-image-normal" 
        src="{{pet.imageUrl || defaultImage}}" 
        mode="aspectFill"
        binderror="onImageError"
      ></image>
      <view class="pet-info-normal">
        <view class="pet-name-normal text-ellipsis">{{pet.name || '未命名'}}</view>
        <view class="pet-breed-normal text-ellipsis text-secondary">{{pet.breed || '未知品种'}}</view>
        <view class="pet-meta-normal">
          <view class="pet-age-normal text-small text-grey">{{pet.age || '未知年龄'}}</view>
          <view class="pet-gender-normal text-small text-grey">{{pet.gender || '未知性别'}}</view>
        </view>
        <view class="pet-actions-normal">
          <view 
            class="pet-favorite-normal {{pet.isFavorite ? 'is-favorite' : ''}}" 
            catchtap="onTapFavorite"
          >
            <image 
              class="favorite-icon" 
              src="{{pet.isFavorite ? '/assets/images/heart-filled.png' : '/assets/images/heart.png'}}" 
              mode="aspectFit"
            ></image>
          </view>
          <view class="pet-adopt-normal" catchtap="onTapAdopt">
            <text>领养</text>
          </view>
        </view>
      </view>
    </view>
  </block>

  <!-- 详细模式 -->
  <block wx:else>
    <view class="pet-card-detailed">
      <image 
        class="pet-image-detailed" 
        src="{{pet.imageUrl || defaultImage}}" 
        mode="aspectFill"
        binderror="onImageError"
      ></image>
      <view class="pet-info-detailed">
        <view class="pet-header-detailed">
          <view class="pet-name-detailed">{{pet.name || '未命名'}}</view>
          <view 
            class="pet-favorite-detailed {{pet.isFavorite ? 'is-favorite' : ''}}" 
            catchtap="onTapFavorite"
          >
            <image 
              class="favorite-icon" 
              src="{{pet.isFavorite ? '/assets/images/heart-filled.png' : '/assets/images/heart.png'}}" 
              mode="aspectFit"
            ></image>
          </view>
        </view>
        <view class="pet-meta-detailed">
          <view class="pet-meta-item">
            <text class="meta-label">品种：</text>
            <text class="meta-value">{{pet.breed || '未知'}}</text>
          </view>
          <view class="pet-meta-item">
            <text class="meta-label">年龄：</text>
            <text class="meta-value">{{pet.age || '未知'}}</text>
          </view>
          <view class="pet-meta-item">
            <text class="meta-label">性别：</text>
            <text class="meta-value">{{pet.gender || '未知'}}</text>
          </view>
          <view class="pet-meta-item">
            <text class="meta-label">健康状况：</text>
            <text class="meta-value">{{pet.health || '未知'}}</text>
          </view>
        </view>
        <view class="pet-description-detailed text-ellipsis-2">
          {{pet.description || '暂无描述'}}
        </view>
        <view class="pet-actions-detailed">
          <view class="pet-adopt-detailed" catchtap="onTapAdopt">领养申请</view>
        </view>
      </view>
    </view>
  </block>
</view>