<!-- pages/shop/detail/detail.wxml -->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 商店详情 -->
  <view class="shop-detail" wx:else>
    <!-- 店铺头部信息 -->
    <view class="shop-header">
      <image class="shop-image" src="{{shopInfo.image}}" mode="aspectFill"></image>
      <view class="shop-info">
        <view class="shop-name-rating">
          <text class="shop-name">{{shopInfo.name}}</text>
          <view class="shop-rating">
            <text class="rating-value">{{shopInfo.rating}}</text>
            <image class="rating-star" src="/assets/images/heart-filled.png"></image>
          </view>
        </view>
        <view class="shop-type">{{shopInfo.type}}</view>
        <view class="shop-address">
          <image class="location-icon" src="/assets/images/location-pin.png"></image>
          <text>{{shopInfo.address}}</text>
        </view>
        <view class="shop-business-hours">
          <text class="business-hours-label">营业时间：</text>
          <text class="business-hours-value">{{shopInfo.businessHours || '暂无信息'}}</text>
        </view>
        <view class="shop-description">{{shopInfo.description}}</view>
      </view>
    </view>

    <!-- 选项卡 -->
    <view class="tabs">
      <view class="tab {{currentTab === 'pets' ? 'active' : ''}}" bindtap="switchTab" data-tab="pets">宠物列表</view>
      <view class="tab {{currentTab === 'info' ? 'active' : ''}}" bindtap="switchTab" data-tab="info">商店信息</view>
      <view class="tab {{currentTab === 'evaluations' ? 'active' : ''}}" bindtap="switchTab" data-tab="evaluations">
        用户评价
        <text class="tab-count" wx:if="{{evalTotal > 0}}">({{evalTotal}})</text>
      </view>
    </view>

    <!-- 宠物列表 -->
    <view class="tab-content" wx:if="{{currentTab === 'pets'}}">
      <view class="pet-list" wx:if="{{pets.length > 0}}">
        <view class="pet-card" wx:for="{{pets}}" wx:key="id">
          <image class="pet-image" src="{{item.image || '/assets/images/default-pet.png'}}" mode="aspectFill"></image>
          <view class="pet-info">
            <view class="pet-name-price">
              <text class="pet-name">{{item.name || '未命名宠物'}}</text>
              <text class="pet-price">¥{{item.price || '待询价'}}</text>
            </view>
            <view class="pet-breed">品种：{{item.breed || '未知'}}</view>
            <view class="pet-age">年龄：{{item.age || '未知'}}个月</view>
            <view class="pet-gender">性别：{{item.gender || '未知'}}</view>
            <view class="pet-stock" wx:if="{{item.stock}}">库存：{{item.stock}}只</view>
            <view class="pet-stock out-of-stock" wx:else>库存不足</view>
            <view class="pet-actions">
              <button class="btn-reserve" bindtap="reservePet" data-pet-id="{{item.ID}}" disabled="{{!item.stock}}">预约</button>
            </view>
          </view>
        </view>
      </view>
      <view class="empty-state" wx:else>
        <image class="empty-image" src="/assets/images/empty.png"></image>
        <text class="empty-text">暂无宠物信息</text>
      </view>
      <!-- 加载更多 -->
      <view class="loading-more" wx:if="{{loading && currentTab === 'pets'}}">
        <view class="loading-spinner"></view>
        <text>加载中...</text>
      </view>
      <view class="no-more" wx:if="{{!petHasMore && pets.length > 0}}">
        <text>没有更多宠物了</text>
      </view>
    </view>

    <!-- 商店信息 -->
    <view class="tab-content" wx:if="{{currentTab === 'info'}}">
      <view class="shop-info-detail">
        <view class="info-item">
          <text class="info-label">商店名称</text>
          <text class="info-value">{{shopInfo.name}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">商店类型</text>
          <text class="info-value">{{shopInfo.type}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">联系方式</text>
          <text class="info-value">{{shopInfo.contact || '暂无信息'}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">营业时间</text>
          <text class="info-value">{{shopInfo.businessHours || '暂无信息'}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">店铺地址</text>
          <text class="info-value">{{shopInfo.address}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">商店介绍</text>
          <text class="info-value">{{shopInfo.description}}</text>
        </view>
        <view class="info-item" wx:if="{{shopInfo.services}}">
          <text class="info-label">服务项目</text>
          <text class="info-value">{{shopInfo.services}}</text>
        </view>
        <view class="info-item" wx:if="{{shopInfo.equipment}}">
          <text class="info-label">店铺设施</text>
          <text class="info-value">{{shopInfo.equipment}}</text>
        </view>
      </view>
    </view>

    <!-- 评价列表 -->
    <view class="tab-content" wx:if="{{currentTab === 'evaluations'}}">
      <view class="evaluation-header">
        <view class="evaluation-title-section">
          <text class="evaluation-title">用户评价</text>
          <text class="evaluation-count" wx:if="{{evalTotal > 0}}">共{{evalTotal}}条评价</text>
        </view>
        <button class="btn-evaluate" bindtap="evaluateShop">我要评价</button>
      </view>
      
      <view class="evaluation-list" wx:if="{{evaluations.length > 0}}">
        <view class="evaluation-item" wx:for="{{evaluations}}" wx:key="id">
          <view class="evaluation-user">
            <image class="user-avatar" src="{{item.userAvatar || '/assets/images/default-pet.png'}}" mode="aspectFill"></image>
            <view class="user-info">
              <text class="user-name">{{item.userName || '匿名用户'}}</text>
              <view class="evaluation-rating">
                <view class="rating-stars">
                  <block wx:for="{{5}}" wx:key="index" wx:for-item="star">
                    <image 
                      class="star-icon" 
                      src="{{star <= item.rating ? '/assets/images/heart-filled.png' : '/assets/images/heart.png'}}"
                    ></image>
                  </block>
                </view>
                <text class="rating-value">{{item.rating}}分</text>
              </view>
            </view>
          </view>
          <view class="evaluation-content">{{item.content || '用户未填写评价内容'}}</view>
          <view class="evaluation-time">{{item.createTime || '未知时间'}}</view>
        </view>
      </view>
      
      <view class="empty-state" wx:else>
        <image class="empty-image" src="/assets/images/empty.png"></image>
        <text class="empty-text">暂无评价信息</text>
        <text class="empty-tip">快来成为第一个评价的用户吧~</text>
      </view>
      
      <!-- 加载更多 -->
      <view class="loading-more" wx:if="{{loading && currentTab === 'evaluations'}}">
        <view class="loading-spinner"></view>
        <text>加载中...</text>
      </view>
      <view class="no-more" wx:if="{{!evalHasMore && evaluations.length > 0}}">
        <text>没有更多评价了</text>
      </view>
    </view>
  </view>

  <!-- 评价弹窗 -->
  <view class="evaluation-modal-overlay" wx:if="{{showEvaluationModal}}" bindtap="closeEvaluationModal">
    <view class="evaluation-modal" catchtap="">
      <view class="modal-header">
        <text class="modal-title">评价商店</text>
        <view class="modal-close" bindtap="closeEvaluationModal">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="modal-content">
        <!-- 商店信息 -->
        <view class="shop-info-card">
          <image class="shop-avatar" src="{{shopInfo.image}}" mode="aspectFill"></image>
          <view class="shop-name-text">{{shopInfo.name}}</view>
        </view>
        
        <!-- 评分选择 -->
        <view class="rating-section">
          <text class="section-title">请给商店评分</text>
          <view class="star-rating">
            <block wx:for="{{5}}" wx:key="index" wx:for-item="star">
              <image 
                class="star-item {{star <= evaluationForm.rating ? 'active' : ''}}" 
                src="{{star <= evaluationForm.rating ? '/assets/images/heart-filled.png' : '/assets/images/heart.png'}}"
                bindtap="onRatingChange"
                data-rating="{{star}}"
              ></image>
            </block>
          </view>
          <text class="rating-text">{{evaluationForm.rating > 0 ? evaluationForm.rating + ' 分' : '点击星星评分'}}</text>
        </view>
        
        <!-- 评价内容 - 使用安全的模态框输入方式 -->
        <view class="content-section">
          <text class="section-title">写下您的评价</text>
          <view class="content-display" bindtap="openContentInput">
            <text class="content-placeholder" wx:if="{{!evaluationForm.content}}">点击这里输入评价内容</text>
            <text class="content-text" wx:else>{{evaluationForm.content}}</text>
            <view class="edit-icon">✏️</view>
          </view>
          <view class="content-count">{{evaluationForm.content.length}}/500</view>
        </view>
      </view>
      
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="closeEvaluationModal">取消</button>
        <button 
          class="btn-submit" 
          bindtap="submitEvaluation"
          disabled="{{submittingEvaluation || !evaluationForm.rating || !evaluationForm.content.trim()}}"
        >
          {{submittingEvaluation ? '提交中...' : '提交评价'}}
        </button>
      </view>
    </view>
  </view>

        <!-- 内容输入模态框 -->
        <view class="content-input-modal" wx:if="{{showContentInput}}" catchtap="closeContentInput">
          <view class="content-input-container" catchtap="stopPropagation">
            <view class="input-header">
              <text class="input-title">输入评价内容</text>
              <button class="input-close" bindtap="closeContentInput">×</button>
            </view>
            <view class="input-body">
              <view class="content-options">
                <button class="quick-option" bindtap="selectQuickContent" data-content="服务很好，环境很棒！">服务很好</button>
                <button class="quick-option" bindtap="selectQuickContent" data-content="宠物品种丰富，质量不错！">品种丰富</button>
                <button class="quick-option" bindtap="selectQuickContent" data-content="店主热情，专业度高！">店主专业</button>
                <button class="quick-option" bindtap="selectQuickContent" data-content="价格合理，值得推荐！">价格合理</button>
              </view>
              <view class="or-divider">
                <text>或者自定义评价</text>
              </view>
              <view class="custom-input-area">
                <view class="custom-input" bindtap="focusCustomInput">
                  <text class="custom-placeholder" wx:if="{{!tempContent}}">请输入您的评价...</text>
                  <text class="custom-text" wx:else>{{tempContent}}</text>
                </view>
                <view class="temp-count">{{tempContent.length}}/500</view>
              </view>
            </view>
            <view class="input-footer">
              <button class="btn-input-cancel" bindtap="closeContentInput">取消</button>
              <button class="btn-input-confirm" bindtap="confirmContentInput">确定</button>
            </view>
          </view>
        </view>
</view>