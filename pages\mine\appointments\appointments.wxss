/* pages/mine/appointments/appointments.wxss */

/* 页面容器 */
.appointment-container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 140rpx; /* 为浮动按钮留出空间 */
}

/* 页面头部 */
.page-header {
  margin-bottom: 30rpx;
  padding: 20rpx 0;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.appointment-item {
  margin-bottom: 20rpx;
}

/* 预约卡片 */
.appointment-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.appointment-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

/* 医院信息 */
.hospital-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 25rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  background-color: #f0f0f0;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.animal-status {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

/* 预约编号 */
.reserve-number {
  font-size: 24rpx;
  color: #999;
  line-height: 1.3;
}

/* 预约状态 */
.appointment-status {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20rpx;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-healthy {
  background-color: #e8f5e8;
  color: #2e7d32;
  border: 1rpx solid #81c784;
}

.status-unhealthy {
  background-color: #ffebee;
  color: #c62828;
  border: 1rpx solid #e57373;
}

/* 修复：使用正确的状态样式 */
.status-pending {
  background-color: #fff3e0;
  color: #f57c00;
  border: 1rpx solid #ffcc80;
}

.status-confirmed {
  background-color: #e8f5e8;
  color: #2e7d32;
  border: 1rpx solid #81c784;
}

.status-completed {
  background-color: #e3f2fd;
  color: #1565c0;
  border: 1rpx solid #64b5f6;
}

.status-cancelled {
  background-color: #ffebee;
  color: #c62828;
  border: 1rpx solid #e57373;
}

/* 兼容旧状态名 */
.status-unconfined {
  background-color: #fff3e0;
  color: #f57c00;
  border: 1rpx solid #ffcc80;
}

.status-confined {
  background-color: #e8f5e8;
  color: #2e7d32;
  border: 1rpx solid #81c784;
}

.status-finished {
  background-color: #e3f2fd;
  color: #1565c0;
  border: 1rpx solid #64b5f6;
}

/* 宠物信息 */
.pet-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
  padding: 15rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 8rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  margin-right: 20rpx;
}

/* 预约时间 */
.appointment-time {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 15rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.appointment-id {
  font-size: 24rpx;
  color: #999;
  line-height: 1.3;
}

.time-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.time-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 联系方式 */
.contact-info {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
  padding: 12rpx 15rpx;
  background-color: #e3f2fd;
  border-radius: 8rpx;
}

.contact-label {
  font-size: 26rpx;
  color: #1565c0;
  margin-right: 10rpx;
}

.contact-value {
  font-size: 28rpx;
  color: #1565c0;
  font-weight: 500;
}

/* 操作按钮 */
.appointment-actions {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
}

.cancel-btn, .detail-btn {
  padding: 0 24rpx !important;
  height: 60rpx !important;
  line-height: 60rpx !important;
  border-radius: 30rpx !important;
  font-size: 26rpx !important;
  border: none !important;
}

.cancel-btn {
  background-color: #ffebee !important;
  color: #c62828 !important;
}

.detail-btn {
  background-color: #e3f2fd !important;
  color: #1565c0 !important;
}

.cancel-btn::after, .detail-btn::after {
  border: none;
}

/* 空状态 */
.empty-container {
  margin-top: 100rpx;
  margin-bottom: 100rpx;
}

/* 加载状态 */
.loading-container {
  padding: 20rpx 0;
}

.loading-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8rpx;
}

.hospital-skeleton {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.text-skeleton {
  height: 30rpx;
  width: 70%;
  margin-bottom: 15rpx;
}

.time-skeleton {
  height: 26rpx;
  width: 50%;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 浮动操作按钮 */
.fab-container {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  z-index: 999;
}

.fab-button {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.fab-button::after {
  border: none;
}

.fab-button:active {
  transform: scale(0.9);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.fab-icon {
  font-size: 32rpx;
  font-weight: 300;
  line-height: 1;
  margin-bottom: 4rpx;
}

.fab-text {
  font-size: 20rpx;
  line-height: 1;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .appointment-container {
    padding: 15rpx;
  }
  
  .appointment-card {
    padding: 25rpx;
  }
  
  .user-avatar {
    width: 100rpx;
    height: 100rpx;
  }
  
  .user-name {
    font-size: 30rpx;
  }
  
  .fab-button {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50rpx;
  }
}