import { getRescueStationEvaluations } from '../../services/rescueService';

const params = {
    stationId: 1
};

getRescueStationEvaluations(params)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('救助站评价:', res.data);
        } else {
            wx.showToast({
                title: res.message || '获取救助站评价失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('获取救助站评价出错', err);
        wx.showToast({
            title: '获取救助站评价出错，请重试',
            icon: 'none'
        });
    });