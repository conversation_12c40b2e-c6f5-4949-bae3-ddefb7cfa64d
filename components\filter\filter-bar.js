// components/filter/filter-bar.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 筛选项列表
    filters: {
      type: Array,
      value: []
    },
    // 当前选中的筛选项索引
    activeIndex: {
      type: Number,
      value: -1
    },
    // 是否显示更多筛选按钮
    showMore: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 是否显示更多筛选面板
    showMorePanel: false,
    // 临时选中的值
    tempValues: {},
    // 展开的筛选项索引
    expandedIndex: -1
  },

  /**
   * 数据监听器
   */
  observers: {
    'filters': function(filters) {
      // 初始化临时选中的值
      const tempValues = {};
      filters.forEach(filter => {
        if (filter.multiple) {
          tempValues[filter.key] = filter.value || [];
        } else {
          tempValues[filter.key] = filter.value || '';
        }
      });
      this.setData({
        tempValues
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 点击筛选项
    onClickFilter: function(e) {
      const { index } = e.currentTarget.dataset;
      const { expandedIndex } = this.data;
      
      // 如果当前筛选项已展开，则收起
      if (expandedIndex === index) {
        this.setData({
          expandedIndex: -1
        });
        return;
      }
      
      // 否则展开当前筛选项
      this.setData({
        expandedIndex: index
      });
    },
    
    // 点击筛选项的选项
    onClickOption: function(e) {
      const { filterIndex, optionIndex } = e.currentTarget.dataset;
      const { filters, tempValues, expandedIndex } = this.data;
      const filter = filters[filterIndex];
      
      if (filter.multiple) {
        // 多选
        const values = tempValues[filter.key] || [];
        const value = filter.options[optionIndex].value;
        const index = values.indexOf(value);
        
        if (index > -1) {
          // 已选中，取消选中
          values.splice(index, 1);
        } else {
          // 未选中，添加选中
          values.push(value);
        }
        
        this.setData({
          [`tempValues.${filter.key}`]: values
        });
      } else {
        // 单选
        const value = filter.options[optionIndex].value;
        
        this.setData({
          [`tempValues.${filter.key}`]: value,
          expandedIndex: -1 // 选择后收起
        });
        
        // 单选立即触发筛选
        if (expandedIndex !== -1) {
          this.triggerFilter();
        }
      }
    },
    
    // 点击重置按钮
    onClickReset: function() {
      const { filters } = this.data;
      const tempValues = {};
      
      filters.forEach(filter => {
        if (filter.multiple) {
          tempValues[filter.key] = [];
        } else {
          tempValues[filter.key] = '';
        }
      });
      
      this.setData({
        tempValues
      });
    },
    
    // 点击确定按钮
    onClickConfirm: function() {
      this.setData({
        expandedIndex: -1
      });
      this.triggerFilter();
    },
    
    // 点击更多筛选按钮
    onClickMore: function() {
      this.setData({
        showMorePanel: true
      });
    },
    
    // 点击关闭更多筛选面板
    onCloseMorePanel: function() {
      this.setData({
        showMorePanel: false
      });
    },
    
    // 点击更多筛选面板的确定按钮
    onConfirmMorePanel: function() {
      this.setData({
        showMorePanel: false
      });
      this.triggerFilter();
    },
    
    // 触发筛选事件
    triggerFilter: function() {
      const { filters, tempValues } = this.data;
      const values = {};
      
      filters.forEach(filter => {
        values[filter.key] = tempValues[filter.key];
      });
      
      this.triggerEvent('filter', { values });
    },
    
    // 阻止滚动穿透
    preventScroll: function() {
      return false;
    }
  }
})