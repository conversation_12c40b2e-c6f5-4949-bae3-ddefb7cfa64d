/**
 * 通用工具函数
 */

/**
 * 格式化时间
 * @param {Date} date - 日期对象
 * @param {string} format - 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的时间字符串
 */
function formatTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) {
    return '';
  }
  
  if (typeof date === 'string') {
    date = new Date(date.replace(/-/g, '/'));
  }
  
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();
  
  return format
    .replace(/YYYY/g, year)
    .replace(/MM/g, padZero(month))
    .replace(/DD/g, padZero(day))
    .replace(/HH/g, padZero(hour))
    .replace(/mm/g, padZero(minute))
    .replace(/ss/g, padZero(second));
}

/**
 * 补零
 * @param {number} n - 数字
 * @returns {string} 补零后的字符串
 */
function padZero(n) {
  return n < 10 ? '0' + n : '' + n;
}

/**
 * 获取当前页面路径
 * @returns {string} 当前页面路径
 */
function getCurrentPageUrl() {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  return currentPage.route;
}

/**
 * 获取当前页面参数
 * @returns {Object} 当前页面参数
 */
function getCurrentPageOptions() {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  return currentPage.options;
}

/**
 * 防抖函数
 * @param {Function} func - 要执行的函数
 * @param {number} wait - 等待时间
 * @param {boolean} immediate - 是否立即执行
 * @returns {Function} 防抖处理后的函数
 */
function debounce(func, wait = 300, immediate = false) {
  let timeout;
  return function() {
    const context = this;
    const args = arguments;
    
    const later = function() {
      timeout = null;
      if (!immediate) func.apply(context, args);
    };
    
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func.apply(context, args);
  };
}

/**
 * 节流函数
 * @param {Function} func - 要执行的函数
 * @param {number} wait - 等待时间
 * @returns {Function} 节流处理后的函数
 */
function throttle(func, wait = 300) {
  let timeout;
  let lastTime = 0;
  
  return function() {
    const context = this;
    const args = arguments;
    const now = Date.now();
    
    if (now - lastTime >= wait) {
      lastTime = now;
      func.apply(context, args);
    } else {
      clearTimeout(timeout);
      timeout = setTimeout(function() {
        lastTime = now;
        func.apply(context, args);
      }, wait - (now - lastTime));
    }
  };
}

/**
 * 显示成功提示
 * @param {string} message - 提示信息
 * @param {number} duration - 显示时长，单位为毫秒
 */
function showSuccess(message, duration = 1500) {
  wx.showToast({
    title: message,
    icon: 'success',
    duration
  });
}

/**
 * 显示错误提示
 * @param {string} message - 提示信息
 * @param {number} duration - 显示时长，单位为毫秒
 */
function showError(message, duration = 1500) {
  wx.showToast({
    title: message,
    icon: 'none',
    duration
  });
}

/**
 * 显示加载提示
 * @param {string} message - 提示信息
 */
function showLoading(message = '加载中') {
  wx.showLoading({
    title: message,
    mask: true
  });
}

/**
 * 隐藏加载提示
 */
function hideLoading() {
  wx.hideLoading();
}

/**
 * 显示模态框
 * @param {Object} options - 选项
 * @param {string} options.title - 标题
 * @param {string} options.content - 内容
 * @param {string} options.confirmText - 确认按钮文字
 * @param {string} options.cancelText - 取消按钮文字
 * @param {boolean} options.showCancel - 是否显示取消按钮
 * @returns {Promise} 返回选择结果的 Promise
 */
function showModal({
  title = '提示',
  content,
  confirmText = '确定',
  cancelText = '取消',
  showCancel = true
}) {
  return new Promise((resolve, reject) => {
    wx.showModal({
      title,
      content,
      confirmText,
      cancelText,
      showCancel,
      success: res => {
        if (res.confirm) {
          resolve(true);
        } else if (res.cancel) {
          resolve(false);
        }
      },
      fail: reject
    });
  });
}

/**
 * 生成随机字符串
 * @param {number} length - 字符串长度
 * @returns {string} 随机字符串
 */
function randomString(length = 16) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 生成UUID
 * @returns {string} UUID
 */
function uuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 格式化文件大小
 * @param {number} size - 文件大小，单位为字节
 * @returns {string} 格式化后的文件大小
 */
function formatFileSize(size) {
  if (size < 1024) {
    return size + 'B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + 'KB';
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + 'MB';
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
  }
}

/**
 * 获取文件扩展名
 * @param {string} filename - 文件名
 * @returns {string} 文件扩展名
 */
function getFileExtension(filename) {
  return filename.slice((Math.max(0, filename.lastIndexOf('.')) || Infinity) + 1);
}

/**
 * 深拷贝对象
 * @param {Object} obj - 要拷贝的对象
 * @returns {Object} 拷贝后的对象
 */
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item));
  }
  
  if (obj instanceof Object) {
    const copy = {};
    Object.keys(obj).forEach(key => {
      copy[key] = deepClone(obj[key]);
    });
    return copy;
  }
  
  throw new Error('Unable to copy obj! Its type isn\'t supported.');
}

export default {
  formatTime,
  padZero,
  getCurrentPageUrl,
  getCurrentPageOptions,
  debounce,
  throttle,
  showSuccess,
  showError,
  showLoading,
  hideLoading,
  showModal,
  randomString,
  uuid,
  formatFileSize,
  getFileExtension,
  deepClone
};