// pages/adopt/detail/detail.js
import animalService from '../../../services/animalService';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 动物ID
    animalId: null,
    // 动物详情
    animal: {},
    // 是否正在加载
    isLoading: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    // 获取传递的参数
    const { id } = options;
    
    if (id) {
      this.setData({ animalId: id });
      // 获取动物详情
      this.getAnimalDetail(id);
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        this.navigateBack();
      }, 1500);
    }
  },

  /**
   * 获取动物详情
   * @param {string} animalId - 动物ID
   */
  getAnimalDetail: function(animalId) {
    // 显示加载中
    this.setData({ isLoading: true });
    
    // 调用服务获取动物详情
    animalService.getAnimalDetail(animalId)
      .then(res => {
        console.log('获取动物详情成功:', res);
        
        if (res && res.data) {
          // 处理返回数据，适配新的数据结构
          const animalData = res.data;
          const processedAnimal = {
            id: animalData.ID,
            name: this.generatePetName(animalData.breed, animalData.gender),
            type: this.mapBreedToType(animalData.breed),
            breed: animalData.breed,
            age: this.calculateAge(animalData.birthDate),
            gender: animalData.gender === '雄' ? '公' : '母',
            // 拼接图片路径
            image: animalData.photo ? `D:/images${animalData.photo}` : '/assets/images/default-pet.png',
            status: animalData.status === '未领养' ? '待领养' : animalData.status,
            location: '详细地址待获取', // 可以根据用户地址或者接口返回
            description: this.generateDescription(animalData),
            medicalRecord: animalData.medicalRecord,
            birthDate: animalData.birthDate,
            source: animalData.source,
            // 医疗相关信息
            vaccine: '已完成基础疫苗接种',
            sterilization: animalData.gender === '雄' ? '已绝育' : '已绝育',
            deworming: '已完成体内外驱虫',
            // 救助站信息
            rescueStation: '爱心救助站',
            stationAddress: '地址信息待获取',
            stationPhone: '010-12345678'
          };
          
          this.setData({
            animal: processedAnimal,
            isLoading: false
          });
        } else {
          // 无数据时使用模拟数据
          this.loadMockAnimalDetail(animalId);
        }
      })
      .catch(err => {
        console.error('获取动物详情失败', err);
        // 接口失败时使用模拟数据
        this.loadMockAnimalDetail(animalId);
      });
  },

  /**
   * 根据品种映射动物类型
   */
  mapBreedToType: function(breed) {
    const dogBreeds = ['金毛', '萨摩耶', '柯基', '泰迪', '哈士奇', '边牧', '拉布拉多'];
    const catBreeds = ['英短', '美短', '橘猫', '蓝猫', '布偶', '波斯猫'];
    
    if (dogBreeds.some(dog => breed.includes(dog))) {
      return '狗狗';
    } else if (catBreeds.some(cat => breed.includes(cat))) {
      return '猫咪';
    } else {
      return '其他';
    }
  },

  /**
   * 根据品种和性别生成宠物名字
   */
  generatePetName: function(breed, gender) {
    const maleNames = ['小白', '旺财', '大黄', '豆豆', '球球', '毛毛'];
    const femaleNames = ['咪咪', '花花', '美美', '甜甜', '妞妞', '可可'];
    
    const names = gender === '雄' ? maleNames : femaleNames;
    return names[Math.floor(Math.random() * names.length)];
  },

  /**
   * 根据出生日期计算年龄
   */
  calculateAge: function(birthDate) {
    if (!birthDate) return '未知';
    
    const birth = new Date(birthDate);
    const now = new Date();
    const ageInMonths = (now.getFullYear() - birth.getFullYear()) * 12 + (now.getMonth() - birth.getMonth());
    
    if (ageInMonths < 12) {
      return `${ageInMonths}个月`;
    } else {
      const years = Math.floor(ageInMonths / 12);
      const months = ageInMonths % 12;
      return months > 0 ? `${years}岁${months}个月` : `${years}岁`;
    }
  },

  /**
   * 根据动物数据生成描述
   */
  generateDescription: function(animalData) {
    const typeMap = {
      '金毛': '金毛是一只温顺友善的狗狗，非常聪明且容易训练，适合家庭饲养。',
      '萨摩耶': '萨摩耶拥有洁白的毛发和温和的性格，是优秀的伴侣犬。',
      '英短': '英短猫性格沉稳，适应能力强，是理想的室内宠物。',
      '美短': '美短猫活泼可爱，与人亲近，是很好的家庭伴侣。'
    };
    
    const defaultDesc = '这是一只可爱的小宠物，它期待着一个温暖的家。';
    const breedDesc = typeMap[animalData.breed] || defaultDesc;
    const medicalInfo = animalData.medicalRecord ? `医疗记录：${animalData.medicalRecord}。` : '';
    
    return `${breedDesc}${medicalInfo}如果你喜欢它，请联系救助站了解更多信息，或者直接申请领养。`;
  },

  /**
   * 加载模拟动物详情数据
   */
  loadMockAnimalDetail: function(animalId) {
    const mockAnimal = {
      id: animalId,
      name: '小白',
      type: '狗狗',
      breed: '萨摩耶',
      age: '2岁',
      gender: '公',
      image: '/assets/images/default-pet.png',
      status: '待领养',
      location: '北京市朝阳区',
      description: '小白是一只活泼可爱的萨摩耶，性格温顺友善，非常喜欢与人互动。它非常聪明，已经学会了基本的指令如坐下、握手等。小白非常适合有小孩的家庭，也适合有足够活动空间的家庭领养。',
      vaccine: '已完成全部疫苗接种',
      sterilization: '已绝育',
      deworming: '已完成体内外驱虫',
      medicalRecord: '无过往病史，身体健康',
      birthDate: '2022-01-01',
      rescueStation: '爱心救助站',
      stationAddress: '北京市朝阳区XX路XX号',
      stationPhone: '010-12345678'
    };
    
    this.setData({
      animal: mockAnimal,
      isLoading: false
    });

    // 如果是从接口失败切换到模拟数据，显示提示
    if (this.data.isLoading) {
      wx.showToast({
        title: '已切换到离线模式',
        icon: 'none'
      });
    }
  },

  /**
   * 返回上一页
   */
  navigateBack: function() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 申请领养
   */
  applyAdopt: function() {
    const { animalId } = this.data;
    
    // 添加轻微振动反馈
    if (wx.vibrateShort) {
      wx.vibrateShort({
        type: 'light'
      });
    }
    
    // 跳转到申请页面
    wx.navigateTo({
      url: `/pages/adopt/apply/apply?id=${animalId}`
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {
    // 页面渲染完成
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 页面显示时可以刷新数据
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
    // 页面隐藏
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
    // 页面卸载
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {
    // 刷新数据
    const { animalId } = this.data;
    if (animalId) {
      this.getAnimalDetail(animalId);
    }
    
    // 停止下拉刷新
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 500);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    // 详情页不需要实现
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {
    const { animal } = this.data;
    
    return {
      title: `【待领养】${animal.name} - ${animal.breed}`,
      path: `/pages/adopt/detail/detail?id=${animal.id}`,
      imageUrl: animal.image || '/assets/images/default-pet.png'
    };
  }
})