import { register } from '../../services/userService';

const userData = {
    account: 'testuser',
    password: 'testpassword',
    // 其他注册所需字段
};

register(userData)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            wx.showToast({
                title: '注册成功',
                icon: 'success'
            });
        } else {
            wx.showToast({
                title: res.message || '注册失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('注册出错', err);
        wx.showToast({
            title: '注册出错，请重试',
            icon: 'none'
        });
    });