.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 标签页 */
.tabs {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0 20rpx;
  position: relative;
}

.tab-text {
  font-size: 30rpx;
  color: #999;
  transition: color 0.3s;
}

.tab-item.active .tab-text {
  color: #FF6F61;
  font-weight: bold;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  width: 60rpx;
  height: 4rpx;
  background-color: #FF6F61;
  border-radius: 2rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6F61;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 预约列表 */
.reservation-list {
  padding: 20rpx;
}

.reservation-item {
  margin-bottom: 30rpx;
}

.reservation-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 25rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.hospital-info, .shop-info {
  flex: 1;
}

.hospital-name, .shop-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.reserve-number, .order-number {
  font-size: 24rpx;
  color: #999;
}

.status {
  color: white;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  white-space: nowrap;
}

/* 卡片内容 */
.card-content {
  margin-bottom: 25rpx;
}

.info-row {
  display: flex;
  margin-bottom: 12rpx;
  align-items: flex-start;
}

.label {
  font-size: 28rpx;
  color: #666;
  min-width: 160rpx;
  flex-shrink: 0;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  line-height: 1.4;
}

/* 宠物信息 */
.pet-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
}

.pet-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.pet-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.pet-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.pet-breed {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.pet-price {
  font-size: 28rpx;
  color: #FF6F61;
  font-weight: bold;
}

/* 操作按钮 */
.card-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-btn {
  background-color: #4CAF50;
  color: white;
}

.contact-btn:active {
  background-color: #45a049;
}

.cancel-btn {
  background-color: #FF6F61;
  color: white;
}

.cancel-btn:active {
  background-color: #e55a4e;
}

.detail-btn {
  background-color: #f0f0f0;
  color: #333;
}

.detail-btn:active {
  background-color: #e0e0e0;
}

/* 加载更多和没有更多 */
.loading-more, .no-more {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 150rpx 40rpx;
}

.empty-image {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 30rpx;
  color: #999;
  margin-bottom: 50rpx;
}

.goto-btn {
  width: 300rpx;
  height: 70rpx;
  background: linear-gradient(135deg, #FF6F61 0%, #FF8A75 100%);
  color: white;
  border-radius: 35rpx;
  font-size: 28rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(255, 111, 97, 0.3);
}

.goto-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 111, 97, 0.3);
}