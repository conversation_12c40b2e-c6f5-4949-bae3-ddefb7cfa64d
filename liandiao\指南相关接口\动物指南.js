import { getAnimalGuide } from '../../services/guideService';

getAnimalGuide()
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('动物指南:', res.data);
        } else {
            wx.showToast({
                title: res.message || '获取动物指南失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('获取动物指南出错', err);
        wx.showToast({
            title: '获取动物指南出错，请重试',
            icon: 'none'
        });
    });