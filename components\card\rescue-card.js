// components/card/rescue-card.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 救助站数据
    rescue: {
      type: Object,
      value: {}
    },
    // 显示模式：compact(紧凑), normal(普通), detailed(详细)
    mode: {
      type: String,
      value: 'normal'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 默认图片
    defaultImage: '/assets/images/default-rescue.png'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 点击卡片
    onTapCard: function() {
      const { rescue } = this.properties;
      this.triggerEvent('tap', { rescueId: rescue.id });
    },
    
    // 点击收藏按钮
    onTapFavorite: function(e) {
      e.stopPropagation(); // 阻止事件冒泡
      const { rescue } = this.properties;
      this.triggerEvent('favorite', { rescueId: rescue.id, isFavorite: !rescue.isFavorite });
    },
    
    // 点击电话按钮
    onTapPhone: function(e) {
      e.stopPropagation(); // 阻止事件冒泡
      const { rescue } = this.properties;
      if (rescue.phone) {
        wx.makePhoneCall({
          phoneNumber: rescue.phone,
          fail: (err) => {
            console.error('拨打电话失败:', err);
            wx.showToast({
              title: '拨打电话失败',
              icon: 'none'
            });
          }
        });
      } else {
        wx.showToast({
          title: '无电话号码',
          icon: 'none'
        });
      }
    },
    
    // 点击导航按钮
    onTapNavigation: function(e) {
      e.stopPropagation(); // 阻止事件冒泡
      const { rescue } = this.properties;
      if (rescue.latitude && rescue.longitude) {
        wx.openLocation({
          latitude: rescue.latitude,
          longitude: rescue.longitude,
          name: rescue.name,
          address: rescue.address
        });
      } else {
        wx.showToast({
          title: '无位置信息',
          icon: 'none'
        });
      }
    },
    
    // 点击捐赠按钮
    onTapDonate: function(e) {
      e.stopPropagation(); // 阻止事件冒泡
      const { rescue } = this.properties;
      this.triggerEvent('donate', { rescueId: rescue.id });
    },
    
    // 图片加载失败
    onImageError: function() {
      this.setData({
        'rescue.imageUrl': this.data.defaultImage
      });
    }
  }
})