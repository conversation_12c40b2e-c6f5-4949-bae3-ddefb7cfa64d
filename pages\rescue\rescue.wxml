<!--pages/rescue/rescue.wxml-->
<view class="container">
  <!-- 头部搜索区域 -->
  <view class="header">
    <view class="title-section">
      <text class="page-title">救助站</text>
      <text class="page-subtitle">为流浪动物寻找温暖的家</text>
      <text class="location-text">📍 当前位置：{{currentAddress}}</text>
    </view>
    
    <!-- 搜索按钮 -->
    <view class="search-section">
      <view wx:if="{{!showSearch}}" class="search-btn" bindtap="showSearchInput">
        🔍
      </view>
      <view wx:else class="search-input-container">
        <input 
          class="search-input"
          placeholder="搜索救助站..."
          value="{{searchQuery}}"
          bindinput="onSearchInput"
          bindconfirm="performSearch"
        />
        <view class="search-actions">
          <view class="search-confirm" bindtap="performSearch">搜索</view>
          <view class="search-cancel" bindtap="hideSearchInput">取消</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions">
    <view class="action-item" bindtap="viewMyEvaluations">
      <text class="action-icon">📝</text>
      <text class="action-text">我的评价</text>
    </view>
  </view>

  <!-- 骨架屏 -->
  <view wx:if="{{loading && rescueStations.length === 0}}" class="skeleton-container">
    <view wx:for="{{[1,2,3]}}" wx:key="*this" class="skeleton-item">
      <view class="skeleton-image"></view>
      <view class="skeleton-content">
        <view class="skeleton-title"></view>
        <view class="skeleton-text"></view>
        <view class="skeleton-text short"></view>
      </view>
    </view>
  </view>

  <!-- 救助站列表 -->
  <view wx:else class="stations-list">
    <view wx:if="{{rescueStations.length === 0 && !loading}}" class="empty-container">
      <image class="empty-image" src="/assets/images/empty-rescue.png" mode="aspectFit" />
      <text class="empty-text">暂无救助站信息</text>
      <text class="empty-hint">试试搜索其他地区</text>
    </view>
    
    <view wx:else>
      <view 
        wx:for="{{rescueStations}}" 
        wx:key="id" 
        class="station-item"
        data-id="{{item.id}}"
        bindtap="goToDetail"
      >
        <!-- 救助站卡片 -->
        <view class="station-card">
          <!-- 救助站图片 -->
          <image 
            class="station-photo" 
            src="{{item.photo}}" 
            mode="aspectFill"
            lazy-load="{{true}}"
          />
          
          <!-- 救助站信息 -->
          <view class="station-info">
            <view class="station-header">
              <text class="station-name">{{item.name}}</text>
              <view class="station-rating">
                <text class="rating-stars">{{item.rating}}⭐</text>
                <text class="distance">{{item.distance}}</text>
              </view>
            </view>
            
            <text class="station-address">📍 {{item.address}}</text>
            <text class="station-description">{{item.description}}</text>
            
            <!-- 收容情况 -->
            <view class="capacity-info">
              <text class="capacity-text">收容情况：{{item.currentAnimals}}/{{item.capacity}}</text>
              <view class="capacity-bar">
                <view 
                  class="capacity-fill" 
                  style="width: {{(item.currentAnimals / item.capacity) * 100+'%'}}"
                ></view>
              </view>
            </view>
            
            <!-- 许可证信息 -->
            <text wx:if="{{item.license}}" class="license-text">许可证：{{item.license}}</text>
          </view>
          
          <!-- 操作按钮 -->
          <view class="station-actions">
            <view 
              class="action-btn phone-btn"
              data-phone="{{item.phone}}"
              catchtap="makePhoneCall"
            >
              📞
            </view>
            <view 
              class="action-btn nav-btn"
              data-name="{{item.name}}"
              data-address="{{item.address}}"
              catchtap="navigateToStation"
            >
              🧭
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view wx:if="{{rescueStations.length > 0 && hasMore}}" class="load-more">
    <text wx:if="{{loading}}">正在加载更多...</text>
    <text wx:else>上拉加载更多</text>
  </view>

  <!-- 没有更多 -->
  <view wx:if="{{rescueStations.length > 0 && !hasMore}}" class="no-more">
    <text>没有更多救助站了</text>
  </view>

  <!-- 刷新提示 -->
  <view wx:if="{{refreshing}}" class="refresh-hint">
    <text>正在刷新...</text>
  </view>
</view>