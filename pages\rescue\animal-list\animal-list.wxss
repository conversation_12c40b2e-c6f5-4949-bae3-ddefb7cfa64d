/* pages/rescue/animal-list/animal-list.wxss */

.container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 40rpx;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #FF6F61 0%, #FF8A75 100%);
  padding: 40rpx 30rpx;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  flex: 1;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.page-subtitle {
  display: block;
  font-size: 24rpx;
  opacity: 0.9;
}

.header-actions {
  display: flex;
  gap: 15rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70rpx;
  height: 70rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.2s;
}

.action-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.action-icon {
  font-size: 28rpx;
}

/* 筛选栏 */
.filter-bar {
  background: white;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
  transition: all 0.3s;
}

.filter-bar.show-search {
  padding: 30rpx;
}

.search-container {
  margin-bottom: 20rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 25rpx;
  padding: 0 20rpx;
  height: 70rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.search-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FF6F61;
  border-radius: 50%;
  margin-left: 10rpx;
}

.search-btn:active {
  background: #e55a4e;
  transform: scale(0.95);
}

.search-icon {
  color: white;
  font-size: 24rpx;
}

.filter-container {
  display: flex;
  justify-content: flex-end;
}

.status-filter {
  background: #f8f9fa;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
}

.filter-content {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.filter-text {
  font-size: 26rpx;
  color: #666;
}

.filter-arrow {
  font-size: 18rpx;
  color: #999;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6F61;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
  margin-bottom: 10rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error-container {
  display: flex;
  justify-content: center;
  padding: 100rpx 30rpx;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  line-height: 1.5;
}

.retry-btn {
  background: #FF6F61;
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 25rpx;
  font-size: 28rpx;
}

.retry-btn:active {
  background: #e55a4e;
  transform: scale(0.95);
}

/* 空数据状态 */
.empty-container {
  display: flex;
  justify-content: center;
  padding: 100rpx 30rpx;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-icon {
  font-size: 100rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.empty-action {
  background: linear-gradient(135deg, #FF6F61 0%, #FF8A75 100%);
  color: white;
  padding: 25rpx 50rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(255, 111, 97, 0.3);
}

.empty-action:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(255, 111, 97, 0.4);
}

/* 动物列表 */
.animal-list {
  padding: 20rpx;
}

.animal-card {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s;
}

.animal-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

/* 动物照片 */
.animal-photo {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}

.photo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 50%);
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding: 20rpx;
}

.animal-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
  color: white;
  backdrop-filter: blur(10rpx);
}

.status-available {
  background: rgba(40, 167, 69, 0.9);
}

.status-adopted {
  background: rgba(108, 117, 125, 0.9);
}

.status-treatment {
  background: rgba(255, 193, 7, 0.9);
}

.status-quarantine {
  background: rgba(255, 111, 97, 0.9);
}

.status-euthanized {
  background: rgba(220, 53, 69, 0.9);
}

.status-default {
  background: rgba(108, 117, 125, 0.9);
}

/* 动物信息 */
.animal-info {
  padding: 30rpx;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.animal-breed {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.animal-gender {
  display: flex;
  align-items: center;
  gap: 5rpx;
  padding: 8rpx 12rpx;
  border-radius: 15rpx;
  font-size: 22rpx;
}

.gender-male {
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
}

.gender-female {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.gender-icon {
  font-size: 24rpx;
}

.info-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.info-label {
  color: #666;
  min-width: 80rpx;
}

.info-value {
  color: #333;
  flex: 1;
}

.medical-record {
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-top: 15rpx;
}

.record-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.record-content {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

/* 操作按钮 */
.animal-actions {
  display: flex;
  justify-content: flex-end;
  padding: 0 30rpx 30rpx;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 15rpx 25rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  transition: all 0.2s;
}

.edit-btn {
  background: rgba(255, 111, 97, 0.1);
  color: #FF6F61;
}

.edit-btn:active {
  background: rgba(255, 111, 97, 0.2);
  transform: scale(0.95);
}

.action-button .action-icon {
  font-size: 20rpx;
}

.action-button .action-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 分页加载 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100rpx;
  background: white;
  border-radius: 15rpx;
  margin: 20rpx;
  transition: all 0.2s;
}

.load-more:active {
  background: #f8f9fa;
  transform: scale(0.98);
}

.load-more-text {
  font-size: 28rpx;
  color: #666;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx;
  gap: 15rpx;
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
  background-color: #f8f9fa;
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .page-header {
    padding: 30rpx 20rpx;
  }
  
  .page-title {
    font-size: 32rpx;
  }
  
  .animal-list {
    padding: 15rpx;
  }
  
  .animal-card {
    margin-bottom: 15rpx;
  }
  
  .animal-info {
    padding: 25rpx 20rpx;
  }
  
  .animal-breed {
    font-size: 28rpx;
  }
  
  .info-item {
    font-size: 24rpx;
  }
  
  .animal-actions {
    padding: 0 20rpx 25rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .container {
    background-color: #1a1a1a;
  }
  
  .filter-bar {
    background: #2d2d2d;
    border-bottom-color: #444;
  }
  
  .search-box {
    background: #3d3d3d;
  }
  
  .search-input {
    color: #fff;
  }
  
  .status-filter {
    background: #3d3d3d;
  }
  
  .filter-text {
    color: #ccc;
  }
  
  .animal-card {
    background: #2d2d2d;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
  }
  
  .animal-breed,
  .info-value,
  .record-content {
    color: #fff;
  }
  
  .info-label,
  .record-label {
    color: #999;
  }
  
  .medical-record {
    background: #3d3d3d;
  }
  
  .load-more {
    background: #2d2d2d;
  }
  
  .load-more:active {
    background: #3d3d3d;
  }
  
  .loading-text,
  .load-more-text {
    color: #ccc;
  }
  
  .error-text {
    color: #999;
  }
  
  .empty-title {
    color: #fff;
  }
  
  .empty-desc {
    color: #999;
  }
}