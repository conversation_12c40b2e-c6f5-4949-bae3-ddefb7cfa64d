/* components/card/rescue-card.wxss */

/* 通用卡片样式 */
.rescue-card {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 紧凑模式 */
.rescue-card-compact {
  width: 100%;
  height: 240rpx;
  display: flex;
  flex-direction: column;
}

.rescue-image-compact {
  width: 100%;
  height: 180rpx;
}

.rescue-info-compact {
  padding: 8rpx 12rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.rescue-name-compact {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}

.rescue-category-compact {
  font-size: 22rpx;
  color: #666;
  margin-top: 4rpx;
}

/* 普通模式 */
.rescue-card-normal {
  display: flex;
  padding: 20rpx;
}

.rescue-image-normal {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.rescue-info-normal {
  margin-left: 20rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.rescue-name-normal {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.rescue-rating-normal {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.rating-stars {
  display: flex;
  align-items: center;
}

.star-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}

.rating-value {
  font-size: 24rpx;
  color: #ff9800;
  margin-left: 8rpx;
}

.rating-count {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}

.rescue-address-normal {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.rescue-animal-count {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.animal-count-text {
  font-size: 24rpx;
  color: #666;
}

.animal-count-value {
  font-size: 24rpx;
  color: #4eaaa8;
  font-weight: 500;
}

.rescue-actions-normal {
  display: flex;
  margin-top: auto;
}

.rescue-action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
}

.action-icon {
  width: 36rpx;
  height: 36rpx;
  margin-bottom: 4rpx;
}

.action-text {
  font-size: 22rpx;
  color: #666;
}

/* 详细模式 */
.rescue-card-detailed {
  display: flex;
  flex-direction: column;
}

.rescue-image-detailed {
  width: 100%;
  height: 300rpx;
}

.rescue-info-detailed {
  padding: 24rpx;
}

.rescue-header-detailed {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
}

.rescue-name-detailed {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.rescue-rating-detailed {
  display: flex;
  align-items: center;
}

.rescue-meta-detailed {
  margin-bottom: 20rpx;
}

.rescue-meta-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.meta-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.meta-text {
  font-size: 28rpx;
  color: #666;
}

.rescue-description-detailed {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 24rpx;
}

.rescue-animal-types {
  margin-bottom: 24rpx;
}

.rescue-animal-types-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
}

.rescue-animal-types-list {
  display: flex;
  flex-wrap: wrap;
}

.rescue-animal-type-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 48rpx;
  padding: 0 16rpx;
  background-color: #f5f5f5;
  color: #666;
  font-size: 24rpx;
  border-radius: 24rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

.rescue-actions-detailed {
  display: flex;
  justify-content: space-between;
}

.rescue-action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  margin: 0 10rpx;
}

.rescue-action-button:first-child {
  margin-left: 0;
}

.rescue-action-button:last-child {
  margin-right: 0;
}

.rescue-action-button.primary {
  background-color: #4eaaa8;
}

.rescue-action-button.primary .action-text {
  color: #fff;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-secondary {
  color: #666;
}