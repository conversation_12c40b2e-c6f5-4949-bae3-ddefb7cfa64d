/* pages/mine/settings/settings.wxss */.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

/* 分组标题 */
.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  padding: 30rpx 30rpx 20rpx;
  position: relative;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 28rpx;
  background: #FF6F61;
  border-radius: 3rpx;
}

/* 设置项 */
.setting-item {
  background-color: #fff;
  padding: 30rpx;
  margin: 0 20rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
}

.setting-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

.setting-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.setting-icon {
  font-size: 36rpx;
  margin-right: 24rpx;
  min-width: 36rpx;
}

.setting-text {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.setting-label {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 6rpx;
  font-weight: 500;
}

.setting-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.3;
}

.setting-action {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}

.arrow {
  font-size: 28rpx;
  color: #ccc;
  margin-left: 10rpx;
}

/* 头像预览 */
.avatar-preview {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  border: 2rpx solid #f0f0f0;
}

/* 编辑模式 */
.edit-mode {
  background-color: #fff;
  margin: 0 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.edit-form {
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #fafafa;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #FF6F61;
  background-color: #fff;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background-color: #fafafa;
  box-sizing: border-box;
  resize: none;
}

.form-textarea:focus {
  border-color: #FF6F61;
  background-color: #fff;
}

/* 编辑操作按钮 */
.edit-actions {
  display: flex;
  gap: 20rpx;
}

.cancel-btn, .save-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #666;
}

.cancel-btn:active {
  background-color: #e0e0e0;
}

.save-btn {
  background: linear-gradient(135deg, #FF6F61 0%, #FF8A75 100%);
  color: white;
}

.save-btn:active {
  background: linear-gradient(135deg, #e55a4e 0%, #e67763 100%);
}

.save-btn[loading] {
  opacity: 0.8;
}

/* 信息显示模式 */
.info-display .setting-item {
  margin: 0;
  margin-bottom: 2rpx;
  border-radius: 0;
  box-shadow: none;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-display .setting-item:first-child {
  border-radius: 16rpx 16rpx 0 0;
}

.info-display .setting-item:last-child {
  border-radius: 0 0 16rpx 16rpx;
  border-bottom: none;
  margin-bottom: 0;
}

/* Switch 开关样式优化 */
switch {
  transform: scale(0.8);
}

/* 版本信息 */
.version-info {
  text-align: center;
  padding: 60rpx 40rpx 40rpx;
  margin-top: 40rpx;
}

.version-text {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.build-text {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.update-text {
  display: block;
  font-size: 24rpx;
  color: #999;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .setting-item {
    padding: 25rpx;
    margin: 0 15rpx;
  }
  
  .setting-icon {
    font-size: 32rpx;
    margin-right: 20rpx;
  }
  
  .setting-label {
    font-size: 28rpx;
  }
  
  .setting-desc {
    font-size: 22rpx;
  }
}

/* 深色模式适配（如果需要） */
@media (prefers-color-scheme: dark) {
  .container {
    background-color: #1a1a1a;
  }
  
  .setting-item {
    background-color: #2a2a2a;
  }
  
  .setting-label {
    color: #ffffff;
  }
  
  .setting-desc {
    color: #cccccc;
  }
  
  .form-input, .form-textarea {
    background-color: #3a3a3a;
    border-color: #4a4a4a;
    color: #ffffff;
  }
}

/* 动画效果 */
.setting-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 特殊状态 */
.setting-item.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background-color: white;
  padding: 40rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6F61;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}