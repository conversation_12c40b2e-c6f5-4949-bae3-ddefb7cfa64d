<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading && orders.length === 0}}" class="loading-container">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 内容区域 -->
  <view wx:else>
    <!-- 空状态 -->
    <view wx:if="{{orders.length === 0 && !loading}}" class="empty-container">
      <image class="empty-image" src="/assets/images/empty.png" mode="aspectFit" />
      <text class="empty-text">暂无预购订单</text>
      <button class="retry-btn" bindtap="retryLoad">重新加载</button>
    </view>
    
    <!-- 订单列表 -->
    <view wx:else class="order-list">
      <!-- 刷新提示 -->
      <view wx:if="{{isRefreshing}}" class="refresh-tip">
        <text>刷新中...</text>
      </view>
      
      <!-- 订单项目 -->
      <view wx:for="{{orders}}" wx:key="id" class="order-item" bindtap="viewOrderDetail" data-id="{{item.id}}" data-order="{{item}}">
        <!-- 订单头部 -->
        <view class="order-header">
          <text class="order-id">订单号: {{item.id}}</text>
          <view class="status-container">
            <text class="order-status status-{{item.statusClass}}">{{item.status}}</text>
          </view>
        </view>
        
        <!-- 订单内容 -->
        <view class="order-content">
          <image 
            class="order-image" 
            src="{{item.petImage || '/assets/images/default-pet.png'}}" 
            mode="aspectFill" 
            lazy-load="{{true}}"
          />
          <view class="order-info">
            <text class="pet-name">{{item.petName}}</text>
            <text class="pet-type">品种: {{item.petType}}</text>
            <text class="order-time">下单时间: {{item.orderTime}}</text>
            <text class="order-price">金额: ¥{{item.price}}</text>
            <text wx:if="{{item.address}}" class="order-address">地址: {{item.address}}</text>
          </view>
        </view>
        
        <!-- 订单操作按钮 -->
        <view class="order-actions">
          <!-- 根据状态显示不同操作 -->
          <block wx:if="{{item.status === '待支付'}}">
            <button class="action-btn cancel-btn" bindtap="cancelOrder" data-id="{{item.id}}" catchtap="true">取消订单</button>
            <button class="action-btn update-btn" bindtap="updateOrderStatus" data-id="{{item.id}}" data-status="已支付" catchtap="true">标记已支付</button>
            <button class="action-btn pay-btn">去支付</button>
          </block>
          <block wx:elif="{{item.status === '已支付'}}">
            <button class="action-btn update-btn" bindtap="updateOrderStatus" data-id="{{item.id}}" data-status="已完成" catchtap="true">标记完成</button>
            <button class="action-btn info-btn" disabled>已支付</button>
          </block>
          <block wx:elif="{{item.status === '已完成'}}">
            <button class="action-btn success-btn" disabled>已完成</button>
          </block>
          <block wx:else>
            <button class="action-btn cancel-btn" bindtap="cancelOrder" data-id="{{item.id}}" catchtap="true">删除订单</button>
          </block>
        </view>
      </view>
      
      <!-- 加载更多提示 -->
      <view wx:if="{{hasMore && orders.length > 0}}" class="load-more">
        <view wx:if="{{loading}}" class="loading-more">
          <view class="loading-icon-small"></view>
          <text>加载更多中...</text>
        </view>
        <text wx:else class="load-more-text">上拉加载更多</text>
      </view>
      
      <!-- 无更多数据提示 -->
      <view wx:if="{{!hasMore && orders.length > 0}}" class="no-more">
        <text>没有更多订单了</text>
      </view>
      
      <!-- 总数显示 -->
      <view wx:if="{{total > 0}}" class="total-info">
        <text>共 {{total}} 条预购记录</text>
      </view>
    </view>
  </view>
</view>