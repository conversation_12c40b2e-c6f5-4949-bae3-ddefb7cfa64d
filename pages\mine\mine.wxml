<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else>
    <!-- 用户头像和信息 -->
    <view class="user-header">
      <view class="avatar-container" bindtap="onAvatarTap">
        <image 
          class="avatar" 
          src="{{userInfo.avatar || '/assets/images/default-avatar.png'}}" 
          mode="aspectFill" 
        />
        
        <!-- 未登录遮罩 -->
        <view wx:if="{{!isLoggedIn}}" class="avatar-mask">
          <text class="login-hint">点击登录</text>
        </view>
        
        <!-- 头像上传中遮罩 -->
        <view wx:if="{{isLoggedIn && isUploadingAvatar}}" class="avatar-uploading-mask">
          <view class="uploading-icon"></view>
          <text class="uploading-text">上传中</text>
        </view>
        
        <!-- 已登录状态下的头像编辑提示 -->
        <view wx:if="{{isLoggedIn && !isUploadingAvatar}}" class="avatar-edit-hint">
          <image class="edit-icon" src="/assets/images/edit-icon.png" mode="aspectFit"></image>
        </view>
      </view>
      
      <view class="user-info">
        <text class="username {{!isLoggedIn ? 'not-logged-in' : ''}}">
          {{userInfo.username || '未登录'}}
        </text>
        <text wx:if="{{isLoggedIn && userInfo.usertype}}" class="user-type">
          {{userInfo.usertype}}
        </text>
        
        <!-- 地址信息，添加编辑功能 -->
        <view wx:if="{{isLoggedIn}}" class="address-container">
          <text class="user-address">
            📍 {{userInfo.address || '未设置地址'}}
          </text>
          <view class="address-edit-btn" bindtap="showEditAddress">
            <text class="edit-text">编辑</text>
          </view>
        </view>
        
        <text wx:if="{{isLoggedIn && userInfo.bio}}" class="bio">
          {{userInfo.bio}}
        </text>
        <text wx:if="{{isLoggedIn && !userInfo.bio}}" class="bio placeholder">
          这个人很懒，什么都没写~
        </text>
      </view>
      
      <view wx:if="{{isLoggedIn}}" class="user-stats">
        <view class="stat-item">
          <text class="stat-number">{{userInfo.posts || 0}}</text>
          <text class="stat-label">发布</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{userInfo.followers || 0}}</text>
          <text class="stat-label">关注者</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{userInfo.following || 0}}</text>
          <text class="stat-label">关注</text>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view wx:if="{{isLoggedIn}}" class="menu-section">
      <!-- 我的活动 -->
      <view class="menu-group">
        <view class="menu-title">我的活动</view>
        <view class="menu-list">
          <view class="menu-item" bindtap="toMyPosts">
            <view class="menu-icon">📋</view>
            <view class="menu-content">
              <text class="menu-text">我的发布</text>
              <text class="menu-desc">查看我发布的宠物信息</text>
            </view>
            <view class="menu-arrow">></view>
          </view>
          <view class="menu-item" bindtap="toMyFavorites">
            <view class="menu-icon">❤️</view>
            <view class="menu-content">
              <text class="menu-text">我的收藏</text>
              <text class="menu-desc">收藏的宠物信息</text>
            </view>
            <view class="menu-arrow">></view>
          </view>
        </view>
      </view>

      <!-- 我的服务 -->
      <view class="menu-group">
        <view class="menu-title">我的服务</view>
        <view class="menu-list">
          <view class="menu-item" bindtap="toMyOrders">
            <view class="menu-icon">🛒</view>
            <view class="menu-content">
              <text class="menu-text">我的订单</text>
              <text class="menu-desc">查看宠物相关订单</text>
            </view>
            <view class="menu-arrow">></view>
          </view>
          <view class="menu-item" bindtap="toMyReservations">
            <view class="menu-icon">📅</view>
            <view class="menu-content">
              <text class="menu-text">我的预约</text>
              <text class="menu-desc">医疗预约和其他预约</text>
            </view>
            <view class="menu-arrow">></view>
          </view>
          <view class="menu-item" bindtap="toMyEvaluations">
            <view class="menu-icon">⭐</view>
            <view class="menu-content">
              <text class="menu-text">我的评价</text>
              <text class="menu-desc">查看我的评价记录</text>
            </view>
            <view class="menu-arrow">></view>
          </view>
        </view>
      </view>

      <!-- 设置 -->
      <view class="menu-group">
        <view class="menu-title">设置</view>
        <view class="menu-list">
          <view class="menu-item" bindtap="toSettings">
            <view class="menu-icon">⚙️</view>
            <view class="menu-content">
              <text class="menu-text">设置</text>
              <text class="menu-desc">个人信息和系统设置</text>
            </view>
            <view class="menu-arrow">></view>
          </view>
        </view>
      </view>

      <!-- 账户操作 -->
      <view class="action-buttons">
        <button class="logout-btn" bindtap="logout">
          退出登录
        </button>
        <button class="delete-btn" bindtap="deleteAccount">
          注销账户
        </button>
      </view>
    </view>

    <!-- 未登录状态 -->
    <view wx:else class="not-logged-in">
      <view class="login-hint-container">
        <image class="login-illustration" src="/assets/images/login-hint.png" mode="aspectFit" />
        <text class="login-hint-text">登录后查看更多功能</text>
        <button class="login-btn" bindtap="login">
          立即登录
        </button>
      </view>
    </view>
  </view>

  <!-- 修改地址弹窗 -->
  <view wx:if="{{showAddressModal}}" class="modal-overlay" bindtap="hideAddressModal">
    <view class="address-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">修改地址</text>
        <view class="modal-close" bindtap="hideAddressModal">×</view>
      </view>
      
      <view class="modal-content">
        <!-- 地址输入 -->
        <view class="form-group">
          <text class="form-label">地址</text>
          <input 
            class="form-input" 
            type="text" 
            placeholder="请输入详细地址"
            value="{{tempAddress}}"
            bindinput="onAddressInput"
            maxlength="100"
          />
        </view>
        
        <!-- 用户类型选择 -->
        <view class="form-group">
          <text class="form-label">用户类型</text>
          <picker 
            class="form-picker"
            mode="selector" 
            range="{{userTypeOptions}}"
            range-key="label"
            value="{{selectedUserTypeIndex}}"
            bindchange="onUserTypeChange"
          >
            <view class="picker-display">
              {{userTypeOptions[selectedUserTypeIndex].label}}
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="cancel-btn" bindtap="hideAddressModal">取消</button>
        <button 
          class="confirm-btn" 
          bindtap="submitAddressUpdate"
          disabled="{{isUpdatingAddress}}"
        >
          {{isUpdatingAddress ? '保存中...' : '保存'}}
        </button>
      </view>
    </view>
  </view>
</view>