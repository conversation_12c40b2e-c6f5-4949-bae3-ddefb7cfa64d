<view class="container">
  <!-- 标签页 -->
  <view class="tabs">
    <view 
      wx:for="{{tabs}}" 
      wx:key="key"
      class="tab-item {{activeTab === item.key ? 'active' : ''}}"
      data-tab="{{item.key}}"
      bindtap="switchTab"
    >
      <text class="tab-text">{{item.name}}</text>
      <view wx:if="{{activeTab === item.key}}" class="tab-indicator"></view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading && reservations.length === 0}}" class="loading-container">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 预约列表 -->
  <view wx:elif="{{reservations.length > 0}}" class="reservation-list">
    <view 
      wx:for="{{reservations}}" 
      wx:key="id"
      class="reservation-item"
    >
      <!-- 医疗预约卡片 -->
      <view wx:if="{{activeTab === 'medical'}}" class="reservation-card">
        <view class="card-header">
          <view class="hospital-info">
            <text class="hospital-name">{{item.hospitalName}}</text>
            <text class="reserve-number">预约号：{{item.reserveNumber}}</text>
          </view>
          <view class="status" style="background-color: {{getStatusColor(item.status)}}">
            {{item.statusText}}
          </view>
        </view>
        
        <view class="card-content">
          <view class="info-row">
            <text class="label">宠物名称：</text>
            <text class="value">{{item.petName}}</text>
          </view>
          <view class="info-row">
            <text class="label">服务项目：</text>
            <text class="value">{{item.service}}</text>
          </view>
          <view class="info-row">
            <text class="label">预约时间：</text>
            <text class="value">{{item.reserveTime}}</text>
          </view>
          <view class="info-row">
            <text class="label">医院地址：</text>
            <text class="value">{{item.address}}</text>
          </view>
        </view>

        <view class="card-actions">
          <button 
            class="action-btn contact-btn"
            data-phone="{{item.contact}}"
            bindtap="contact"
          >
            联系医院
          </button>
          <button 
            wx:if="{{item.status === 'pending' || item.status === 'confirmed'}}"
            class="action-btn cancel-btn"
            data-id="{{item.id}}"
            data-type="medical"
            bindtap="cancelReservation"
          >
            取消预约
          </button>
          <button 
            class="action-btn detail-btn"
            data-id="{{item.hospitalId}}"
            data-type="medical"
            bindtap="viewDetail"
          >
            查看详情
          </button>
        </view>
      </view>

      <!-- 宠物预购卡片 -->
      <view wx:else class="reservation-card">
        <view class="card-header">
          <view class="shop-info">
            <text class="shop-name">{{item.shopName}}</text>
            <text class="order-number">订单号：{{item.orderNumber}}</text>
          </view>
          <view class="status" style="background-color: {{getStatusColor(item.status)}}">
            {{item.statusText}}
          </view>
        </view>
        
        <view class="card-content">
          <view class="pet-info">
            <image class="pet-image" src="{{item.petImage}}" mode="aspectFill" />
            <view class="pet-details">
              <text class="pet-name">{{item.petName}}</text>
              <text class="pet-breed">{{item.petBreed}}</text>
              <text class="pet-price">¥{{item.price}}</text>
            </view>
          </view>
          <view class="info-row">
            <text class="label">预约时间：</text>
            <text class="value">{{item.appointmentTime}}</text>
          </view>
          <view class="info-row">
            <text class="label">商店地址：</text>
            <text class="value">{{item.address}}</text>
          </view>
        </view>

        <view class="card-actions">
          <button 
            class="action-btn contact-btn"
            data-phone="{{item.contact}}"
            bindtap="contact"
          >
            联系商店
          </button>
          <button 
            wx:if="{{item.status === 'pending'}}"
            class="action-btn cancel-btn"
            data-id="{{item.id}}"
            data-type="pet"
            bindtap="cancelReservation"
          >
            取消预约
          </button>
          <button 
            class="action-btn detail-btn"
            data-id="{{item.shopId}}"
            data-type="pet"
            bindtap="viewDetail"
          >
            查看详情
          </button>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{loading}}" class="loading-more">
      <text>加载中...</text>
    </view>
    
    <!-- 没有更多 -->
    <view wx:elif="{{!hasMore}}" class="no-more">
      <text>没有更多了</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:else class="empty-container">
    <image class="empty-image" src="/assets/images/empty.png" mode="aspectFit" />
    <text class="empty-text">
      {{activeTab === 'medical' ? '暂无医疗预约' : '暂无宠物预购'}}
    </text>
    <button class="goto-btn" bindtap="goToList">
      {{activeTab === 'medical' ? '去预约医疗' : '去选购宠物'}}
    </button>
  </view>
</view>