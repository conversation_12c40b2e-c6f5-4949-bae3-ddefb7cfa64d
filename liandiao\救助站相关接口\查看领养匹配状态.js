import { getAdoptStatusForRescue } from '../../services/rescueService';

const params = {
    // 查询参数
};

getAdoptStatusForRescue(params)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('领养匹配状态:', res.data);
        } else {
            wx.showToast({
                title: res.message || '获取领养匹配状态失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('获取领养匹配状态出错', err);
        wx.showToast({
            title: '获取领养匹配状态出错，请重试',
            icon: 'none'
        });
    });