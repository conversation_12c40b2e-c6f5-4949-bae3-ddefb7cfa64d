import { login } from '../../services/userService';

const loginData = {
    account: 'testuser',
    password: 'testpassword'
};

login(loginData)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            wx.setStorageSync('token', res.data.jwttoken);
            wx.showToast({
                title: '登录成功',
                icon: 'success'
            });
        } else {
            wx.showToast({
                title: res.message || '登录失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('登录出错', err);
        wx.showToast({
            title: '登录出错，请重试',
            icon: 'none'
        });
    });