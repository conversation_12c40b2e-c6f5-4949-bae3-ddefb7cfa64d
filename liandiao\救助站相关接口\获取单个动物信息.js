import { getAnimalInfo } from '../../services/rescueService';

const animalId = 1;

getAnimalInfo(animalId)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('动物信息:', res.data);
        } else {
            wx.showToast({
                title: res.message || '获取动物信息失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('获取动物信息出错', err);
        wx.showToast({
            title: '获取动物信息出错，请重试',
            icon: 'none'
        });
    });