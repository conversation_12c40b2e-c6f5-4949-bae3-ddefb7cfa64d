// pages/mine/settings/settings.js
import userService from '../../../services/userService';
import { CONFIG } from '../../../services/config';

Page({
  data: {
    userInfo: null,
    isLoggedIn: false,
    
    // 个人信息
    editMode: false,
    tempUserInfo: {},
    
    // 系统设置
    systemSettings: {
      notificationEnabled: true,
      locationEnabled: true,
      autoUpdate: true,
      darkMode: false
    },
    
    // 应用信息
    appInfo: {
      version: '1.0.0',
      buildNumber: '202403150001',
      updateTime: '2024-03-15'
    },
    
    // 缓存信息
    cacheSize: '0MB',
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.loadUserInfo();
    this.loadSystemSettings();
    this.calculateCacheSize();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadUserInfo();
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    const userInfo = userService.getLocalUserInfo();
    const isLoggedIn = userService.isLoggedIn();
    
    this.setData({
      userInfo: userInfo,
      isLoggedIn: isLoggedIn,
      tempUserInfo: userInfo ? { ...userInfo } : {}
    });
  },

  /**
   * 加载系统设置
   */
  loadSystemSettings() {
    try {
      const settings = wx.getStorageSync('systemSettings');
      if (settings) {
        this.setData({
          systemSettings: { ...this.data.systemSettings, ...settings }
        });
      }
    } catch (error) {
      console.warn('加载系统设置失败:', error);
    }
  },

  /**
   * 保存系统设置
   */
  saveSystemSettings() {
    try {
      wx.setStorageSync('systemSettings', this.data.systemSettings);
    } catch (error) {
      console.error('保存系统设置失败:', error);
    }
  },

  /**
   * 计算缓存大小
   */
  calculateCacheSize() {
    // 模拟计算缓存大小
    setTimeout(() => {
      this.setData({
        cacheSize: '12.5MB'
      });
    }, 500);
  },

  /**
   * 开启编辑模式
   */
  startEdit() {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    this.setData({
      editMode: true,
      tempUserInfo: { ...this.data.userInfo }
    });
  },

  /**
   * 取消编辑
   */
  cancelEdit() {
    this.setData({
      editMode: false,
      tempUserInfo: { ...this.data.userInfo }
    });
  },

  /**
   * 保存个人信息
   */
  saveUserInfo() {
    if (!this.data.tempUserInfo.username || this.data.tempUserInfo.username.trim() === '') {
      wx.showToast({
        title: '用户名不能为空',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    userService.updateUserInfo(this.data.tempUserInfo)
      .then(() => {
        this.setData({
          userInfo: { ...this.data.tempUserInfo },
          editMode: false,
          loading: false
        });

        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });
      })
      .catch(error => {
        console.error('保存用户信息失败:', error);
        this.setData({ loading: false });
        wx.showToast({
          title: error.message || '保存失败，请重试',
          icon: 'none'
        });
      });
  },

  /**
   * 输入用户名
   */
  onUsernameInput(e) {
    this.setData({
      'tempUserInfo.username': e.detail.value
    });
  },

  /**
   * 输入个人简介
   */
  onBioInput(e) {
    this.setData({
      'tempUserInfo.bio': e.detail.value
    });
  },

  /**
   * 上传头像
   */
  uploadAvatar() {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        
        wx.showLoading({
          title: '上传中...',
          mask: true
        });

        const userType = this.data.userInfo.usertype || '普通用户';
        
        userService.uploadImage(tempFilePath, userType, '头像')
          .then(result => {
            wx.hideLoading();
            
            if (result && result.data && result.data.url) {
              // 更新本地用户信息
              const updatedUserInfo = {
                ...this.data.userInfo,
                avatar: result.data.url
              };
              
              this.setData({
                userInfo: updatedUserInfo,
                'tempUserInfo.avatar': result.data.url
              });

              // 保存到本地存储
              wx.setStorageSync('userInfo', updatedUserInfo);

              wx.showToast({
                title: '头像上传成功',
                icon: 'success'
              });
            } else {
              throw new Error('上传返回数据格式错误');
            }
          })
          .catch(error => {
            wx.hideLoading();
            console.error('头像上传失败:', error);
            wx.showToast({
              title: error.message || '头像上传失败',
              icon: 'none'
            });
          });
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 切换通知设置
   */
  toggleNotification(e) {
    const enabled = e.detail.value;
    this.setData({
      'systemSettings.notificationEnabled': enabled
    });
    this.saveSystemSettings();
    
    wx.showToast({
      title: enabled ? '已开启通知' : '已关闭通知',
      icon: 'success'
    });
  },

  /**
   * 切换位置服务
   */
  toggleLocation(e) {
    const enabled = e.detail.value;
    this.setData({
      'systemSettings.locationEnabled': enabled
    });
    this.saveSystemSettings();
    
    wx.showToast({
      title: enabled ? '已开启位置服务' : '已关闭位置服务',
      icon: 'success'
    });
  },

  /**
   * 切换自动更新
   */
  toggleAutoUpdate(e) {
    const enabled = e.detail.value;
    this.setData({
      'systemSettings.autoUpdate': enabled
    });
    this.saveSystemSettings();
    
    wx.showToast({
      title: enabled ? '已开启自动更新' : '已关闭自动更新',
      icon: 'success'
    });
  },

  /**
   * 切换暗黑模式
   */
  toggleDarkMode(e) {
    const enabled = e.detail.value;
    this.setData({
      'systemSettings.darkMode': enabled
    });
    this.saveSystemSettings();
    
    wx.showToast({
      title: enabled ? '已开启暗黑模式' : '已关闭暗黑模式',
      icon: 'success'
    });
  },

  /**
   * 修改地址
   */
  changeAddress() {
    if (!this.data.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '修改地址',
      editable: true,
      placeholderText: '请输入新地址',
      success: (res) => {
        if (res.confirm && res.content && res.content.trim()) {
          const newAddress = res.content.trim();
          const userType = this.data.userInfo.usertype || '普通用户';

          wx.showLoading({
            title: '更新中...',
            mask: true
          });

          userService.updateAddress(newAddress, userType)
            .then(() => {
              wx.hideLoading();
              
              // 更新本地用户信息
              const updatedUserInfo = {
                ...this.data.userInfo,
                address: newAddress
              };
              
              this.setData({
                userInfo: updatedUserInfo,
                'tempUserInfo.address': newAddress
              });

              // 保存到本地存储
              wx.setStorageSync('userInfo', updatedUserInfo);

              wx.showToast({
                title: '地址更新成功',
                icon: 'success'
              });
            })
            .catch(error => {
              wx.hideLoading();
              console.error('地址更新失败:', error);
              wx.showToast({
                title: error.message || '地址更新失败',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  /**
   * 清除缓存
   */
  clearCache() {
    wx.showModal({
      title: '清除缓存',
      content: '确定要清除应用缓存吗？这可能会影响应用运行速度。',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '清理中...',
            mask: true
          });

          // 模拟清除缓存过程
          setTimeout(() => {
            wx.hideLoading();
            this.setData({
              cacheSize: '0MB'
            });

            wx.showToast({
              title: '缓存清理完成',
              icon: 'success'
            });

            // 重新计算缓存大小
            setTimeout(() => {
              this.calculateCacheSize();
            }, 2000);
          }, 1500);
        }
      }
    });
  },

  /**
   * 检查更新
   */
  checkUpdate() {
    wx.showLoading({
      title: '检查中...',
      mask: true
    });

    // 模拟检查更新
    setTimeout(() => {
      wx.hideLoading();
      wx.showModal({
        title: '检查更新',
        content: '当前已是最新版本',
        showCancel: false
      });
    }, 1000);
  },

  /**
   * 关于我们
   */
  aboutUs() {
    wx.showModal({
      title: '关于我们',
      content: '城市流浪动物救助平台\n版本：' + this.data.appInfo.version + '\n致力于为流浪动物寻找温暖的家',
      showCancel: false
    });
  },

  /**
   * 隐私政策
   */
  privacyPolicy() {
    wx.showModal({
      title: '隐私政策',
      content: '我们非常重视您的隐私安全，详细的隐私政策请访问我们的官方网站查看。',
      showCancel: false
    });
  },

  /**
   * 用户协议
   */
  userAgreement() {
    wx.showModal({
      title: '用户协议',
      content: '感谢您使用我们的服务，请遵守相关使用协议，详细协议内容请访问官方网站。',
      showCancel: false
    });
  },

  /**
   * 联系客服
   */
  contactService() {
    wx.showActionSheet({
      itemList: ['电话客服', '在线客服', '意见反馈'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            wx.makePhoneCall({
              phoneNumber: '************'
            });
            break;
          case 1:
            wx.showToast({
              title: '客服功能开发中',
              icon: 'none'
            });
            break;
          case 2:
            wx.showToast({
              title: '反馈功能开发中',
              icon: 'none'
            });
            break;
        }
      }
    });
  }
});