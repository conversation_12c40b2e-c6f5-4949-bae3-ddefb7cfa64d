import { uploadImage } from '../../services/userService';

const userType = 'COMMON';
const imageType = 'AVATAR';
const filePath = '本地图片文件路径';

uploadImage(userType, imageType, filePath)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            wx.showToast({
                title: '图片上传成功',
                icon: 'success'
            });
        } else {
            wx.showToast({
                title: res.message || '图片上传失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('图片上传出错', err);
        wx.showToast({
            title: '图片上传出错，请重试',
            icon: 'none'
        });
    });