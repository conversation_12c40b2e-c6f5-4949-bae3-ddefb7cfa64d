<!--pages/encyclopedia/encyclopedia.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <input 
        class="search-input" 
        placeholder="请输入动物品种，如：金毛、波斯猫" 
        value="{{searchValue}}"
        bindinput="onSearchInput"
        confirm-type="search"
        bindconfirm="onSearchTap"
      />
      <view class="search-btn" bindtap="onSearchTap">
        <text class="search-icon">🔍</text>
      </view>
      <view wx:if="{{searchValue}}" class="clear-btn" bindtap="onClearSearch">
        <text class="clear-icon">×</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-section">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在查询中...</text>
  </view>

  <!-- 搜索结果 -->
  <view wx:elif="{{animalList.length > 0}}" class="result-section">
    <view class="result-header">
      <text class="result-title">找到 {{animalList.length}} 条结果</text>
    </view>
    
    <view class="animal-list">
      <view 
        wx:for="{{animalList}}" 
        wx:key="breed"
        class="animal-card"
        data-index="{{index}}"
        bindtap="onAnimalTap"
      >
        <view class="card-header">
          <text class="animal-name">{{item.breed}}</text>
          <text class="view-detail">查看详情 ></text>
        </view>
        
        <view class="card-content">
          <view class="info-item">
            <text class="info-label">🍖 饮食习性：</text>
            <text class="info-value">{{item.dietInfo}}</text>
          </view>
          
          <view class="info-item">
            <text class="info-label">🏠 生活习性：</text>
            <text class="info-value">{{item.livingHabits}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:elif="{{isEmpty}}" class="empty-section">
    <view class="empty-icon">🔍</view>
    <text class="empty-title">未找到相关动物</text>
    <text class="empty-desc">请尝试其他关键词，如：金毛、波斯猫、哈士奇</text>
    <button class="retry-btn" bindtap="onRetrySearch">重新搜索</button>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{errorMessage}}" class="error-section">
    <view class="error-icon">⚠️</view>
    <text class="error-title">查询失败</text>
    <text class="error-desc">{{errorMessage}}</text>
    <button class="retry-btn" bindtap="onRetrySearch">重试</button>
  </view>

  <!-- 默认状态 -->
  <view wx:else class="default-section">
    <view class="default-icon">🐾</view>
    <text class="default-title">动物百科查询</text>
    <text class="default-desc">输入动物品种名称，获取详细的饮食和生活习性信息</text>
    
    <view class="example-tags">
      <text class="example-title">热门搜索：</text>
      <view class="tag-list">
        <text class="example-tag" data-variety="金毛" bindtap="onExampleTap">金毛</text>
        <text class="example-tag" data-variety="波斯猫" bindtap="onExampleTap">波斯猫</text>
        <text class="example-tag" data-variety="哈士奇" bindtap="onExampleTap">哈士奇</text>
        <text class="example-tag" data-variety="布偶猫" bindtap="onExampleTap">布偶猫</text>
      </view>
    </view>
  </view>
</view>