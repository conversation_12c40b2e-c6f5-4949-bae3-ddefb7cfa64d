// app.js
App({
  /**
   * 全局数据
   */
  globalData: {
    userInfo: null,
    token: '',
    systemInfo: null,
    isFirstUse: false,
    isLogin: false,
    safeBottom: 0,
    statusBarHeight: 0,
    customNavBarHeight: 0,
    menuButtonInfo: null
  },

  /**
   * 当小程序初始化完成时，会触发 onLaunch
   */
  onLaunch: function() {
    // 获取系统信息
    this.getSystemInfo();
    
    // 检查是否首次使用
    this.checkFirstUse();
    
    // 读取本地存储的用户信息和token
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    
    if (userInfo) {
      this.globalData.userInfo = userInfo;
    }
    
    if (token) {
      this.globalData.token = token;
      this.globalData.isLogin = true;
    }
    
    // 检查登录状态
    this.checkLoginStatus();
    
    // 检查版本更新
    this.checkUpdate();
  },

  /**
   * 获取系统信息
   */
  getSystemInfo: function() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      this.globalData.systemInfo = systemInfo;
      
      // 计算安全区域
      const { screenHeight, safeArea } = systemInfo;
      const safeBottom = screenHeight - safeArea.bottom;
      
      // 设置自定义导航栏高度
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      const statusBarHeight = systemInfo.statusBarHeight;
      const customNavBarHeight = (menuButtonInfo.top - statusBarHeight) * 2 + menuButtonInfo.height;
      
      this.globalData.safeBottom = safeBottom;
      this.globalData.statusBarHeight = statusBarHeight;
      this.globalData.customNavBarHeight = customNavBarHeight;
      this.globalData.menuButtonInfo = menuButtonInfo;
    } catch (e) {
      console.error('获取系统信息失败', e);
    }
  },

  /**
   * 检查是否首次使用
   */
  checkFirstUse: function() {
    const hasUsed = wx.getStorageSync('hasUsed');
    if (!hasUsed) {
      this.globalData.isFirstUse = true;
      wx.setStorageSync('hasUsed', true);
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus: function() {
    const token = this.globalData.token;
    const userInfo = this.globalData.userInfo;
    
    // 如果没有token或用户信息，则未登录
    if (!token || !userInfo) {
      this.globalData.isLogin = false;
      return;
    }
    
    // 判断token是否过期
    // 这里可以调用后端接口验证token有效性
    // 简化处理：假设token有效
    this.globalData.isLogin = true;
  },

  /**
   * 检查版本更新
   */
  checkUpdate: function() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      
      updateManager.onCheckForUpdate(function(res) {
        // 请求完新版本信息的回调
        if (res.hasUpdate) {
          console.log('有新版本');
        }
      });
      
      updateManager.onUpdateReady(function() {
        // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: function(res) {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });
      
      updateManager.onUpdateFailed(function() {
        // 新版本下载失败
        wx.showToast({
          title: '更新失败，请稍后再试',
          icon: 'none'
        });
      });
    }
  },

  /**
   * 登录处理
   * @param {Object} userInfo - 用户信息
   * @param {string} token - 登录token
   */
  doLogin: function(userInfo, token) {
    // 保存用户信息和token
    this.globalData.userInfo = userInfo;
    this.globalData.token = token;
    this.globalData.isLogin = true;
    
    // 存储到本地
    wx.setStorageSync('userInfo', userInfo);
    wx.setStorageSync('token', token);
  },

  /**
   * 退出登录
   */
  doLogout: function() {
    // 清除用户信息和token
    this.globalData.userInfo = null;
    this.globalData.token = '';
    this.globalData.isLogin = false;
    
    // 清除本地存储
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('token');
    
    // 跳转到登录页
    wx.reLaunch({
      url: '/pages/login/login'
    });
  }
});