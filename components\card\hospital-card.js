// components/card/hospital-card.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 医院数据
    hospital: {
      type: Object,
      value: {}
    },
    // 显示模式：compact(紧凑), normal(普通), detailed(详细)
    mode: {
      type: String,
      value: 'normal'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 默认图片
    defaultImage: '/assets/images/default-hospital.png'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 点击卡片
    onTapCard: function() {
      const { hospital } = this.properties;
      this.triggerEvent('tap', { hospitalId: hospital.id });
    },
    
    // 点击收藏按钮
    onTapFavorite: function(e) {
      e.stopPropagation(); // 阻止事件冒泡
      const { hospital } = this.properties;
      this.triggerEvent('favorite', { hospitalId: hospital.id, isFavorite: !hospital.isFavorite });
    },
    
    // 点击电话按钮
    onTapPhone: function(e) {
      e.stopPropagation(); // 阻止事件冒泡
      const { hospital } = this.properties;
      if (hospital.phone) {
        wx.makePhoneCall({
          phoneNumber: hospital.phone,
          fail: (err) => {
            console.error('拨打电话失败:', err);
            wx.showToast({
              title: '拨打电话失败',
              icon: 'none'
            });
          }
        });
      } else {
        wx.showToast({
          title: '无电话号码',
          icon: 'none'
        });
      }
    },
    
    // 点击导航按钮
    onTapNavigation: function(e) {
      e.stopPropagation(); // 阻止事件冒泡
      const { hospital } = this.properties;
      if (hospital.latitude && hospital.longitude) {
        wx.openLocation({
          latitude: hospital.latitude,
          longitude: hospital.longitude,
          name: hospital.name,
          address: hospital.address
        });
      } else {
        wx.showToast({
          title: '无位置信息',
          icon: 'none'
        });
      }
    },
    
    // 点击预约按钮
    onTapAppointment: function(e) {
      e.stopPropagation(); // 阻止事件冒泡
      const { hospital } = this.properties;
      this.triggerEvent('appointment', { hospitalId: hospital.id });
    },
    
    // 图片加载失败
    onImageError: function() {
      this.setData({
        'hospital.imageUrl': this.data.defaultImage
      });
    }
  }
})