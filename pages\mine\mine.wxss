.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6F61;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 用户头像和信息 */
.user-header {
  background: linear-gradient(135deg, #FF6F61 0%, #FF8A75 100%);
  padding: 60rpx 30rpx 40rpx;
  color: white;
  position: relative;
}

.user-header::after {
  content: "";
  position: absolute;
  bottom: -20rpx;
  left: 0;
  right: 0;
  height: 40rpx;
  background: #f5f5f5;
  border-radius: 40rpx 40rpx 0 0;
}

.avatar-container {
  position: relative;
  align-self: flex-start;
  margin-bottom: 20rpx;
  cursor: pointer;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.avatar-container:active .avatar {
  transform: scale(0.95);
}

/* 未登录遮罩 */
.avatar-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-hint {
  font-size: 24rpx;
  color: white;
}

/* 头像上传中遮罩 */
.avatar-uploading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.uploading-icon {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8rpx;
}

.uploading-text {
  font-size: 20rpx;
  color: white;
  opacity: 0.9;
}

/* 头像编辑提示 */
.avatar-edit-hint {
  position: absolute;
  bottom: -2rpx;
  right: -2rpx;
  width: 36rpx;
  height: 36rpx;
  background: #FF6F61;
  border-radius: 50%;
  border: 3rpx solid white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.edit-icon {
  width: 20rpx;
  height: 20rpx;
  opacity: 0.9;
}

/* 如果没有编辑图标，可以用文字代替 */
.avatar-edit-hint::before {
  content: "✏️";
  font-size: 18rpx;
}

.user-info {
  margin-bottom: 30rpx;
}

.username {
  font-size: 42rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 12rpx;
}

.username.not-logged-in {
  opacity: 0.8;
}

.user-type {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-bottom: 8rpx;
  margin-right: 10rpx;
}

/* 地址容器样式 */
.address-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.user-address {
  font-size: 24rpx;
  opacity: 0.9;
  flex: 1;
}

.address-edit-btn {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
  padding: 4rpx 12rpx;
  margin-left: 15rpx;
  transition: all 0.3s ease;
}

.address-edit-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.edit-text {
  font-size: 20rpx;
  color: white;
  opacity: 0.9;
}

.bio {
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.4;
  display: block;
}

.bio.placeholder {
  opacity: 0.7;
  font-style: italic;
}

.user-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 20rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 菜单区域 */
.menu-section {
  padding: 0 30rpx 30rpx;
  position: relative;
  z-index: 2;
}

.menu-group {
  margin-bottom: 40rpx;
}

.menu-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  position: relative;
}

.menu-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background: #FF6F61;
  border-radius: 3rpx;
}

.menu-list {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx 25rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f8f8;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 25rpx;
  min-width: 40rpx;
}

.menu-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.menu-text {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.menu-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.3;
}

.menu-arrow {
  font-size: 32rpx;
  color: #ccc;
  margin-left: 20rpx;
}

/* 操作按钮 */
.action-buttons {
  margin-top: 40rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.logout-btn, .delete-btn {
  width: 100%;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.logout-btn {
  background-color: #4CAF50;
  color: white;
}

.logout-btn:active {
  background-color: #45a049;
  transform: translateY(1rpx);
}

.delete-btn {
  background-color: #FF6F61;
  color: white;
}

.delete-btn:active {
  background-color: #e55a4e;
  transform: translateY(1rpx);
}

/* 未登录状态 */
.not-logged-in {
  padding: 40rpx 30rpx;
}

.login-hint-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.login-illustration {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 40rpx;
  opacity: 0.8;
}

.login-hint-text {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 50rpx;
}

.login-btn {
  width: 400rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #FF6F61 0%, #FF8A75 100%);
  color: white;
  border-radius: 40rpx;
  font-size: 32rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(255, 111, 97, 0.3);
  transition: all 0.3s ease;
}

.login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 111, 97, 0.3);
}

/* 修改地址弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.address-modal {
  background: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50rpx) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #999;
  background: #f5f5f5;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:active {
  background: #e0e0e0;
  transform: scale(0.95);
}

.modal-content {
  padding: 40rpx;
  max-height: 50vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fafafa;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #FF6F61;
  background: white;
  box-shadow: 0 0 0 4rpx rgba(255, 111, 97, 0.1);
}

.form-picker {
  width: 100%;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fafafa;
  color: #333;
  transition: all 0.3s ease;
}

.picker-display:active {
  border-color: #FF6F61;
  background: white;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
  transition: transform 0.3s ease;
}

.modal-actions {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 40rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  border: none;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.cancel-btn:active {
  background: #e0e0e0;
  transform: translateY(1rpx);
}

.confirm-btn {
  background: #FF6F61;
  color: white;
}

.confirm-btn:active {
  background: #e55a4e;
  transform: translateY(1rpx);
}

.confirm-btn:disabled {
  background: #ccc;
  color: #999;
  transform: none;
}

/* 响应式设计 */
@media screen and (max-width: 320px) {
  .container {
    padding: 0;
  }
  
  .user-header {
    padding: 40rpx 20rpx 30rpx;
  }
  
  .avatar {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50rpx;
  }
  
  .avatar-mask,
  .avatar-uploading-mask {
    border-radius: 50rpx;
  }
  
  .avatar-edit-hint {
    width: 30rpx;
    height: 30rpx;
  }
  
  .username {
    font-size: 36rpx;
  }
  
  .menu-section {
    padding: 0 20rpx 20rpx;
  }
  
  .modal-overlay {
    padding: 20rpx;
  }
  
  .modal-content {
    padding: 30rpx;
  }
  
  .modal-actions {
    padding: 15rpx 30rpx 30rpx;
  }
}

/* 头像悬浮效果（Web端） */
@media (hover: hover) {
  .avatar-container:hover .avatar {
    transform: scale(1.05);
    box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.2);
  }
  
  .avatar-container:hover .avatar-edit-hint {
    transform: scale(1.1);
  }
  
  .address-edit-btn:hover {
    background: rgba(255, 255, 255, 0.3);
  }
}