// pages/publish/publish.js
import { createPost } from '../../../services/postService';
import { uploadFile } from '../../services/fileService'; // 假设已有文件上传服务

Page({
data: {
postType: 0, // 0-救助信息，1-领养信息，2-寻宠启示
title: '',
content: '',
images: [],
contactPhone: '',
location: '',
isLoading: false,
postTypes: ['救助信息', '领养信息', '寻宠启示']
},

onLoad() {
// 获取当前位置（需要授权）
this.getLocation();
},

// 获取当前位置
getLocation() {
wx.getLocation({
type: 'wgs84',
success: (res) => {
const latitude = res.latitude;
const longitude = res.longitude;
// 可以调用地图API获取具体地址
this.setData({
location: `${latitude},${longitude}`
});
},
fail: (err) => {
console.error('获取位置失败:', err);
wx.showToast({
title: '请授权位置信息',
icon: 'none'
});
}
});
},

// 表单输入事件
handleInput(e) {
const { field } = e.currentTarget.dataset;
this.setData({
[field]: e.detail.value
});
},

// 选择发布类型
selectPostType(e) {
const { value } = e.detail;
this.setData({
postType: value
});
},

// 选择图片
selectImages() {
wx.chooseImage({
count: 9,
sizeType: ['original', 'compressed'],
sourceType: ['album', 'camera'],
success: (res) => {
const tempFilePaths = res.tempFilePaths;
this.setData({
images: [...this.data.images, ...tempFilePaths]
});
}
});
},

// 上传图片
async uploadImages() {
const images = [];
for (const imagePath of this.data.images) {
try {
const res = await uploadFile(imagePath);
images.push(res.data.url);
} catch (error) {
console.error('上传图片失败:', error);
wx.showToast({
title: '图片上传失败',
icon: 'none'
});
return null;
}
}
return images;
},

// 提交发布
async submitPublish() {
const { title, content, postType, contactPhone, location } = this.data;

// 表单验证
if (!title) {
wx.showToast({
title: '标题不能为空',
icon: 'none'
});
return;
}

if (!content) {
wx.showToast({
title: '内容不能为空',
icon: 'none'
});
return;
}

if (!contactPhone) {
wx.showToast({
title: '联系电话不能为空',
icon: 'none'
});
return;
}

// 手机号格式验证
const phoneReg = /^1[3-9]\d{9}$/;
if (!phoneReg.test(contactPhone)) {
wx.showToast({
title: '手机号格式不正确',
icon: 'none'
});
return;
}

this.setData({ isLoading: true });

try {
// 上传图片
let images = [];
if (this.data.images.length > 0) {
images = await this.uploadImages();
if (!images) {
throw new Error('图片上传失败');
}
}

// 准备发布数据
const postData = {
title,
content,
type: postType,
images,
contactPhone,
location,
userId: wx.getStorageSync('userInfo').id // 从缓存中获取用户ID
};

// 调用发布接口
const res = await createPost(postData);

wx.showToast({
title: '发布成功',
icon: 'success'
});

// 返回上一页或跳转到列表页
setTimeout(() => {
wx.navigateBack();
}, 1500);
} catch (error) {
console.error('发布失败:', error);
wx.showToast({
title: error.message || '发布失败，请稍后重试',
icon: 'none'
});
} finally {
this.setData({ isLoading: false });
}
}
});