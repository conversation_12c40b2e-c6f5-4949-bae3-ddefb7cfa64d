/* pages/adopt/detail/detail.wxss */

.container {
  padding-bottom: 120rpx;
}

/* 宠物头图 */
.pet-header {
  position: relative;
  width: 100%;
  height: 500rpx;
}

.pet-image {
  width: 100%;
  height: 100%;
}

.back-btn {
  position: absolute;
  top: 60rpx;
  left: 30rpx;
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.back-icon {
  width: 36rpx;
  height: 36rpx;
}

/* 基本信息 */
.info-section {
  padding: 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.pet-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.pet-status {
  font-size: 24rpx;
  color: #fff;
  background-color: #4eaaa8;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.info-content {
  display: flex;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  width: 50%;
  margin-bottom: 16rpx;
}

.info-label {
  font-size: 28rpx;
  color: #999;
  width: 90rpx;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 宠物详情、领养条件、医疗记录等 */
.section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 8rpx;
  height: 28rpx;
  background-color: #4eaaa8;
  border-radius: 4rpx;
}

.section-content {
  padding: 0 10rpx;
}

/* 详情文本 */
.detail-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 领养条件 */
.condition-item {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
  line-height: 1.5;
}

/* 医疗记录 */
.medical-item {
  display: flex;
  margin-bottom: 16rpx;
}

.medical-date {
  font-size: 28rpx;
  color: #999;
  width: 150rpx;
}

.medical-desc {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

/* 救助站信息 */
.rescue-info {
  padding: 10rpx 0;
}

.rescue-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.rescue-address, .rescue-phone {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding: 10rpx 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.adopt-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #4eaaa8;
  color: #fff;
  font-size: 30rpx;
  border-radius: 40rpx;
}