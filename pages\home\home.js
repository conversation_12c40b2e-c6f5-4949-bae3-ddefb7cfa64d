// pages/home/<USER>
import animalService from '../../services/animalService.js';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 轮播图数据
    banners: [
      {
        id: 1,
        image: '/assets/images/banner1.png',
        url: '/pages/adopt/adopt'
      },
      {
        id: 2,
        image: '/assets/images/banner2.png',
        url: '/pages/shop/shop'
      },
      {
        id: 3,
        image: '/assets/images/banner3.png',
        url: '/pages/hospital/hospital'
      }
    ],
    // 功能菜单
    menus: [
      {
        id: 1,
        icon: '/assets/images/cat-adopt.png',
        name: '宠物领养',
        url: '/pages/adopt/adopt'
      },
      {
        id: 2,
        icon: '/assets/images/cat-shop.png',
        name: '宠物商店',
        url: '/pages/shop/shop'
      },
      {
        id: 3,
        icon: '/assets/images/cat-hospital.png',
        name: '宠物医院',
        url: '/pages/hospital/hospital'
      },
      {
        id: 4,
        icon: '/assets/images/cat-rescue.png',
        name: '救助站',
        url: '/pages/rescue/rescue'
      },
      {
        id: 5,
        icon: '/assets/images/cat-encyclopedia.png',
        name: '宠物百科',
        url: '/pages/encyclopedia/encyclopedia'
      }
    ],
    // 推荐宠物
    pets: [],
    // 加载状态
    loading: false,
    // 错误状态
    error: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log('🏠 首页加载');
    
    // 检查登录状态
    this.checkLoginStatus();
    
    // 获取推荐宠物
    this.getPets();
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus: function() {
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    // 如果没有token或用户信息，跳转到登录页
    if (!token && !userInfo) {
      console.log('🔑 未登录，跳转到登录页');
      wx.reLaunch({
        url: '/pages/login/login'
      });
      return false;
    }
    
    return true;
  },

  /**
   * 获取推荐宠物
   */
  getPets: function() {
    console.log('🐾 开始获取推荐宠物数据');
    
    // 设置加载状态
    this.setData({
      loading: true,
      error: false
    });

    // 调用动物服务接口
    animalService.getAnimalList({
      page: 1,
      pageSize: 6  // 首页只显示6个推荐宠物
    })
    .then(res => {
      console.log('✅ 获取宠物数据成功:', res);
      
      // 处理响应数据
      if (res && res.code === 20 && res.data) {
        // 接口返回成功
        const pets = Array.isArray(res.data) ? res.data : [];
        
        // 格式化宠物数据，确保图片路径正确
        const formattedPets = pets.map(pet => ({
          ...pet,
          image: pet.image || '/assets/images/default-pet.png',
          // 确保必要字段存在
          name: pet.name || '未命名',
          breed: pet.breed || pet.type || '未知品种',
          age: pet.age || '未知年龄',
          gender: pet.gender || '未知',
          status: pet.status || '待领养'
        }));
        
        this.setData({
          pets: formattedPets,
          loading: false,
          error: false
        });
        
        console.log('🎉 宠物数据设置成功，共', formattedPets.length, '只宠物');
        
      } else {
        // 接口返回格式异常，但不报错
        console.log('⚠️ 接口返回数据格式异常:', res);
        this.handleDataError('数据格式异常');
      }
    })
    .catch(err => {
      console.error('❌ 获取推荐宠物失败:', err);
      this.handleDataError(err.message || '网络请求失败');
    });
  },

  /**
   * 处理数据获取错误
   */
  handleDataError: function(errorMessage) {
    console.log('🔄 数据获取失败，使用本地模拟数据');
    
    // 使用本地模拟数据作为降级方案
    const mockPets = [
      {
        id: 1,
        name: '小白',
        type: '狗狗',
        breed: '萨摩耶',
        age: '2岁',
        gender: '公',
        image: '/assets/images/default-pet.png',
        status: '待领养'
      },
      {
        id: 2,
        name: '咪咪',
        type: '猫咪',
        breed: '英短',
        age: '1岁',
        gender: '母',
        image: '/assets/images/default-pet.png',
        status: '待领养'
      },
      {
        id: 3,
        name: '旺财',
        type: '狗狗',
        breed: '金毛',
        age: '3岁',
        gender: '公',
        image: '/assets/images/default-pet.png',
        status: '待领养'
      },
      {
        id: 4,
        name: '花花',
        type: '猫咪',
        breed: '布偶猫',
        age: '2岁',
        gender: '母',
        image: '/assets/images/default-pet.png',
        status: '待领养'
      }
    ];

    this.setData({
      pets: mockPets,
      loading: false,
      error: false
    });

    // 显示提示信息（可选）
    wx.showToast({
      title: '使用本地数据',
      icon: 'none',
      duration: 1500
    });
  },

  /**
   * 跳转到宠物详情页
   */
  goToPetDetail: function(e) {
    const { id } = e.currentTarget.dataset;
    
    if (!id) {
      wx.showToast({
        title: '宠物信息有误',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: `/pages/adopt/detail/detail?id=${id}`
    });
  },

  /**
   * 跳转到功能页面
   */
  goToPage: function(e) {
    const { url } = e.currentTarget.dataset;
    
    if (!url) {
      wx.showToast({
        title: '页面链接有误',
        icon: 'none'
      });
      return;
    }
    
    // 判断是否为 tabBar 页面
    const tabBarPages = [
      '/pages/home/<USER>',
      '/pages/adopt/adopt', 
      '/pages/shop/shop',
      '/pages/hospital/hospital',
      '/pages/mine/mine'
    ];
    
    if (tabBarPages.includes(url)) {
      wx.switchTab({ url });
    } else {
      wx.navigateTo({ url });
    }
  },

  /**
   * 轮播图点击事件
   */
  onBannerTap: function(e) {
    const { url } = e.currentTarget.dataset;
    if (url) {
      this.goToPage(e);
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {
    console.log('🏠 首页渲染完成');
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    console.log('🏠 首页显示');
    // 每次显示页面时检查登录状态
    this.checkLoginStatus();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
    console.log('🏠 首页隐藏');
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
    console.log('🏠 首页卸载');
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {
    console.log('🔄 下拉刷新');
    
    // 重新加载数据
    this.getPets();
    
    // 延迟停止下拉刷新，提升用户体验
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    console.log('📄 上拉触底');
    // 这里可以实现加载更多数据的逻辑
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {
    return {
      title: '宠物之家 - 给流浪宠物一个温暖的家',
      path: '/pages/home/<USER>',
      imageUrl: '/assets/images/logo.png'
    };
  }
});