import { deleteAccount } from '../../services/userService';

const userType = 'COMMON'; // 假设为普通用户

deleteAccount(userType)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            wx.showToast({
                title: '注销成功',
                icon: 'success'
            });
        } else {
            wx.showToast({
                title: res.message || '注销失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('注销出错', err);
        wx.showToast({
            title: '注销出错，请重试',
            icon: 'none'
        });
    });