<!--pages/rescue/animal-manage/animal-manage.wxml-->
<view class="container">
  <!-- 加载动物信息状态 -->
  <view wx:if="{{loadingAnimalInfo}}" class="loading-container">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载动物信息中...</text>
    </view>
  </view>

  <!-- 页面内容 -->
  <view wx:else>
    <!-- 页面头部 -->
    <view class="page-header">
      <view class="header-content">
        <text class="page-title">{{pageMode === 'edit' ? '编辑动物信息' : '上传动物信息'}}</text>
        <text class="page-subtitle">{{pageMode === 'edit' ? '修改动物档案信息' : '为流浪动物建立档案，帮助它们找到温暖的家'}}</text>
      </view>
      <view class="header-actions">
        <view class="action-btn" bindtap="clearForm">
          <text class="action-icon">🗑️</text>
          <text class="action-text">清空</text>
        </view>
      </view>
    </view>

    <!-- 表单区域 -->
    <view class="form-container">
      <!-- 动物照片上传 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">动物照片</text>
          <text class="required-mark">*</text>
        </view>
        
        <view class="photo-upload-area">
          <!-- 已选择图片预览 -->
          <view wx:if="{{selectedImage}}" class="photo-preview">
            <image 
              class="preview-image" 
              src="{{imagePreview}}" 
              mode="aspectFill"
              bindtap="previewImage"
            />
            <view class="photo-actions">
              <view class="photo-action-btn" bindtap="chooseImage">
                <text class="action-icon">📷</text>
                <text class="action-text">重选</text>
              </view>
              <view class="photo-action-btn delete-btn" bindtap="removeImage">
                <text class="action-icon">❌</text>
                <text class="action-text">删除</text>
              </view>
            </view>
          </view>
          
          <!-- 上传按钮 -->
          <view wx:else class="photo-upload-btn" bindtap="chooseImage">
            <view class="upload-icon">📷</view>
            <text class="upload-text">选择照片</text>
            <text class="upload-hint">建议上传清晰的动物照片</text>
          </view>
        </view>
        
        <view wx:if="{{formErrors.photo}}" class="error-text">{{formErrors.photo}}</view>
      </view>

      <!-- 基本信息 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">基本信息</text>
        </view>
        
        <!-- 动物品种 -->
        <view class="form-item">
          <view class="item-label">
            <text class="label-text">动物品种</text>
            <text class="required-mark">*</text>
          </view>
          <input 
            class="form-input {{formErrors.breed ? 'error' : ''}}"
            placeholder="请输入动物品种（如：拉布拉多、中华田园猫）"
            value="{{animalForm.breed}}"
            data-field="breed"
            bindinput="onInputChange"
            maxlength="50"
          />
          <view wx:if="{{formErrors.breed}}" class="error-text">{{formErrors.breed}}</view>
        </view>

        <!-- 性别选择 -->
        <view class="form-item">
          <view class="item-label">
            <text class="label-text">性别</text>
            <text class="required-mark">*</text>
          </view>
          <picker 
            class="form-picker {{formErrors.gender ? 'error' : ''}}"
            range="{{genderOptions}}"
            value="{{genderIndex}}"
            bindchange="onGenderChange"
          >
            <view class="picker-content">
              <text class="picker-text">{{animalForm.gender || '请选择性别'}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
          <view wx:if="{{formErrors.gender}}" class="error-text">{{formErrors.gender}}</view>
        </view>

        <!-- 年龄输入 -->
        <view class="form-item">
          <view class="item-label">
            <text class="label-text">年龄（月）</text>
            <text class="required-mark">*</text>
          </view>
          <input 
            class="form-input {{formErrors.age ? 'error' : ''}}"
            type="number"
            placeholder="请输入年龄（单位：月）"
            value="{{animalForm.age}}"
            data-field="age"
            bindinput="onNumberInput"
          />
          <view wx:if="{{formErrors.age}}" class="error-text">{{formErrors.age}}</view>
        </view>

        <!-- 出生日期（可选） -->
        <view class="form-item">
          <view class="item-label">
            <text class="label-text">出生日期</text>
            <text class="optional-mark">（可选）</text>
          </view>
          <picker 
            class="form-picker"
            mode="date"
            value="{{animalForm.birthDate}}"
            end="{{currentDate}}"
            bindchange="onBirthDateChange"
          >
            <view class="picker-content">
              <text class="picker-text">{{animalForm.birthDate || '请选择出生日期'}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
          <view wx:if="{{formErrors.birthDate}}" class="error-text">{{formErrors.birthDate}}</view>
        </view>
      </view>

      <!-- 来源和状态 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">来源与状态</text>
        </view>
        
        <!-- 动物来源 -->
        <view class="form-item">
          <view class="item-label">
            <text class="label-text">动物来源</text>
            <text class="required-mark">*</text>
          </view>
          <input 
            class="form-input {{formErrors.source ? 'error' : ''}}"
            placeholder="请输入动物来源（如：街头救助、转送救助）"
            value="{{animalForm.source}}"
            data-field="source"
            bindinput="onInputChange"
            maxlength="100"
          />
          <view wx:if="{{formErrors.source}}" class="error-text">{{formErrors.source}}</view>
        </view>

        <!-- 动物状态 -->
        <view class="form-item">
          <view class="item-label">
            <text class="label-text">动物状态</text>
            <text class="required-mark">*</text>
          </view>
          <picker 
            class="form-picker {{formErrors.status ? 'error' : ''}}"
            range="{{statusOptions}}"
            value="{{statusIndex}}"
            bindchange="onStatusChange"
          >
            <view class="picker-content">
              <text class="picker-text">{{animalForm.status || '请选择动物状态'}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
          <view wx:if="{{formErrors.status}}" class="error-text">{{formErrors.status}}</view>
        </view>
      </view>

      <!-- 医疗记录 -->
      <view class="form-section">
        <view class="section-title">
          <text class="title-text">医疗记录</text>
          <text class="optional-mark">（可选）</text>
        </view>
        
        <view class="form-item">
          <textarea 
            class="form-textarea {{formErrors.medicalRecord ? 'error' : ''}}"
            placeholder="请输入医疗记录，如疫苗接种情况、健康状况、治疗记录等（最多500字符）"
            value="{{animalForm.medicalRecord}}"
            data-field="medicalRecord"
            bindinput="onInputChange"
            maxlength="500"
            auto-height="{{true}}"
            cursor-spacing="20"
          ></textarea>
          <view class="char-count-container">
            <text class="char-count {{animalForm.medicalRecord.length >= 500 ? 'char-limit' : ''}}">
              {{animalForm.medicalRecord.length}}/500
            </text>
          </view>
          <view wx:if="{{formErrors.medicalRecord}}" class="error-text">{{formErrors.medicalRecord}}</view>
        </view>
      </view>

      <!-- 提交说明 -->
      <view class="submit-notice">
        <view class="notice-content">
          <text class="notice-icon">💡</text>
          <view class="notice-text">
            <text class="notice-title">温馨提示</text>
            <text class="notice-desc">{{pageMode === 'edit' ? '请确保修改的信息真实准确，更新后的信息将立即生效' : '请确保信息真实准确，上传的动物信息将用于救助和领养服务'}}</text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="form-actions">
        <view class="action-button cancel-button" bindtap="goBack">
          <text class="button-text">取消</text>
        </view>
        <view 
          class="action-button submit-button {{uploading ? 'loading' : ''}} {{!animalForm.breed || !animalForm.gender || !animalForm.age || !animalForm.source || !animalForm.status || !animalForm.photo ? 'disabled' : ''}}" 
          bindtap="submitForm"
        >
          <view wx:if="{{uploading}}" class="submit-loading">
            <view class="loading-spinner"></view>
            <text class="button-text">{{pageMode === 'edit' ? '修改中...' : '上传中...'}}</text>
          </view>
          <text wx:else class="button-text">{{pageMode === 'edit' ? '保存修改' : '提交上传'}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>