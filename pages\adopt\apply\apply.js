// pages/adopt/apply/apply.js
import adoptService from '../../../services/adoptService';
import animalService from '../../../services/animalService';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 动物ID
    animalId: null,
    // 动物信息
    animal: {},
    // 表单数据
    form: {
      name: '',
      phone: '',
      city: '',
      address: '',
      houseType: '',
      houseArea: '',
      allowPet: '',
      occupation: '',
      workTime: '',
      familyMembers: '',
      hasPetExperience: '',
      petExperience: '',
      adoptReason: '',
      petCareTime: ''
    },
    // 住房类型选项
    houseTypes: ['自有住房', '租房', '与家人同住', '其他'],
    houseTypeIndex: -1,
    // 工作时间选项
    workTimeRange: [
      ['周一至周五', '周一至周六', '每天'],
      ['6小时以内', '6-8小时', '8-10小时', '10小时以上']
    ],
    workTimeIndex: [0, 0],
    // 承诺选项
    commitments: [
      { id: '1', text: '我承诺不会无故遗弃或转送宠物', checked: false },
      { id: '2', text: '我承诺为宠物提供良好的生活和医疗条件', checked: false },
      { id: '3', text: '我承诺接受救助站的回访', checked: false },
      { id: '4', text: '我承诺如有特殊情况无法继续养宠，会联系救助站', checked: false }
    ],
    // 是否可以提交
    canSubmit: false,
    // 已选承诺
    selectedCommitments: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    // 获取传递的参数
    const { id } = options;
    
    if (id) {
      this.setData({ animalId: id });
      // 获取动物详情
      this.getAnimalDetail(id);
      
      // 初始化表单数据
      this.initFormData();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack({ delta: 1 });
      }, 1500);
    }
  },

  /**
   * 初始化表单数据
   */
  initFormData: function() {
    // 获取缓存中的用户信息
    const userInfo = wx.getStorageSync('userInfo') || {};
    
    // 更新表单数据
    this.setData({
      'form.name': userInfo.username || '',
      'form.phone': userInfo.phone || '',
      'form.city': userInfo.city || '',
      'form.address': userInfo.address || ''
    });
  },

  /**
   * 获取动物详情
   * @param {string} animalId - 动物ID
   */
  getAnimalDetail: function(animalId) {
    // 调用服务获取动物详情
    animalService.getAnimalDetail(animalId)
      .then(res => {
        if (res && res.data) {
          // 设置数据
          this.setData({
            animal: res.data
          });
        } else {
          // 使用模拟数据（开发阶段）
          const mockAnimal = {
            id: animalId,
            name: '小白',
            type: '狗狗',
            breed: '萨摩耶'
          };
          
          this.setData({
            animal: mockAnimal
          });
        }
      })
      .catch(err => {
        console.error('获取动物详情失败', err);
        wx.showToast({
          title: '获取动物详情失败',
          icon: 'none'
        });
      });
  },

  /**
   * 住房类型选择器变化事件
   */
  bindHouseTypeChange: function(e) {
    const index = e.detail.value;
    
    this.setData({
      houseTypeIndex: index,
      'form.houseType': this.data.houseTypes[index]
    });
    
    // 检查表单完整性
    this.checkFormComplete();
  },

  /**
   * 工作时间选择器变化事件
   */
  bindWorkTimeChange: function(e) {
    const value = e.detail.value;
    const workTimeValue = `${this.data.workTimeRange[0][value[0]]}，${this.data.workTimeRange[1][value[1]]}`;
    
    this.setData({
      workTimeIndex: value,
      'form.workTime': workTimeValue
    });
    
    // 检查表单完整性
    this.checkFormComplete();
  },

  /**
   * 是否允许养宠物单选按钮变化事件
   */
  allowPetChange: function(e) {
    this.setData({
      'form.allowPet': e.detail.value
    });
    
    // 检查表单完整性
    this.checkFormComplete();
  },

  /**
   * 是否有养宠经验单选按钮变化事件
   */
  hasPetExperienceChange: function(e) {
    this.setData({
      'form.hasPetExperience': e.detail.value
    });
    
    // 检查表单完整性
    this.checkFormComplete();
  },

  /**
   * 承诺复选框变化事件
   */
  checkboxChange: function(e) {
    const selectedValues = e.detail.value;
    const commitments = this.data.commitments.map(item => {
      return {
        ...item,
        checked: selectedValues.includes(item.id)
      };
    });
    
    this.setData({
      commitments,
      selectedCommitments: selectedValues
    });
    
    // 检查表单完整性
    this.checkFormComplete();
  },

  /**
   * 检查表单是否完整
   */
  checkFormComplete: function() {
    const { form, selectedCommitments } = this.data;
    
    // 检查必填字段
    const requiredFields = [
      form.name,
      form.phone,
      form.city,
      form.address,
      form.houseType,
      form.houseArea,
      form.allowPet,
      form.occupation,
      form.workTime,
      form.familyMembers,
      form.hasPetExperience,
      form.adoptReason,
      form.petCareTime
    ];
    
    // 如果有养宠经验，则养宠经验描述为必填
    if (form.hasPetExperience === '是' && !form.petExperience) {
      this.setData({ canSubmit: false });
      return;
    }
    
    // 检查是否所有必填字段都已填写
    const isFormComplete = requiredFields.every(field => field.trim() !== '');
    
    // 检查是否勾选了所有承诺
    const isAllCommitChecked = selectedCommitments.length === this.data.commitments.length;
    
    // 设置是否可以提交
    this.setData({
      canSubmit: isFormComplete && isAllCommitChecked
    });
  },

  /**
   * 提交表单
   */
  submitForm: function(e) {
    // 获取表单数据
    const formData = e.detail.value;
    const { animalId, animal, form } = this.data;
    
    // 检查表单完整性
    this.checkFormComplete();
    
    if (!this.data.canSubmit) {
      wx.showToast({
        title: '请完整填写表单并勾选所有承诺',
        icon: 'none'
      });
      return;
    }
    
    // 构造申请数据
    const applyData = {
      animalId,
      animalName: animal.name,
      animalType: animal.type,
      animalBreed: animal.breed,
      ...formData,
      houseType: form.houseType,
      workTime: form.workTime,
      allowPet: form.allowPet,
      hasPetExperience: form.hasPetExperience
    };
    
    // 显示加载中
    wx.showLoading({
      title: '提交中',
      mask: true
    });
    
    // 调用服务提交申请
    adoptService.applyAdopt(applyData)
      .then(res => {
        wx.hideLoading();
        
        if (res && res.code === 20) {
          // 提交成功
          wx.showToast({
            title: '申请提交成功',
            icon: 'success'
          });
          
          // 延迟返回
          setTimeout(() => {
            wx.navigateBack({
              delta: 1
            });
          }, 1500);
        } else {
          // 使用模拟成功响应（开发阶段）
          wx.showToast({
            title: '申请提交成功',
            icon: 'success'
          });
          
          // 延迟返回
          setTimeout(() => {
            wx.navigateBack({
              delta: 1
            });
          }, 1500);
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('申请提交失败', err);
        wx.showToast({
          title: '申请提交失败，请稍后重试',
          icon: 'none'
        });
      });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  }
})