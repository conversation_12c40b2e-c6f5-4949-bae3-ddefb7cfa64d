/* pages/encyclopedia/encyclopedia.wxss */

.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

/* 搜索栏样式 */
.search-section {
  margin-bottom: 30rpx;
}

.search-bar {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 50rpx;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
}

.search-btn {
  padding: 10rpx;
  margin-left: 20rpx;
}

.search-icon {
  font-size: 32rpx;
  color: #666;
}

.clear-btn {
  padding: 10rpx;
  margin-left: 10rpx;
}

.clear-icon {
  font-size: 40rpx;
  color: #999;
  font-weight: bold;
}

/* 加载状态 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e1e1e1;
  border-top: 4rpx solid #1989fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

/* 搜索结果 */
.result-section {
  margin-top: 20rpx;
}

.result-header {
  margin-bottom: 20rpx;
}

.result-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.animal-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.animal-card {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.animal-card:active {
  transform: scale(0.98);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.animal-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.view-detail {
  font-size: 24rpx;
  color: #1989fa;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.info-item {
  display: flex;
  align-items: flex-start;
  line-height: 1.5;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  min-width: 180rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

/* 空状态 */
.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

/* 错误状态 */
.error-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.error-title {
  font-size: 32rpx;
  color: #ff4444;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.error-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

/* 默认状态 */
.default-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.default-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.default-title {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.default-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 50rpx;
  line-height: 1.5;
}

/* 示例标签 */
.example-tags {
  width: 100%;
}

.example-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  justify-content: center;
}

.example-tag {
  background: #e8f4ff;
  color: #1989fa;
  padding: 15rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  border: 2rpx solid #d4edff;
  transition: all 0.2s ease;
}

.example-tag:active {
  background: #1989fa;
  color: #ffffff;
}

/* 重试按钮 */
.retry-btn {
  background: #1989fa;
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
}

.retry-btn:active {
  background: #1976d2;
}