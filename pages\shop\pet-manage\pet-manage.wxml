<!-- pages/shop/pet-manage/pet-manage.wxml -->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">{{mode === 'edit' ? '编辑宠物信息' : '上传宠物信息'}}</text>
    <text class="page-subtitle">{{mode === 'edit' ? '修改宠物的详细信息' : '请填写完整的宠物信息'}}</text>
  </view>

  <!-- 数据加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 宠物信息表单 -->
  <view class="form-container" wx:else>
    
    <!-- 宠物图片上传 -->
    <view class="form-section">
      <view class="section-title">宠物照片</view>
      <view class="photo-upload">
        <view class="photo-preview" wx:if="{{formData.photo}}">
          <image class="preview-image" src="{{formData.photo}}" mode="aspectFill"></image>
          <view class="photo-mask" bindtap="chooseImage">
            <text class="mask-text">重新选择</text>
          </view>
        </view>
        <view class="photo-placeholder" wx:else bindtap="chooseImage">
          <view class="placeholder-icon">📷</view>
          <text class="placeholder-text">点击上传照片</text>
        </view>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="form-section">
      <view class="section-title">基本信息</view>
      
      <!-- 宠物品种 -->
      <view class="form-item">
        <view class="item-label">品种 <text class="required">*</text></view>
        <input 
          class="item-input" 
          placeholder="请输入宠物品种（如：拉布拉多）" 
          value="{{formData.breed}}"
          data-field="breed"
          bindinput="onInputChange"
          maxlength="50"
        />
      </view>

      <!-- 性别选择 -->
      <view class="form-item">
        <view class="item-label">性别 <text class="required">*</text></view>
        <view class="gender-selector">
          <view 
            class="gender-option {{formData.gender === '男' ? 'active' : ''}}" 
            data-gender="男" 
            bindtap="selectGender"
          >
            <text>♂ 男</text>
          </view>
          <view 
            class="gender-option {{formData.gender === '女' ? 'active' : ''}}" 
            data-gender="女" 
            bindtap="selectGender"
          >
            <text>♀ 女</text>
          </view>
        </view>
      </view>

      <!-- 年龄 -->
      <view class="form-item">
        <view class="item-label">年龄 <text class="required">*</text></view>
        <view class="input-with-unit">
          <input 
            class="item-input" 
            placeholder="请输入年龄" 
            value="{{formData.age}}"
            data-field="age"
            bindinput="onInputChange"
            type="number"
          />
          <text class="input-unit">个月</text>
        </view>
      </view>
    </view>

    <!-- 商品信息 -->
    <view class="form-section">
      <view class="section-title">商品信息</view>
      
      <!-- 库存数量 -->
      <view class="form-item">
        <view class="item-label">库存数量 <text class="required">*</text></view>
        <input 
          class="item-input" 
          placeholder="请输入库存数量" 
          value="{{formData.stock}}"
          data-field="stock"
          bindinput="onInputChange"
          type="number"
        />
      </view>

      <!-- 价格 -->
      <view class="form-item">
        <view class="item-label">价格 <text class="required">*</text></view>
        <view class="input-with-unit">
          <input 
            class="item-input" 
            placeholder="请输入价格" 
            value="{{formData.price}}"
            data-field="price"
            bindinput="onInputChange"
            type="digit"
          />
          <text class="input-unit">元</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-container" wx:if="{{!loading}}">
    <button 
      class="submit-btn {{!canSubmit ? 'disabled' : ''}}" 
      bindtap="submitForm"
      disabled="{{!canSubmit || submitting}}"
    >
      {{submitting ? (mode === 'edit' ? '修改中...' : '上传中...') : (mode === 'edit' ? '修改宠物信息' : '上传宠物信息')}}
    </button>
    
    <!-- 编辑模式下的提示 -->
    <view class="form-tip" wx:if="{{mode === 'edit' && !canSubmit && !submitting}}">
      <text class="tip-text">{{formData.breed ? '修改信息后才能提交' : '请完善宠物信息'}}</text>
    </view>
  </view>

  <!-- 加载遮罩 -->
  <view class="loading-mask" wx:if="{{submitting}}">
    <view class="loading-content">
      <view class="loading-icon"></view>
      <text class="loading-text">{{mode === 'edit' ? '正在修改...' : '正在上传...'}}</text>
    </view>
  </view>
</view>