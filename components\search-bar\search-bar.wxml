<!-- components/search-bar/search-bar.wxml -->
<view class="search-bar">
  <view 
    class="search-input-container {{shape === 'round' ? 'round' : 'square'}}" 
    style="background-color: {{background}};"
    bindtap="onClick"
  >
    <view class="search-icon-container">
      <image src="/assets/images/search.png" class="search-icon" mode="aspectFit"></image>
    </view>
    <input 
      class="search-input" 
      type="text" 
      placeholder="{{placeholder}}" 
      placeholder-class="search-placeholder" 
      value="{{innerValue}}" 
      focus="{{focus}}" 
      disabled="{{disabled}}" 
      bindinput="onInput" 
      bindconfirm="onConfirm" 
      bindfocus="onFocus" 
      bindblur="onBlur" 
    />
    <view 
      wx:if="{{innerValue.length > 0}}" 
      class="search-clear" 
      catchtap="onClear" 
    >
      <image src="/assets/images/close-circle.png" class="clear-icon" mode="aspectFit"></image>
    </view>
  </view>
  <view 
    wx:if="{{showCancel || isFocus}}" 
    class="search-cancel" 
    bindtap="onCancel"
  >
    {{cancelText}}
  </view>
</view>