/* pages/login/login.wxss */

/* 全局容器 */
.login-container {
  padding: 30rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 50rpx;
  padding-top: 60rpx;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
  border-radius: 50%;
  box-shadow: 0 8rpx 24rpx rgba(78, 170, 168, 0.3);
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

/* 表单容器 */
.form-container {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.required {
  color: #f56c6c;
  margin-left: 4rpx;
  font-size: 24rpx;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #4eaaa8;
  background-color: #fff;
  box-shadow: 0 0 0 6rpx rgba(78, 170, 168, 0.1);
}

.input-error {
  border-color: #f56c6c !important;
  background-color: #fef0f0 !important;
}

.error-message {
  font-size: 24rpx;
  color: #f56c6c;
  margin-top: 8rpx;
  display: flex;
  align-items: center;
}

.error-message::before {
  content: "⚠️";
  margin-right: 6rpx;
  font-size: 20rpx;
}

/* 密码输入框 */
.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  z-index: 10;
}

.eye-icon {
  width: 36rpx;
  height: 36rpx;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.password-toggle:active .eye-icon {
  opacity: 1;
}

/* 选择器 */
.form-picker {
  width: 100%;
  height: 88rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
}

.form-picker:active {
  background-color: #fff;
  border-color: #4eaaa8;
}

.picker-arrow {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

/* 表单选项行 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

/* 记住账号 */
.remember-account {
  display: flex;
  align-items: center;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  margin-right: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.checkbox-checked {
  background-color: #4eaaa8;
  border-color: #4eaaa8;
}

.checkbox-icon {
  width: 20rpx;
  height: 20rpx;
}

.remember-text {
  font-size: 26rpx;
  color: #666;
}

.forgot-password {
  font-size: 26rpx;
  color: #4eaaa8;
  text-decoration: underline;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #4eaaa8, #3d8b89);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(78, 170, 168, 0.4);
  transition: all 0.3s ease;
  border: none;
  margin-bottom: 30rpx;
}

.login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(78, 170, 168, 0.4);
}

.btn-disabled {
  background: #a6d2d1;
  box-shadow: none;
  transform: none;
}

/* 注册链接 */
.register-link {
  text-align: center;
  font-size: 28rpx;
  color: #666;
}

.link {
  color: #4eaaa8;
  font-weight: 500;
  text-decoration: underline;
}

/* 响应式设计 */
@media screen and (max-width: 320px) {
  .login-container {
    padding: 20rpx;
  }
  
  .form-container {
    padding: 30rpx;
  }
  
  .header {
    padding-top: 40rpx;
    margin-bottom: 40rpx;
  }
  
  .logo {
    width: 120rpx;
    height: 120rpx;
  }
  
  .title {
    font-size: 36rpx;
  }
}