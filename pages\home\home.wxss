/* pages/home/<USER>/

/* 轮播图 */
.banner {
  width: 100%;
  height: 350rpx;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 功能菜单 */
.menu-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 20rpx 0;
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
}

.menu-item {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.menu-name {
  font-size: 24rpx;
  color: #333;
}

/* 分区标题 */
.section {
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 24rpx;
  color: #999;
}

/* 宠物列表 */
.pet-list {
  display: flex;
  flex-direction: column;
}

.pet-card {
  display: flex;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.pet-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.pet-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.pet-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.pet-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.pet-status {
  align-self: flex-start;
  font-size: 22rpx;
  color: #fff;
  background-color: #4eaaa8;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
}