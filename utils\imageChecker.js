// ==================== utils/imageChecker.js ====================
/**
 * 图片资源检查和管理工具
 * 用于检查项目中的图片资源是否存在
 */

import { ImageAssets, CommonImages } from './imageAssets';

/**
 * 检查单个图片是否存在
 * @param {string} imagePath - 图片路径
 * @returns {Promise<boolean>} 图片是否存在
 */
export const checkSingleImage = (imagePath) => {
  return new Promise((resolve) => {
    wx.getImageInfo({
      src: imagePath,
      success: () => {
        console.log('✅ 图片存在:', imagePath);
        resolve(true);
      },
      fail: (error) => {
        console.warn('❌ 图片不存在:', imagePath, error);
        resolve(false);
      }
    });
  });
};

/**
 * 批量检查图片资源
 * @param {Object} imageConfig - 图片配置对象
 * @returns {Promise<Object>} 检查结果
 */
export const checkImageBatch = async (imageConfig) => {
  const results = {
    total: 0,
    exists: 0,
    missing: 0,
    missingList: []
  };
  
  const checkPromises = [];
  
  // 递归检查所有图片
  const traverse = (obj, parentKey = '') => {
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'string' && (value.includes('.png') || value.includes('.jpg') || value.includes('.gif'))) {
        // 这是一个图片路径
        const fullKey = parentKey ? `${parentKey}.${key}` : key;
        results.total++;
        
        checkPromises.push(
          checkSingleImage(value).then(exists => {
            if (exists) {
              results.exists++;
            } else {
              results.missing++;
              results.missingList.push({
                key: fullKey,
                path: value
              });
            }
          })
        );
      } else if (typeof value === 'object' && value !== null) {
        // 递归检查嵌套对象
        const nestedKey = parentKey ? `${parentKey}.${key}` : key;
        traverse(value, nestedKey);
      }
    }
  };
  
  traverse(imageConfig);
  
  // 等待所有检查完成
  await Promise.all(checkPromises);
  
  return results;
};

/**
 * 检查项目中的所有图片资源
 * @returns {Promise<Object>} 完整的检查报告
 */
export const checkAllProjectImages = async () => {
  console.log('🔍 开始检查项目图片资源...');
  
  const report = {
    timestamp: new Date().toISOString(),
    categories: {}
  };
  
  // 检查基础图标
  console.log('📋 检查基础图标...');
  report.categories.icons = await checkImageBatch(ImageAssets.ICONS);
  
  // 检查默认图片
  console.log('📋 检查默认图片...');
  report.categories.defaults = await checkImageBatch(ImageAssets.DEFAULTS);
  
  // 检查轮播图
  console.log('📋 检查轮播图...');
  report.categories.banners = await checkImageBatch(ImageAssets.BANNERS);
  
  // 检查分类图标
  console.log('📋 检查分类图标...');
  report.categories.categories = await checkImageBatch(ImageAssets.CATEGORIES);
  
  // 检查底部导航栏图标
  console.log('📋 检查底部导航栏图标...');
  report.categories.tabbar = await checkImageBatch(ImageAssets.TABBAR);
  
  // 计算总体统计
  report.summary = {
    totalImages: 0,
    existingImages: 0,
    missingImages: 0,
    missingList: []
  };
  
  Object.values(report.categories).forEach(category => {
    report.summary.totalImages += category.total;
    report.summary.existingImages += category.exists;
    report.summary.missingImages += category.missing;
    report.summary.missingList.push(...category.missingList);
  });
  
  // 输出报告
  console.log('📊 图片资源检查报告:');
  console.log(`总计: ${report.summary.totalImages} 张图片`);
  console.log(`存在: ${report.summary.existingImages} 张图片`);
  console.log(`缺失: ${report.summary.missingImages} 张图片`);
  
  if (report.summary.missingList.length > 0) {
    console.warn('❌ 缺失的图片资源:');
    report.summary.missingList.forEach(item => {
      console.warn(`  ${item.key}: ${item.path}`);
    });
  } else {
    console.log('✅ 所有图片资源都存在!');
  }
  
  return report;
};

/**
 * 创建缺失图片的占位符
 * @param {Array} missingList - 缺失图片列表
 */
export const createPlaceholders = (missingList) => {
  console.log('🔧 为缺失的图片创建占位符建议:');
  
  const suggestions = {
    required: [], // 必需的图片
    optional: [], // 可选的图片
    fallbacks: [] // 可以使用备用方案的图片
  };
  
  missingList.forEach(item => {
    const path = item.path.toLowerCase();
    
    if (path.includes('tabbar') || path.includes('logo')) {
      suggestions.required.push({
        ...item,
        priority: 'high',
        description: '导航栏和Logo图片，影响用户体验'
      });
    } else if (path.includes('default') || path.includes('empty')) {
      suggestions.required.push({
        ...item,
        priority: 'medium',
        description: '默认占位图片，需要提供'
      });
    } else if (path.includes('banner')) {
      suggestions.optional.push({
        ...item,
        priority: 'medium',
        description: '轮播图，可以暂时隐藏该功能'
      });
    } else {
      suggestions.fallbacks.push({
        ...item,
        priority: 'low',
        description: '可以使用其他图片或图标字体替代'
      });
    }
  });
  
  return suggestions;
};

/**
 * 生成图片资源清单
 * @returns {Object} 图片资源清单
 */
export const generateImageManifest = () => {
  const manifest = {
    required: [
      // 必需的基础图片
      '/assets/images/default-pet.png',
      '/assets/images/logo.png',
      '/assets/images/empty.png',
    ],
    icons: [
      // 基础图标
      '/assets/images/search.png',
      '/assets/images/heart.png',
      '/assets/images/heart-filled.png',
      '/assets/images/location-pin.png',
      '/assets/images/back-white.png',
      '/assets/images/back-black.png',
      '/assets/images/check.png',
      '/assets/images/close-circle.png',
      '/assets/images/arrow-down.png',
      '/assets/images/eye-open.png',
      '/assets/images/eye-close.png'
    ],
    tabbar: [
      // 底部导航栏图标
      '/assets/images/tabbar/home.png',
      '/assets/images/tabbar/home-active.png',
      '/assets/images/tabbar/adopt.png',
      '/assets/images/tabbar/adopt-active.png',
      '/assets/images/tabbar/shop.png',
      '/assets/images/tabbar/shop-active.png',
      '/assets/images/tabbar/hospital.png',
      '/assets/images/tabbar/hospital-active.png',
      '/assets/images/tabbar/mine.png',
      '/assets/images/tabbar/mine-active.png'
    ],
    categories: [
      // 分类图标
      '/assets/images/cat-encyclopedia.png',
      '/assets/images/cat-shop.png',
      '/assets/images/cat-rescue.png',
      '/assets/images/cat-hospital.png',
      '/assets/images/cat-adopt.png'
    ],
    optional: [
      // 可选图片
      '/assets/images/banner1.png',
      '/assets/images/banner2.png',
      '/assets/images/banner3.png',
      '/assets/images/loading.gif'
    ]
  };
  
  return manifest;
};

/**
 * 在开发模式下自动检查图片资源
 */
export const autoCheckInDev = () => {
  // 只在开发环境执行
  if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
    const systemInfo = wx.getSystemInfoSync();
    // 检查是否为开发工具
    if (systemInfo.platform === 'devtools') {
      setTimeout(() => {
        checkAllProjectImages().catch(error => {
          console.error('图片资源检查失败:', error);
        });
      }, 1000); // 延迟1秒执行，确保页面加载完成
    }
  }
};

export default {
  checkSingleImage,
  checkImageBatch,
  checkAllProjectImages,
  createPlaceholders,
  generateImageManifest,
  autoCheckInDev
};

// ==================== 使用说明 ====================
/**
 * 在 app.js 中使用自动检查功能：
 * 
 * import { autoCheckInDev } from './utils/imageChecker';
 * 
 * App({
 *   onLaunch() {
 *     // 开发环境下自动检查图片资源
 *     autoCheckInDev();
 *   }
 * });
 * 
 * 在页面中手动检查特定图片：
 * 
 * import { checkSingleImage } from './utils/imageChecker';
 * 
 * Page({
 *   async onLoad() {
 *     const exists = await checkSingleImage('/assets/images/logo.png');
 *     if (!exists) {
 *       console.warn('Logo图片不存在');
 *     }
 *   }
 * });
 */