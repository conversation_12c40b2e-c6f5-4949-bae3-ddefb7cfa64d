import { getShopEvaluations } from '../../services/shopService';

const params = {
    shopId: 1
};

getShopEvaluations(params)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('商店评价:', res.data);
        } else {
            wx.showToast({
                title: res.message || '获取商店评价失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('获取商店评价出错', err);
        wx.showToast({
            title: '获取商店评价出错，请重试',
            icon: 'none'
        });
    });