import { searchPets } from '../../services/searchService';

const params = {
    // 搜索参数
};

searchPets(params)
    .then(res => {
        if (res.code === CONFIG.ERROR_CODES.SUCCESS) {
            console.log('宠物搜索结果:', res.data);
        } else {
            wx.showToast({
                title: res.message || '宠物搜索失败',
                icon: 'none'
            });
        }
    })
    .catch(err => {
        console.error('宠物搜索出错', err);
        wx.showToast({
            title: '宠物搜索出错，请重试',
            icon: 'none'
        });
    });