// pages/rescue/adoption-manage/adoption-manage.js
import rescueService from '../../../services/rescueService'
import userService from '../../../services/userService'

Page({
  data: {
    stationId: null,
    stationInfo: {},
    loading: true,
    
    // 领养申请列表
    adoptionRequests: [],
    requestsLoading: false,
    requestPage: 1,
    requestPageSize: 10,
    requestHasMore: true,
    requestTotal: 0,
    
    // 状态筛选
    statusFilter: 'all', // all, 待审核, 已匹配, 已领养, 已取消
    statusOptions: [
      { value: 'all', label: '全部' },
      { value: '待审核', label: '待审核' },
      { value: '已匹配', label: '已匹配' },
      { value: '已领养', label: '已领养' },
      { value: '已取消', label: '已取消' }
    ],
    showStatusPicker: false,
    
    // 状态修改
    showStatusModal: false,
    selectedRequest: null,
    newStatus: '',
    statusUpdateOptions: ['待审核', '已匹配', '已领养', '已取消'],
    updatingStatus: false,
    
    // 申请人详情弹窗
    showApplicantModal: false,
    
    // 权限控制
    isRescueStationUser: false,
    currentUserId: null,
    currentUserType: null
  },

  onLoad(options) {
    const { stationId } = options;
    
    if (!stationId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({ stationId: parseInt(stationId) });
    this.checkUserPermission();
    this.loadStationInfo();
    this.loadAdoptionRequests();
  },

  /**
   * 检查用户权限
   */
  checkUserPermission() {
    const userInfo = userService.getCurrentUser();
    
    if (!userInfo) {
      wx.showModal({
        title: '权限不足',
        content: '请先登录',
        showCancel: false,
        success: () => {
          wx.navigateTo({
            url: '/pages/login/login'
          });
        }
      });
      return;
    }

    const userType = userInfo.userType || '';
    const isRescueStationUser = userType.length >= 3 && userType.length <= 4;
    
    if (!isRescueStationUser) {
      wx.showModal({
        title: '权限不足',
        content: '仅救助站用户可管理领养申请',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    this.setData({
      isRescueStationUser: true,
      currentUserId: userInfo.id,
      currentUserType: userType
    });
  },

  /**
   * 加载救助站信息
   */
  async loadStationInfo() {
    try {
      // TODO: 调用真实接口获取救助站信息
      // const result = await rescueService.getStationDetail(this.data.stationId);
      
      // 暂时使用模拟数据
      const mockStationInfo = {
        id: this.data.stationId,
        name: '爱心动物救助站',
        address: '北京市朝阳区宠物救助大街123号'
      };

      this.setData({
        stationInfo: mockStationInfo
      });
    } catch (error) {
      console.error('获取救助站信息失败:', error);
      wx.showToast({
        title: '获取救助站信息失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载领养申请列表 - 调用真实接口
   */
  async loadAdoptionRequests(isRefresh = false) {
    if (isRefresh) {
      this.setData({
        requestPage: 1,
        adoptionRequests: [],
        requestHasMore: true,
        requestTotal: 0
      });
    }

    this.setData({ requestsLoading: true });

    try {
      // 调用查看领养匹配状态接口
      const requestData = {
        page: this.data.requestPage,
        pageSize: this.data.requestPageSize
      };
      
      const result = await rescueService.getAdoptionStatus(requestData);
      
      if (result.code === 200) {
        const currentRequests = isRefresh ? [] : this.data.adoptionRequests;
        let newRequests = result.data || [];
        
        // 状态筛选
        if (this.data.statusFilter !== 'all') {
          newRequests = newRequests.filter(item => item.adoptionStatus === this.data.statusFilter);
        }
        
        // 数据格式转换，适配现有页面显示
        const formattedRequests = newRequests.map(item => ({
          id: item.id,
          userId: item.id, // 使用记录ID作为用户ID
          userName: item.username,
          userPhone: item.contact,
          animalId: item.animalId,
          animalName: `动物${item.animalId}`, // 默认名称，后续可通过其他接口获取
          animalType: '待获取', // 需要其他接口获取
          animalBreed: '待获取', // 需要其他接口获取
          animalPhoto: '/assets/images/default-animal.jpg', // 默认图片
          status: item.adoptionStatus,
          
          // 新增的申请人详细信息
          housing: item.housing,
          job: item.job,
          healthCondition: item.healthCondition,
          freeTime: item.freeTime,
          
          // 默认值，后续可通过其他接口获取
          applyReason: '申请理由待获取',
          createTime: '待获取',
          updateTime: '待获取'
        }));
        
        this.setData({
          adoptionRequests: [...currentRequests, ...formattedRequests],
          requestPage: this.data.requestPage + 1,
          requestHasMore: newRequests.length >= this.data.requestPageSize,
          requestTotal: result.total || 0,
          requestsLoading: false
        });
        
        // 获取动物详细信息
        this.loadAnimalDetails(formattedRequests);
        
      } else {
        throw new Error(result.message || '获取数据失败');
      }

    } catch (error) {
      console.error('获取领养申请列表失败:', error);
      this.setData({ requestsLoading: false });
      
      wx.showToast({
        title: error.message || '获取申请列表失败',
        icon: 'none'
      });
    }
  },

  /**
   * 获取动物详细信息
   * 这里需要额外的接口来获取动物的详细信息
   */
  async loadAnimalDetails(requests) {
    try {
      // TODO: 调用获取动物详情的接口
      // 这里需要你提供动物详情接口文档
      
      const animalIds = requests.map(item => item.animalId);
      // const animalDetails = await rescueService.getAnimalsDetail(animalIds);
      
      // 暂时保持默认值，等你提供动物详情接口后再完善
      console.log('需要获取这些动物的详细信息:', animalIds);
      
    } catch (error) {
      console.error('获取动物详情失败:', error);
    }
  },

  /**
   * 显示状态筛选器
   */
  showStatusFilter() {
    this.setData({ showStatusPicker: true });
  },

  /**
   * 隐藏状态筛选器
   */
  hideStatusFilter() {
    this.setData({ showStatusPicker: false });
  },

  /**
   * 选择状态筛选
   */
  selectStatusFilter(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({ 
      statusFilter: status,
      showStatusPicker: false
    });
    this.loadAdoptionRequests(true);
  },

  /**
   * 显示状态修改弹窗
   */
  showUpdateStatusModal(e) {
    const request = e.currentTarget.dataset.request;
    
    if (!request) {
      wx.showToast({
        title: '申请信息错误',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showStatusModal: true,
      selectedRequest: request,
      newStatus: request.status
    });
  },

  /**
   * 隐藏状态修改弹窗
   */
  hideStatusModal() {
    this.setData({
      showStatusModal: false,
      selectedRequest: null,
      newStatus: '',
      updatingStatus: false
    });
  },

  /**
   * 选择新状态
   */
  selectNewStatus(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({ newStatus: status });
  },

  /**
   * 确认修改状态
   */
  async confirmUpdateStatus() {
    const { selectedRequest, newStatus } = this.data;
    
    if (!selectedRequest || !newStatus) {
      wx.showToast({
        title: '请选择状态',
        icon: 'none'
      });
      return;
    }

    if (newStatus === selectedRequest.status) {
      wx.showToast({
        title: '状态未改变',
        icon: 'none'
      });
      return;
    }

    this.setData({ updatingStatus: true });

    try {
      // TODO: 调用修改状态的接口
      // 这里需要你提供修改状态的接口文档
      // const result = await rescueService.updateAdoptionStatus(selectedRequest.id, newStatus);
      
      // 模拟API调用成功
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      wx.showToast({
        title: '修改成功',
        icon: 'success'
      });

      // 更新本地数据
      const updatedRequests = this.data.adoptionRequests.map(item => {
        if (item.id === selectedRequest.id) {
          return { ...item, status: newStatus, updateTime: new Date().toLocaleString() };
        }
        return item;
      });

      this.setData({
        adoptionRequests: updatedRequests,
        updatingStatus: false
      });

      this.hideStatusModal();

    } catch (error) {
      console.error('修改状态失败:', error);
      this.setData({ updatingStatus: false });
      
      let errorMessage = '修改失败，请重试';
      if (error.code === 400) {
        errorMessage = '参数无效';
      } else if (error.code === 403) {
        errorMessage = '无权限修改';
      } else if (error.code === 404) {
        errorMessage = '申请记录不存在';
      } else if (error.message) {
        errorMessage = error.message;
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none'
      });
    }
  },

  /**
   * 拨打申请人电话
   */
  makePhoneCall(e) {
    const { phone } = e.currentTarget.dataset;
    
    if (!phone) {
      wx.showToast({
        title: '暂无联系电话',
        icon: 'none'
      });
      return;
    }

    wx.makePhoneCall({
      phoneNumber: phone,
      fail: () => {
        wx.showToast({
          title: '拨号失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 查看动物详情
   */
  viewAnimalDetail(e) {
    const { animalId } = e.currentTarget.dataset;
    
    if (!animalId) {
      wx.showToast({
        title: '动物信息错误',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/animal/detail/detail?id=${animalId}`
    });
  },

  /**
   * 查看申请人详细信息
   */
  viewApplicantDetail(e) {
    const request = e.currentTarget.dataset.request;
    
    if (!request) {
      wx.showToast({
        title: '申请信息错误',
        icon: 'none'
      });
      return;
    }

    // 显示申请人详细信息弹窗
    this.setData({
      showApplicantModal: true,
      selectedRequest: request
    });
  },

  /**
   * 隐藏申请人详情弹窗
   */
  hideApplicantModal() {
    this.setData({
      showApplicantModal: false,
      selectedRequest: null
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadAdoptionRequests(true);
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (this.data.requestHasMore && !this.data.requestsLoading) {
      this.loadAdoptionRequests();
    }
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: `${this.data.stationInfo.name} - 领养申请管理`,
      path: `/pages/rescue/adoption-manage/adoption-manage?stationId=${this.data.stationId}`
    };
  }
});