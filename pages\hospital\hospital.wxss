/* pages/hospital/hospital.wxss */

.hospital-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

/* ==================== 搜索头部区域 ==================== */
.search-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 120rpx 30rpx 40rpx;
  position: relative;
  overflow: hidden;
}

.header-decoration {
  position: absolute;
  top: -100rpx;
  right: -50rpx;
  width: 300rpx;
  height: 300rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.header-decoration-2 {
  position: absolute;
  bottom: -80rpx;
  left: -60rpx;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
}

.header-content {
  position: relative;
  z-index: 2;
}

.header-title {
  display: block;
  color: white;
  font-size: 56rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 10rpx;
}

.header-subtitle {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
  text-align: center;
  margin-bottom: 30rpx;
}

/* 位置选择器 */
.location-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 40rpx;
  padding: 16rpx 24rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.location-selector .location-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 12rpx;
  filter: brightness(0) invert(1);
}

.location-text {
  color: white;
  font-size: 28rpx;
  font-weight: 500;
  flex: 1;
  text-align: center;
}

.location-loading {
  display: flex;
  gap: 6rpx;
  margin-left: 12rpx;
}

.location-arrow {
  width: 20rpx;
  height: 20rpx;
  margin-left: 12rpx;
  filter: brightness(0) invert(1);
  opacity: 0.7;
}

/* 搜索框样式 */
.search-wrapper {
  margin-bottom: 30rpx;
}

.search-input-container {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50rpx;
  padding: 0 60rpx 0 60rpx;
  backdrop-filter: blur(20rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.search-icon {
  position: absolute;
  left: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 36rpx;
  height: 36rpx;
  opacity: 0.6;
}

.search-input {
  width: 100%;
  height: 100rpx;
  font-size: 30rpx;
  color: #333;
  background: transparent;
  border: none;
}

.search-input::placeholder {
  color: #999;
}

.search-loading {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: 8rpx;
}

.loading-dot {
  width: 8rpx;
  height: 8rpx;
  background: #667eea;
  border-radius: 50%;
  animation: loading-bounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 筛选标签 */
.filter-scroll {
  white-space: nowrap;
}

.filter-container {
  display: inline-flex;
  gap: 20rpx;
  padding: 0 10rpx;
}

.filter-chip {
  flex-shrink: 0;
  position: relative;
  padding: 16rpx 32rpx;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
}

.filter-chip.active {
  background: white;
  color: #667eea;
  border-color: white;
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 20rpx rgba(255, 255, 255, 0.2);
}

.filter-text {
  position: relative;
  z-index: 2;
}

.filter-indicator {
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background: #667eea;
  border-radius: 3rpx;
}

/* ==================== 内容区域 ==================== */
.content-section {
  background: #f8f9fa;
  border-radius: 40rpx 40rpx 0 0;
  margin-top: -20rpx;
  position: relative;
  z-index: 3;
  min-height: 80vh;
}

.hospital-list {
  padding: 40rpx 30rpx;
}

/* ==================== 医院卡片 ==================== */
.hospital-card {
  background: white;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
}

.hospital-card:active {
  transform: scale(0.98);
}

/* 图片容器 */
.card-image-container {
  position: relative;
  height: 320rpx;
  overflow: hidden;
}

.hospital-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.hospital-card:active .hospital-image {
  transform: scale(1.05);
}

/* 评分标签 */
.rating-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  padding: 10rpx 16rpx;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.star-icon {
  width: 28rpx;
  height: 28rpx;
}

.rating-value {
  font-size: 26rpx;
  font-weight: bold;
  color: #ff6b6b;
}

/* 类型标签 */
.type-badge {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.type-text {
  color: white;
}

/* 图片悬停效果 */
.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.hospital-card:active .image-overlay {
  opacity: 1;
}

.overlay-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
}

.overlay-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
  filter: brightness(0) invert(1);
}

.overlay-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* ==================== 卡片内容 ==================== */
.card-content {
  padding: 30rpx;
}

.hospital-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.hospital-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  flex: 1;
}

.hospital-status {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.hospital-status.online {
  background: #e8f5e8;
  color: #27ae60;
}

.hospital-address {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.location-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 12rpx;
  opacity: 0.6;
}

.address-text {
  font-size: 28rpx;
  color: #7f8c8d;
  flex: 1;
}

.hospital-description {
  margin-bottom: 20rpx;
}

.desc-text {
  font-size: 26rpx;
  color: #95a5a6;
  line-height: 1.5;
}

.hospital-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 16rpx 0;
  border-top: 1rpx solid #f0f0f0;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

.meta-text {
  font-size: 24rpx;
  color: #27ae60;
  font-weight: 500;
}

.contact-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.phone-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

.contact-text {
  font-size: 24rpx;
  color: #7f8c8d;
}

/* ==================== 操作按钮 ==================== */
.card-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.detail-btn {
  background: #ecf0f1;
  color: #2c3e50;
}

.detail-btn:active {
  background: #d5dbdb;
  transform: scale(0.98);
}

.appointment-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.appointment-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
}

.btn-icon {
  width: 32rpx;
  height: 32rpx;
}

.appointment-btn .btn-icon {
  filter: brightness(0) invert(1);
}

/* ==================== 装饰元素 ==================== */
.card-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #667eea, #764ba2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.hospital-card:active .card-decoration {
  opacity: 1;
}

.decoration-line {
  width: 100%;
  height: 100%;
  background: inherit;
}

/* ==================== 空状态 ==================== */
.empty-container {
  padding: 120rpx 40rpx;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  margin-bottom: 40rpx;
}

.empty-title {
  display: block;
  font-size: 32rpx;
  color: #2c3e50;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  display: block;
  font-size: 28rpx;
  color: #95a5a6;
  line-height: 1.4;
}

.refresh-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
}

.refresh-icon {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1);
}

/* ==================== 加载状态 ==================== */
.loading-container {
  padding: 120rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #7f8c8d;
}

/* 加载更多 */
.load-more-section {
  padding: 40rpx;
  display: flex;
  justify-content: center;
}

.load-more-content {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.loading-dots {
  display: flex;
  gap: 8rpx;
}

.load-more-text {
  font-size: 28rpx;
  color: #7f8c8d;
}

/* 结束提示 */
.end-section {
  padding: 40rpx;
}

.end-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.end-line {
  flex: 1;
  height: 2rpx;
  background: #e0e0e0;
}

.end-text {
  font-size: 24rpx;
  color: #95a5a6;
  padding: 0 20rpx;
}

/* ==================== 悬浮按钮 ==================== */
.fab-container {
  position: fixed;
  bottom: 60rpx;
  right: 60rpx;
  z-index: 1000;
}

.fab-button {
  width: 112rpx;
  height: 112rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 12rpx 30rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.fab-button:active {
  transform: scale(0.95);
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.4);
}

.fab-icon {
  width: 48rpx;
  height: 48rpx;
  filter: brightness(0) invert(1);
  z-index: 2;
}

.fab-ripple {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  opacity: 0;
  transform: scale(0);
  transition: all 0.6s ease;
}

.fab-button:active .fab-ripple {
  opacity: 1;
  transform: scale(1);
}