/* pages/rescue/adoption-manage/adoption-manage.wxss */

.container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF6F61;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 主要内容 */
.main-content {
  padding-bottom: 40rpx;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #FF6F61 0%, #FF8A75 100%);
  padding: 30rpx;
  color: white;
}

.header-info {
  margin-bottom: 25rpx;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.station-name {
  display: block;
  font-size: 26rpx;
  opacity: 0.9;
  margin-bottom: 8rpx;
}

.request-count {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
}

/* 筛选区域 */
.filter-section {
  display: flex;
  justify-content: flex-end;
}

.filter-btn {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  gap: 10rpx;
}

.filter-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

.filter-text {
  font-size: 26rpx;
}

.filter-arrow {
  font-size: 20rpx;
  opacity: 0.8;
}

/* 申请列表容器 */
.requests-container {
  padding: 20rpx;
}

/* 加载状态 */
.requests-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
  font-size: 26rpx;
  color: #999;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #FF6F61;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

/* 空状态 */
.empty-requests {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
  text-align: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: bold;
}

.empty-hint {
  font-size: 26rpx;
  color: #999;
}

/* 申请列表 */
.requests-list {
  margin-top: 20rpx;
}

/* 申请卡片 */
.request-card {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 25rpx 25rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.applicant-info {
  flex: 1;
}

.applicant-name {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.apply-time {
  display: block;
  font-size: 24rpx;
  color: #999;
}

/* 状态标签 */
.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: white;
  font-weight: bold;
}

.status-Pending_Review {
  background: #ffc107;
}

.status-Matched {
  background: #17a2b8;
}

.status-Adopted {
  background: #28a745;
}

.status-Cancelled {
  background: #dc3545;
}

/* 动物信息 */
.animal-info {
  display: flex;
  padding: 20rpx 25rpx;
  align-items: center;
  gap: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background 0.2s;
}

.animal-info:active {
  background: #f8f9fa;
}

.animal-photo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  object-fit: cover;
}

.animal-details {
  flex: 1;
}

.animal-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.animal-type {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.animal-hint {
  display: block;
  font-size: 22rpx;
  color: #4285f4;
}

/* 申请人详细信息 */
.applicant-details {
  padding: 20rpx 25rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: #fafbfc;
}

.detail-row {
  display: flex;
  margin-bottom: 12rpx;
  align-items: flex-start;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 24rpx;
  color: #666;
  min-width: 140rpx;
  font-weight: bold;
}

.detail-value {
  font-size: 24rpx;
  color: #333;
  flex: 1;
  line-height: 1.4;
  word-break: break-word;
}

/* 申请理由 */
.apply-reason {
  padding: 20rpx 25rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.reason-label {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  font-weight: bold;
}

.reason-content {
  display: block;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  word-break: break-word;
}

/* 操作按钮 */
.card-actions {
  display: flex;
  padding: 20rpx 25rpx;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  height: 70rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  transition: all 0.2s;
}

.action-btn:active {
  transform: scale(0.98);
}

.contact-btn {
  background: #4CAF50;
  color: white;
}

.contact-btn:active {
  background: #45a049;
}

.status-btn {
  background: #FF6F61;
  color: white;
}

.status-btn:active {
  background: #e55a4e;
}

.btn-icon {
  font-size: 24rpx;
}

.btn-text {
  font-size: 26rpx;
}

/* 更新信息 */
.update-info {
  padding: 15rpx 25rpx;
  background: #f8f9fa;
}

.update-text {
  font-size: 22rpx;
  color: #999;
}

/* 提示信息 */
.load-more-hint,
.no-more-hint {
  text-align: center;
  padding: 40rpx 0;
  font-size: 26rpx;
  color: #999;
}

/* 筛选弹窗 */
.picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.picker-content {
  position: relative;
  width: 100%;
  max-height: 50vh;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.picker-close {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #999;
  border-radius: 20rpx;
  background: #f8f9fa;
}

.picker-close:active {
  background: #e9ecef;
}

.picker-list {
  max-height: 40vh;
  overflow-y: auto;
}

.picker-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background 0.2s;
}

.picker-item:active {
  background: #f8f9fa;
}

.picker-item:last-child {
  border-bottom: none;
}

.picker-item.selected {
  background: #fff3f1;
}

.picker-label {
  font-size: 28rpx;
  color: #333;
}

.picker-item.selected .picker-label {
  color: #FF6F61;
  font-weight: bold;
}

.selected-icon {
  font-size: 24rpx;
  color: #FF6F61;
  font-weight: bold;
}

/* 状态修改弹窗 */
.status-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  position: relative;
  width: 600rpx;
  max-height: 80vh;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  animation: modalScale 0.3s ease-out;
}

@keyframes modalScale {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #999;
  border-radius: 20rpx;
  background: #f8f9fa;
}

.modal-close:active {
  background: #e9ecef;
}

.modal-body {
  padding: 30rpx;
  max-height: 50vh;
  overflow-y: auto;
}

/* 申请摘要 */
.request-summary {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.summary-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.summary-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.summary-item:last-child {
  margin-bottom: 0;
}

/* 状态选择器 */
.status-selector {
  margin-top: 20rpx;
}

.selector-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.status-options {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.status-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  transition: all 0.2s;
}

.status-option:active {
  transform: scale(0.98);
}

.status-option.selected {
  border-color: #FF6F61;
  background: #fff3f1;
}

.option-text {
  font-size: 26rpx;
  color: #333;
}

.status-option.selected .option-text {
  color: #FF6F61;
  font-weight: bold;
}

.option-check {
  font-size: 24rpx;
  color: #FF6F61;
  font-weight: bold;
}

/* 弹窗操作按钮 */
.modal-actions {
  display: flex;
  padding: 20rpx 30rpx 30rpx;
  gap: 20rpx;
}

.modal-actions .action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}

.cancel-btn:active {
  background: #e9ecef;
}

.confirm-btn {
  background: #FF6F61;
  color: white;
}

.confirm-btn:active {
  background: #e55a4e;
}

.confirm-btn.loading,
.confirm-btn.disabled {
  background: #cccccc;
  color: #999;
  pointer-events: none;
}

.update-loading {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.loading-spinner-small {
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #FF6F61;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
  .modal-content {
    width: 90%;
    margin: 0 5%;
  }
  
  .card-actions {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .action-btn {
    height: 60rpx;
    font-size: 24rpx;
  }
  
  .animal-info {
    flex-direction: column;
    text-align: center;
  }
  
  .animal-photo {
    width: 100rpx;
    height: 100rpx;
  }
}