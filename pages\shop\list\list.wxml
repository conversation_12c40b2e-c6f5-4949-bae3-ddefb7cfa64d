<!--pages/shop/list/list.wxml-->
<view class="pet-list-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">宠物列表</text>
    <view class="add-btn" bindtap="onAddPetTap">
      <text class="add-icon">+</text>
      <text class="add-text">添加宠物</text>
    </view>
  </view>

  <!-- 宠物列表 -->
  <view class="pet-list" wx:if="{{!isEmpty}}">
    <view 
      class="pet-card" 
      wx:for="{{petList}}" 
      wx:key="id"
      data-id="{{item.id}}"
      bindtap="onPetCardTap"
    >
      <!-- 宠物图片 -->
      <view class="pet-image-container">
        <image 
          class="pet-image" 
          src="{{item.photo}}" 
          mode="aspectFill"
          lazy-load="{{true}}"
        />
        <view class="stock-badge" wx:if="{{item.stock <= 5}}">
          <text class="stock-text">仅剩{{item.stock}}只</text>
        </view>
      </view>

      <!-- 宠物信息 -->
      <view class="pet-info">
        <view class="pet-breed">{{item.breed}}</view>
        <view class="pet-details">
          <view class="detail-item">
            <text class="detail-label">年龄:</text>
            <text class="detail-value">{{item.age}}个月</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">性别:</text>
            <text class="detail-value gender-{{item.gender}}">{{item.gender}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">库存:</text>
            <text class="detail-value stock-{{item.stock > 0 ? 'available' : 'empty'}}">
              {{item.stock}}只
            </text>
          </view>
        </view>
        <view class="pet-price">
          <text class="price-symbol">¥</text>
          <text class="price-value">{{item.price}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{!isEmpty && petList.length > 0}}">
    <view class="loading" wx:if="{{loading}}">
      <view class="loading-icon"></view>
      <text class="loading-text">加载中...</text>
    </view>
    <view class="no-more" wx:elif="{{!hasMore}}">
      <text class="no-more-text">没有更多数据了</text>
    </view>
    <view class="load-more-btn" wx:else bindtap="loadMoreData">
      <text class="load-more-text">点击加载更多</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{isEmpty && !loading}}">
    <view class="empty-icon">🐾</view>
    <text class="empty-title">暂无宠物信息</text>
    <text class="empty-desc">还没有添加任何宠物，快去添加吧！</text>
    <view class="empty-action">
      <button class="retry-btn" bindtap="onAddPetTap">添加宠物</button>
    </view>
  </view>

  <!-- 首次加载状态 -->
  <view class="first-loading" wx:if="{{loading && petList.length === 0}}">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 网络错误状态 -->
  <view class="error-state" wx:if="{{isEmpty && !loading}}">
    <view class="error-icon">⚠️</view>
    <text class="error-title">加载失败</text>
    <text class="error-desc">网络连接异常，请检查网络后重试</text>
    <view class="error-action">
      <button class="retry-btn" bindtap="onRetryTap">重新加载</button>
    </view>
  </view>
</view>