// pages/hospital/appointment/appointment.js
import hospitalService from '../../../services/hospitalService';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    hospitalId: null,
    hospitalInfo: null,
    loading: true,
    submitting: false,
    // 表单数据
    formData: {
      ownerName: '',
      ownerPhone: '',
      petName: '',
      petType: '',
      petAge: '',
      petGender: '公',
      symptoms: '',
      appointmentTime: '',
      remark: ''
    },
    // 错误信息
    errors: {
      ownerName: '',
      ownerPhone: '',
      petName: '',
      petType: '',
      appointmentTime: ''
    },
    // 日期选择
    minDate: '',
    maxDate: '',
    showDatePicker: false,
    selectedDate: '',
    // 时间选择
    showTimePicker: false,
    timeOptions: [
      '9:00', '9:30', '10:00', '10:30', '11:00', '11:30', 
      '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', 
      '17:00', '17:30'
    ],
    selectedTime: '',
    // 预约成功后的状态
    appointmentSuccess: false,
    appointmentNumber: '',
    // 宠物类型选项
    petTypes: ['猫', '狗', '兔子', '鸟类', '爬行类', '其他'],
    showPetTypePicker: false,
    // 性别选项
    genderOptions: ['公', '母'],
    showGenderPicker: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options;
    
    if (!id) {
      this.showError('缺少必要参数');
      return;
    }
    
    this.setData({ 
      hospitalId: id,
      // 日期范围（今天到30天后）
      minDate: this.formatDate(new Date()),
      maxDate: this.formatDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000))
    });
    
    // 获取用户信息填充表单
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({
        'formData.ownerName': userInfo.username || '',
        'formData.ownerPhone': userInfo.phone || ''
      });
    }
    
    // 获取医院信息
    this.getHospitalInfo();
  },
  
  /**
   * 获取医院信息
   */
  getHospitalInfo() {
    const { hospitalId } = this.data;
    
    hospitalService.getHospitalList({ id: hospitalId })
      .then(res => {
        if (res && res.data && Array.isArray(res.data)) {
          const hospital = res.data.find(item => item.id == hospitalId);
          if (hospital) {
            this.setData({ 
              hospitalInfo: hospital,
              loading: false
            });
          } else {
            this.showError('未找到医院信息');
          }
        } else {
          // 使用模拟数据
          const mockHospital = {
            id: hospitalId,
            name: '爱宠动物医院',
            type: '常规诊疗',
            rating: 4.8,
            image: '/assets/images/default-pet.png',
            address: '北京市朝阳区三里屯街道',
            description: '专业宠物医疗服务，提供全方位的宠物健康保障',
            businessHours: '8:00-20:00',
            contact: '010-12345678'
          };
          
          this.setData({
            hospitalInfo: mockHospital,
            loading: false
          });
        }
      })
      .catch(err => {
        console.error('获取医院信息失败', err);
        this.showError('获取医院信息失败');
      });
  },
  
  /**
   * 显示错误并返回
   */
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
    
    setTimeout(() => {
      wx.navigateBack();
    }, 2000);
  },
  
  /**
   * 处理输入变化
   */
  handleInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`formData.${field}`]: value,
      [`errors.${field}`]: ''  // 清除对应的错误信息
    });
  },
  
  /**
   * 选择宠物类型
   */
  showPetTypePicker() {
    this.setData({ showPetTypePicker: true });
  },
  
  /**
   * 关闭宠物类型选择
   */
  closePetTypePicker() {
    this.setData({ showPetTypePicker: false });
  },
  
  /**
   * 选择宠物类型
   */
  selectPetType(e) {
    const { type } = e.currentTarget.dataset;
    this.setData({
      'formData.petType': type,
      showPetTypePicker: false,
      'errors.petType': ''
    });
  },
  
  /**
   * 选择性别
   */
  showGenderPicker() {
    this.setData({ showGenderPicker: true });
  },
  
  /**
   * 关闭性别选择
   */
  closeGenderPicker() {
    this.setData({ showGenderPicker: false });
  },
  
  /**
   * 选择性别
   */
  selectGender(e) {
    const { gender } = e.currentTarget.dataset;
    this.setData({
      'formData.petGender': gender,
      showGenderPicker: false
    });
  },
  
  /**
   * 选择日期
   */
  showDatePickerPopup() {
    this.setData({ showDatePicker: true });
  },
  
  /**
   * 关闭日期选择
   */
  closeDatePicker() {
    this.setData({ showDatePicker: false });
  },
  
  /**
   * 确认日期选择
   */
  confirmDatePicker(e) {
    const date = e.detail.value;
    this.setData({
      selectedDate: date,
      showDatePicker: false,
      'errors.appointmentTime': ''
    });
    
    // 如果已经选择了时间，则更新预约时间
    if (this.data.selectedTime) {
      this.updateAppointmentTime();
    } else {
      // 选择完日期后自动弹出时间选择
      this.showTimePickerPopup();
    }
  },
  
  /**
   * 选择时间
   */
  showTimePickerPopup() {
    this.setData({ showTimePicker: true });
  },
  
  /**
   * 关闭时间选择
   */
  closeTimePicker() {
    this.setData({ showTimePicker: false });
  },
  
  /**
   * 选择时间
   */
  selectTime(e) {
    const { time } = e.currentTarget.dataset;
    this.setData({
      selectedTime: time,
      showTimePicker: false,
      'errors.appointmentTime': ''
    });
    
    // 更新预约时间
    this.updateAppointmentTime();
  },
  
  /**
   * 更新预约时间
   */
  updateAppointmentTime() {
    const { selectedDate, selectedTime } = this.data;
    if (selectedDate && selectedTime) {
      this.setData({
        'formData.appointmentTime': `${selectedDate} ${selectedTime}`
      });
    }
  },
  
  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },
  
  /**
   * 验证表单
   */
  validateForm() {
    const { formData } = this.data;
    let isValid = true;
    const errors = {};
    
    // 验证主人姓名
    if (!formData.ownerName.trim()) {
      errors.ownerName = '请输入主人姓名';
      isValid = false;
    }
    
    // 验证联系电话
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!formData.ownerPhone.trim()) {
      errors.ownerPhone = '请输入联系电话';
      isValid = false;
    } else if (!phoneRegex.test(formData.ownerPhone.trim())) {
      errors.ownerPhone = '请输入正确的手机号码';
      isValid = false;
    }
    
    // 验证宠物名称
    if (!formData.petName.trim()) {
      errors.petName = '请输入宠物名称';
      isValid = false;
    }
    
    // 验证宠物类型
    if (!formData.petType) {
      errors.petType = '请选择宠物类型';
      isValid = false;
    }
    
    // 验证预约时间
    if (!formData.appointmentTime) {
      errors.appointmentTime = '请选择预约时间';
      isValid = false;
    }
    
    // 设置错误信息
    this.setData({ errors });
    
    return isValid;
  },
  
  /**
   * 提交表单
   */
  submitForm() {
    // 表单验证
    if (!this.validateForm()) {
      wx.showToast({
        title: '请完善表单信息',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 设置提交状态
    this.setData({ submitting: true });
    
    // 构建请求数据
    const { formData, hospitalId, hospitalInfo } = this.data;
    const requestData = {
      hospitalId,
      hospitalName: hospitalInfo.name,
      ownerName: formData.ownerName,
      ownerPhone: formData.ownerPhone,
      petName: formData.petName,
      petType: formData.petType,
      petAge: formData.petAge,
      petGender: formData.petGender,
      symptoms: formData.symptoms,
      appointmentTime: formData.appointmentTime,
      remark: formData.remark
    };
    
    // 调用预约接口
    hospitalService.reserveMedical(requestData)
      .then(res => {
        console.log('预约结果', res);
        
        if (res && res.code === 20) {
          // 预约成功
          this.setData({
            appointmentSuccess: true,
            appointmentNumber: res.appointmentNumber || '未提供预约号',
            submitting: false
          });
          
          // 显示成功提示
          wx.showToast({
            title: '预约成功',
            icon: 'success',
            duration: 2000
          });
        } else {
          // 预约失败
          wx.showToast({
            title: res?.message || '预约失败，请稍后重试',
            icon: 'none',
            duration: 2000
          });
          this.setData({ submitting: false });
        }
      })
      .catch(err => {
        console.error('预约失败', err);
        wx.showToast({
          title: '预约失败，请稍后重试',
          icon: 'none',
          duration: 2000
        });
        this.setData({ submitting: false });
      });
  },
  
  /**
   * 查看我的预约
   */
  viewMyAppointments() {
    wx.navigateTo({
      url: '/pages/mine/appointments/appointments'
    });
  },
  
  /**
   * 返回首页
   */
  goHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { hospitalInfo } = this.data;
    return {
      title: hospitalInfo ? `${hospitalInfo.name} - 在线预约` : '宠物医院预约',
      path: `/pages/hospital/detail/detail?id=${this.data.hospitalId}`
    };
  }
})