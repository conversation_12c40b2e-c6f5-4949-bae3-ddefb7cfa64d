// pages/hospital/detail/detail.js 
import hospitalService from '../../../services/hospitalService';
import evaluationService from '../../../services/evaluationService';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    hospitalId: null,
    evaluations: [],
    loading: true,
    currentTab: 'evaluations',  // 默认显示评价页面
    pageSize: 10,
    evalCurrentPage: 1,
    evalHasMore: true,
    evalTotal: 0,  // 评价总数
    // 评价弹窗相关
    showEvaluationModal: false,
    evaluationForm: {
      rating: 0,
      content: ''
    },
    submittingEvaluation: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('🏥 医院详情页接收参数:', options);
    
    const { id } = options;
    
    // 简化ID验证逻辑
    if (!id) {
      console.error('❌ 医院ID参数不存在');
      this.handleError('医院ID不存在');
      return;
    }

    // 转换和验证ID
    let hospitalId = parseInt(id);
    if (isNaN(hospitalId) || hospitalId <= 0) {
      console.error('❌ 医院ID无效:', id);
      this.handleError('医院ID无效');
      return;
    }

    console.log('✅ 医院ID验证通过:', hospitalId);
    this.setData({ 
      hospitalId,
      loading: false
    });
    
    // 获取医院评价
    this.getHospitalEvaluations();
  },

  /**
   * 获取医院评价 - 使用前端Mock数据
   * 临时方案：由于后端接口不完善，前端提供Mock数据
   * 后续后端接口Ready后，只需修改service层即可
   */
  getHospitalEvaluations(loadMore = false) {
    console.log('🏥 开始获取医院评价，loadMore:', loadMore);
    
    const { hospitalId, evalCurrentPage, pageSize, evaluations } = this.data;
    
    if (!loadMore) {
      this.setData({ loading: true });
    }
    
    // 🎯 使用前端Mock数据（临时方案）
    console.log('🎯 使用前端Mock数据，医院ID:', hospitalId);
    
    // 模拟网络请求延迟
    setTimeout(() => {
      const mockEvaluations = this.getMockHospitalEvaluations(hospitalId, evalCurrentPage, pageSize);
      
      // 模拟分页逻辑
      const totalMockData = this.getAllMockEvaluations(hospitalId);
      const startIndex = (evalCurrentPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const currentPageData = totalMockData.slice(startIndex, endIndex);
      
      const processedEvaluations = currentPageData.map(evaluation => ({
        id: evaluation.id,
        userName: evaluation.userName,
        userAvatar: evaluation.userAvatar,
        content: evaluation.content,
        rating: evaluation.rating,
        createTime: evaluation.createTime
      }));
      
      console.log('📥 Mock数据加载完成:', {
        page: evalCurrentPage,
        pageSize: pageSize,
        count: processedEvaluations.length,
        total: totalMockData.length
      });
      
      this.setData({
        evaluations: loadMore ? [...evaluations, ...processedEvaluations] : processedEvaluations,
        evalHasMore: endIndex < totalMockData.length, // 是否还有更多数据
        evalCurrentPage: loadMore ? evalCurrentPage + 1 : evalCurrentPage,
        evalTotal: totalMockData.length,
        loading: false
      });
      
      console.log('✅ 医院评价Mock数据更新完成');
    }, 800); // 模拟800ms网络延迟
  },

  /**
   * 格式化创建时间 - 保持不变
   */
  formatCreateTime(createTime) {
    if (!createTime) return '未知时间';
    
    try {
      const date = new Date(createTime);
      if (isNaN(date.getTime())) {
        return createTime;
      }
      
      const now = new Date();
      const diff = now.getTime() - date.getTime();
      
      const minute = 60 * 1000;
      const hour = 60 * minute;
      const day = 24 * hour;
      
      if (diff < minute) {
        return '刚刚';
      } else if (diff < hour) {
        return `${Math.floor(diff / minute)}分钟前`;
      } else if (diff < day) {
        return `${Math.floor(diff / hour)}小时前`;
      } else if (diff < 7 * day) {
        return `${Math.floor(diff / day)}天前`;
      } else {
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      }
    } catch (error) {
      console.warn('时间格式化失败:', error);
      return createTime;
    }
  },

  /**
   * 切换标签
   */
  switchTab(e) {
    const { tab } = e.currentTarget.dataset;
    console.log('🔄 切换标签:', tab);
    if (tab === 'evaluations') {
      this.setData({ currentTab: tab });
    }
  },

  /**
   * 加载更多评价
   */
  loadMoreEvaluations() {
    if (this.data.evalHasMore && !this.data.loading) {
      console.log('📄 加载更多评价');
      this.getHospitalEvaluations(true);
    }
  },

  /**
   * 评价医院 - 显示评价弹窗
   */
  evaluateHospital() {
    console.log('💬 打开评价弹窗');
    
    // 检查是否登录
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000
      });
      
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }, 1500);
      
      return;
    }
    
    // 显示评价弹窗
    this.setData({
      showEvaluationModal: true,
      evaluationForm: {
        rating: 0,
        content: ''
      }
    });
  },

  /**
   * 关闭评价弹窗
   */
  closeEvaluationModal() {
    console.log('❌ 关闭评价弹窗');
    this.setData({
      showEvaluationModal: false
    });
  },

  /**
   * 评分变化
   */
  onRatingChange(e) {
    const { rating } = e.currentTarget.dataset;
    console.log('⭐ 评分变化:', rating);
    this.setData({
      'evaluationForm.rating': rating
    });
  },

  /**
   * 评价内容输入
   */
  onContentInput(e) {
    const { value } = e.detail;
    this.setData({
      'evaluationForm.content': value
    });
  },

  /**
   * 提交评价 - 使用前端Mock响应
   */
  submitEvaluation() {
    console.log('📝 开始提交评价');
    
    const { hospitalId, evaluationForm } = this.data;
    const { rating, content } = evaluationForm;

    // 验证评分
    if (!rating || rating < 1 || rating > 5) {
      wx.showToast({
        title: '请选择1-5星评分',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 验证评价内容
    if (!content || !content.trim()) {
      wx.showToast({
        title: '请输入评价内容',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    if (content.length > 500) {
      wx.showToast({
        title: '评价内容不能超过500字',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 设置提交状态
    this.setData({ submittingEvaluation: true });

    console.log('📤 提交评价（使用Mock响应）:', { hospitalId, rating, content });

    // 🎯 使用前端Mock响应（临时方案）
    setTimeout(() => {
      console.log('📥 Mock评价提交成功');
      
      wx.showToast({
        title: '评价成功',
        icon: 'success',
        duration: 2000
      });
      
      // 关闭弹窗
      this.setData({
        showEvaluationModal: false,
        submittingEvaluation: false
      });
      
      // 重新加载评价列表
      this.setData({
        evalCurrentPage: 1,
        evaluations: [],
        evalHasMore: true
      });
      this.getHospitalEvaluations();
      
      console.log('✅ 评价提交成功，已刷新评价列表');
    }, 1000); // 模拟1秒提交时间
  },

  /**
   * 🎯 获取所有Mock评价数据（根据医院ID）
   */
  getAllMockEvaluations(hospitalId) {
    // 根据医院ID生成不同的评价数据
    const baseEvaluations = [
      {
        id: 1,
        userName: '爱宠人士小李',
        userAvatar: '/assets/images/default-pet.png',
        content: '医生很专业，设备先进，我家狗狗在这里治疗效果很好！医生耐心细致，会详细解释病情和治疗方案。环境也很干净，停车方便。',
        rating: 5,
        createTime: '2024-03-15 14:30'
      },
      {
        id: 2,
        userName: '铲屎官小王',
        userAvatar: '/assets/images/default-pet.png',
        content: '服务态度很好，医生耐心负责，护士也很温柔。我家猫咪平时很怕生，但在这里很放松。价格合理，性价比高。',
        rating: 4,
        createTime: '2024-03-12 10:15'
      },
      {
        id: 3,
        userName: '宠物达人',
        userAvatar: '/assets/images/default-pet.png',
        content: '24小时急诊真的很方便，半夜宠物生病也不用担心了。医生经验丰富，一眼就看出了问题所在。强烈推荐！',
        rating: 5,
        createTime: '2024-03-10 16:45'
      },
      {
        id: 4,
        userName: '新手铲屎官',
        userAvatar: '/assets/images/default-pet.png',
        content: '第一次带宠物看病，医生很耐心地解释，学到了很多护理知识。前台服务也很好，会主动提醒复查时间。',
        rating: 4,
        createTime: '2024-03-08 11:20'
      },
      {
        id: 5,
        userName: '多宠家庭',
        userAvatar: '/assets/images/default-pet.png',
        content: '我家有三只宠物，经常来这里。医生都认识我们了，每次都很细心。疫苗、体检、美容一条龙服务很棒。',
        rating: 5,
        createTime: '2024-03-05 09:30'
      },
      {
        id: 6,
        userName: '猫奴一枚',
        userAvatar: '/assets/images/default-pet.png',
        content: '医院环境很好，没有异味。医生技术过硬，我家猫咪的皮肤病在这里治好了。收费透明，没有乱收费。',
        rating: 4,
        createTime: '2024-03-02 15:20'
      },
      {
        id: 7,
        userName: '狗狗爱好者',
        userAvatar: '/assets/images/default-pet.png',
        content: '设备很先进，有专业的X光机和B超。医生诊断准确，治疗方案合理。停车位充足，带大型犬来很方便。',
        rating: 5,
        createTime: '2024-03-01 12:45'
      },
      {
        id: 8,
        userName: '资深宠主',
        userAvatar: '/assets/images/default-pet.png',
        content: '养宠物十多年了，这是我见过最专业的宠物医院之一。医生不仅技术好，而且很有爱心，真心为宠物着想。',
        rating: 5,
        createTime: '2024-02-28 18:10'
      },
      {
        id: 9,
        userName: '上班族宠主',
        userAvatar: '/assets/images/default-pet.png',
        content: '营业时间长，适合上班族。晚上下班后还能带宠物来看病。医生服务态度好，不会因为是晚上就态度差。',
        rating: 4,
        createTime: '2024-02-25 20:30'
      },
      {
        id: 10,
        userName: '学生党',
        userAvatar: '/assets/images/default-pet.png',
        content: '价格比较亲民，适合学生。医生会根据实际情况推荐治疗方案，不会过度医疗。护士姐姐也很温柔。',
        rating: 4,
        createTime: '2024-02-22 14:15'
      }
    ];

    // 根据医院ID稍微调整数据，模拟不同医院的评价
    return baseEvaluations.map(evaluation => ({
      ...evaluation,
      id: evaluation.id + hospitalId * 100, // 确保不同医院的评价ID不同
      createTime: this.formatCreateTime(evaluation.createTime)
    }));
  },

  /**
   * 🎯 获取Mock医院评价（分页）
   */
  getMockHospitalEvaluations(hospitalId, page, pageSize) {
    const allEvaluations = this.getAllMockEvaluations(hospitalId);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return allEvaluations.slice(startIndex, endIndex);
  },

  /**
   * 处理错误 - 简化错误处理
   */
  handleError(message) {
    console.error('❌ 页面错误:', message);
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
    
    this.setData({ loading: false });
    
    setTimeout(() => {
      wx.navigateBack();
    }, 2000);
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('🔄 下拉刷新');
    
    this.setData({
      evalCurrentPage: 1,
      evaluations: [],
      evalHasMore: true
    });
    
    this.getHospitalEvaluations().finally(() => {
      wx.stopPullDownRefresh();
      console.log('✅ 下拉刷新完成');
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('📄 触底加载更多评价');
    this.loadMoreEvaluations();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '宠物医院评价',
      path: `/pages/hospital/detail/detail?id=${this.data.hospitalId}`,
      imageUrl: '/assets/images/default-hospital.png'
    };
  }
});