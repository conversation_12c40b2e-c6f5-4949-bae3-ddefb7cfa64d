// pages/mine/order/order.js
// 尝试不同的导入方式，确保路径正确
import shopService from '../../../services/shopService.js';
import userService from '../../../services/userService.js';
import { CONFIG } from '../../../services/config.js';

Page({
  data: {
    orders: [],
    loading: true,
    page: 1,
    pageSize: 10,
    total: 0,
    hasMore: true,
    isRefreshing: false
  },

  onLoad() {
    console.log('订单页面加载中...');
    this.loadOrders(true); // true表示重新加载
  },

  onShow() {
    console.log('订单页面显示');
    // 只有在页面显示时才刷新，避免重复加载
    if (this.data.orders.length === 0) {
      this.loadOrders(true);
    }
  },

  /**
   * 处理图片路径
   * @param {string} imagePath - 原始图片路径
   * @returns {string} 处理后的图片路径
   */
  processImagePath(imagePath) {
    if (!imagePath) {
      return '/assets/images/default-pet.png';
    }
    
    // 如果是完整的URL（以http开头），直接使用
    if (imagePath.startsWith('http')) {
      return imagePath;
    }
    
    // 如果是相对路径，拼接 D:/images/
    return `D:/images/${imagePath}`;
  },

  /**
   * 加载订单数据
   * @param {boolean} reset - 是否重置数据（用于下拉刷新）
   */
  loadOrders(reset = false) {
    console.log('开始加载订单数据, reset:', reset);
    
    // 检查是否已登录
    if (!userService || !userService.isLoggedIn()) {
      console.log('用户未登录');
      wx.showToast({ title: '请先登录', icon: 'none' });
      setTimeout(() => {
        wx.navigateTo({ url: '/pages/login/login' });
      }, 1500);
      this.setData({ loading: false });
      return;
    }

    // 如果是重置，重新设置分页参数
    if (reset) {
      this.setData({ 
        page: 1, 
        loading: true, 
        isRefreshing: true,
        hasMore: true
      });
    } else {
      this.setData({ loading: true });
    }
    
    const currentPage = reset ? 1 : this.data.page;
    
    // 调用服务获取订单数据 - 使用整数参数
    shopService.getPetOrderStatus({ 
      page: parseInt(currentPage), 
      pageSize: parseInt(this.data.pageSize) 
    })
      .then(res => {
        console.log('获取订单成功:', res);
        
        // 使用200作为成功状态码，符合API文档要求
        if (res.code === 200) {
          // 处理订单数据 - 使用API返回的数据结构
          const orders = Array.isArray(res.data) ? res.data : [];
          const total = res.total || 0;
          
          console.log('处理订单数据:', { orders: orders.length, total });
          
          // 处理订单数据映射 - 根据API文档调整字段映射
          const processedOrders = orders.map(item => {
            const statusInfo = this.formatOrderStatus(item.reservationStatus);
            
            return {
              // 基础信息
              id: item.id,
              orderId: item.orderId || item.id, // API返回orderId字段
              
              // 用户信息
              username: item.username || '未知用户',
              avatar: this.processImagePath(item.avatar),
              
              // 宠物信息 - API返回breed和photo
              petName: item.breed || '未知宠物',
              petType: item.breed || '未知类型',
              petImage: this.processImagePath(item.photo),
              
              // 订单信息 - API返回paymentTime
              orderTime: item.paymentTime || new Date().toLocaleString(),
              paymentTime: item.paymentTime,
              
              // 状态信息
              status: statusInfo.text,
              statusClass: statusInfo.class,
              reservationStatus: item.reservationStatus,
              
              // 显示用的格式化数据
              displayTime: this.formatTime(item.paymentTime),
              
              // 保留原始数据用于调试
              _original: item
            };
          });
          
          // 根据是否重置来决定如何更新数据
          let finalOrders;
          if (reset) {
            finalOrders = processedOrders;
          } else {
            // 追加数据（用于加载更多）
            finalOrders = [...this.data.orders, ...processedOrders];
          }
          
          // 检查是否还有更多数据
          const hasMore = finalOrders.length < total;
          
          this.setData({
            orders: finalOrders,
            total: total,
            hasMore: hasMore,
            page: currentPage,
            loading: false,
            isRefreshing: false
          });
          
          console.log('订单数据更新完成:', { 
            ordersCount: finalOrders.length, 
            total, 
            hasMore,
            currentPage 
          });
          
        } else {
          console.error('获取订单失败:', res.message);
          wx.showToast({ title: res.message || '获取订单失败', icon: 'none' });
          this.setData({ 
            loading: false, 
            isRefreshing: false 
          });
        }
      })
      .catch(err => {
        console.error('获取订单失败:', err);
        
        // 如果是登录错误，不显示Mock数据
        if (err.message && err.message.includes('登录')) {
          wx.showToast({ title: '请先登录', icon: 'none' });
          setTimeout(() => {
            wx.navigateTo({ url: '/pages/login/login' });
          }, 1500);
          this.setData({ 
            loading: false, 
            isRefreshing: false 
          });
          return;
        }
        
        // 如果获取失败且是重置模式，提供模拟数据作为降级方案
        if (reset) {
          this.loadMockOrders();
          wx.showToast({ title: '网络错误，显示本地数据', icon: 'none' });
        } else {
          wx.showToast({ title: '加载失败，请重试', icon: 'none' });
          this.setData({ 
            loading: false, 
            isRefreshing: false 
          });
        }
      });
  },

  /**
   * 格式化时间显示
   * @param {string} timeStr - 时间字符串
   * @returns {string} 格式化后的时间
   */
  formatTime(timeStr) {
    if (!timeStr) return '未知时间';
    
    try {
      // 如果已经是格式化的字符串，直接返回
      if (timeStr.includes('-') && timeStr.includes(':')) {
        return timeStr;
      }
      
      // 尝试解析时间戳
      const date = new Date(timeStr);
      if (isNaN(date.getTime())) {
        return timeStr; // 无法解析，返回原始字符串
      }
      
      return date.toLocaleString('zh-CN');
    } catch (error) {
      console.error('时间格式化错误:', error);
      return timeStr;
    }
  },

  /**
   * 格式化订单状态显示
   * @param {string} status - 原始状态
   * @returns {Object} 格式化后的状态信息
   */
  formatOrderStatus(status) {
    const statusMap = {
      '待发货': { text: '待发货', class: 'pending' },
      '处理中': { text: '处理中', class: 'processing' },
      '已支付': { text: '已支付', class: 'paid' },
      '已发货': { text: '已发货', class: 'shipped' },
      '已完成': { text: '已完成', class: 'completed' },
      '已取消': { text: '已取消', class: 'cancelled' },
      // 兼容旧状态
      '待支付': { text: '待支付', class: 'pending' },
      'pending': { text: '待处理', class: 'pending' },
      'confirmed': { text: '已确认', class: 'paid' },
      'paid': { text: '已支付', class: 'paid' },
      'completed': { text: '已完成', class: 'completed' },
      'cancelled': { text: '已取消', class: 'cancelled' }
    };
    
    return statusMap[status] || { text: status || '未知状态', class: 'pending' };
  },

  /**
   * 加载模拟订单数据（降级方案）
   */
  loadMockOrders() {
    const mockOrders = [
      {
        id: 1,
        orderId: 'ORDER001',
        username: '测试用户',
        avatar: '/assets/images/default-avatar.png',
        petName: '金毛',
        petType: '金毛',
        petImage: '/assets/images/default-pet.png',
        orderTime: '2024-03-15 14:30:00',
        paymentTime: '2024-03-15 14:30:00',
        displayTime: '2024-03-15 14:30:00',
        status: '已完成',
        statusClass: 'completed',
        reservationStatus: '已完成'
      },
      {
        id: 2,
        orderId: 'ORDER002',
        username: '测试用户2',
        avatar: '/assets/images/default-avatar.png',
        petName: '布偶猫',
        petType: '布偶猫',
        petImage: '/assets/images/default-pet.png',
        orderTime: '2024-03-14 10:20:00',
        paymentTime: '2024-03-14 10:20:00',
        displayTime: '2024-03-14 10:20:00',
        status: '已支付',
        statusClass: 'paid',
        reservationStatus: '已支付'
      }
    ];
    
    this.setData({
      orders: mockOrders,
      total: mockOrders.length,
      hasMore: false,
      loading: false,
      isRefreshing: false
    });
  },

  /**
   * 取消/删除订单
   */
  cancelOrder(e) {
    const orderId = e.currentTarget.dataset.id;
    
    // 验证ID为正整数（根据API文档要求）
    if (!orderId || !Number.isInteger(Number(orderId)) || Number(orderId) <= 0) {
      wx.showToast({
        title: '无效的订单ID',
        icon: 'none'
      });
      return;
    }

    // 确保用户已登录
    if (!userService.isLoggedIn()) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }, 1500);
      return;
    }
    
    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除此预订订单吗？',
      success: (res) => {
        if (res.confirm) {
          this.performDeleteOrder(orderId);
        }
      },
    });
  },

  /**
   * 执行删除订单操作
   */
  performDeleteOrder(orderId) {
    console.log('删除订单:', orderId);
    
    // 显示加载中
    wx.showLoading({ 
      title: '删除中...',
      mask: true
    });
    
    shopService.deletePetOrder(orderId)
      .then(res => {
        wx.hideLoading();
        
        // 根据API文档，成功状态码是200
        if (res && res.code === 200) {
          wx.showToast({ 
            title: res.message || '删除成功',
            icon: 'success' 
          });
          
          // 重新加载订单列表
          this.loadOrders(true);
        } else {
          // 处理API返回的错误信息
          wx.showToast({ 
            title: res.message || '删除失败', 
            icon: 'none' 
          });
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('删除订单失败:', err);
        
        // 根据API文档可能的错误状态码
        let errorMessage = '删除失败，请重试';
        if (err.statusCode === 403) {
          errorMessage = '无权限删除此订单';
        } else if (err.statusCode === 404) {
          errorMessage = '订单不存在';
        } else if (err.code === 403) {
          errorMessage = err.message || '无权限删除此订单';
        } else if (err.code === 404) {
          errorMessage = err.message || '订单不存在';
        }
        
        wx.showToast({ 
          title: errorMessage, 
          icon: 'none' 
        });
      });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log('下拉刷新订单');
    this.loadOrders(true);
    // 延迟停止下拉刷新动画
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    console.log('触发加载更多, hasMore:', this.data.hasMore);
    
    if (!this.data.hasMore || this.data.loading) {
      console.log('无更多数据或正在加载中');
      return;
    }
    
    // 增加页码并加载
    this.setData({
      page: this.data.page + 1
    });
    
    this.loadOrders(false); // false表示追加数据
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail(e) {
    const orderId = e.currentTarget.dataset.id;
    const orderData = e.currentTarget.dataset.order;
    
    console.log('查看订单详情:', { orderId, orderData });
    
    // 跳转到订单详情页面（如果有的话）
    wx.navigateTo({
      url: `/pages/order/detail/detail?id=${orderId}`
    }).catch(() => {
      // 如果没有详情页面，显示详细信息
      const order = this.data.orders.find(o => o.id === orderId);
      if (order) {
        const detail = `订单详情：
订单号：${order.orderId}
用户：${order.username}
宠物：${order.petName} (${order.petType})
支付时间：${order.displayTime}
状态：${order.status}`;
        
        wx.showModal({
          title: '订单详情',
          content: detail,
          showCancel: false,
          confirmText: '我知道了'
        });
      }
    });
  },

  /**
   * 重新加载数据（错误重试）
   */
  retryLoad() {
    console.log('重试加载订单数据');
    this.loadOrders(true);
  }
});