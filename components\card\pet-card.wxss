/* components/card/pet-card.wxss */

/* 通用卡片样式 */
.pet-card {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 紧凑模式 */
.pet-card-compact {
  width: 100%;
  height: 240rpx;
  display: flex;
  flex-direction: column;
}

.pet-image-compact {
  width: 100%;
  height: 180rpx;
}

.pet-info-compact {
  padding: 8rpx 12rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.pet-name-compact {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}

.pet-breed-compact {
  font-size: 22rpx;
  color: #666;
  margin-top: 4rpx;
}

/* 普通模式 */
.pet-card-normal {
  display: flex;
  padding: 20rpx;
}

.pet-image-normal {
  width: 180rpx;
  height: 180rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.pet-info-normal {
  margin-left: 20rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.pet-name-normal {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.pet-breed-normal {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.pet-meta-normal {
  display: flex;
  margin-bottom: 24rpx;
}

.pet-age-normal {
  margin-right: 20rpx;
}

.pet-actions-normal {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.pet-favorite-normal {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.is-favorite {
  background-color: rgba(245, 108, 108, 0.1);
}

.favorite-icon {
  width: 32rpx;
  height: 32rpx;
}

.pet-adopt-normal {
  height: 60rpx;
  padding: 0 30rpx;
  background-color: #4eaaa8;
  color: #fff;
  border-radius: 30rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 详细模式 */
.pet-card-detailed {
  display: flex;
  flex-direction: column;
}

.pet-image-detailed {
  width: 100%;
  height: 360rpx;
}

.pet-info-detailed {
  padding: 24rpx;
}

.pet-header-detailed {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.pet-name-detailed {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.pet-favorite-detailed {
  width: 64rpx;
  height: 64rpx;
  border-radius: 32rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pet-meta-detailed {
  margin-bottom: 16rpx;
}

.pet-meta-item {
  display: flex;
  margin-bottom: 8rpx;
}

.meta-label {
  font-size: 26rpx;
  color: #666;
  width: 150rpx;
}