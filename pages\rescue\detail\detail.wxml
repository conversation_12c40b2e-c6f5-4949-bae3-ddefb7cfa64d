<!--pages/rescue/detail/detail.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-icon"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 救助站详情 -->
  <view wx:else class="detail-content">
    <!-- 救助站头部信息 -->
    <view class="station-header">
      <image 
        class="station-photo" 
        src="{{stationInfo.photo}}" 
        mode="aspectFill"
        lazy-load="{{true}}"
      />
      <view class="header-overlay">
        <text class="station-name">{{stationInfo.name}}</text>
        <text class="station-address">📍 {{stationInfo.address}}</text>
      </view>
      
      <!-- 快捷操作按钮 -->
      <view class="quick-actions">
        <view class="action-btn" bindtap="makePhoneCall">
          <text class="action-icon">📞</text>
          <text class="action-text">联系</text>
        </view>
        <view class="action-btn" bindtap="navigateToStation">
          <text class="action-icon">🧭</text>
          <text class="action-text">导航</text>
        </view>
        <!-- 救助站用户才显示管理按钮 -->
        <view wx:if="{{isRescueStationUser}}" class="action-btn manage-btn" bindtap="manageAdoptions">
          <text class="action-icon">📝</text>
          <text class="action-text">管理</text>
        </view>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="info-section">
      <view class="section-title">基本信息</view>
      <view class="info-item">
        <text class="info-label">联系电话</text>
        <text class="info-value">{{stationInfo.phone}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">营业时间</text>
        <text class="info-value">{{stationInfo.businessHours}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">成立年份</text>
        <text class="info-value">{{stationInfo.establishedYear}}年</text>
      </view>
      <view class="info-item">
        <text class="info-label">收容能力</text>
        <text class="info-value">{{stationInfo.capacity}}只</text>
      </view>
      <view class="info-item">
        <text class="info-label">当前收容</text>
        <text class="info-value">{{stationInfo.currentAnimals}}只</text>
      </view>
      <view wx:if="{{stationInfo.website}}" class="info-item">
        <text class="info-label">官方网站</text>
        <text class="info-value website-link">{{stationInfo.website}}</text>
      </view>
    </view>

    <!-- 救助站介绍 -->
    <view class="info-section">
      <view class="section-title">机构介绍</view>
      <text class="description-text">{{stationInfo.description}}</text>
    </view>

    <!-- 服务项目 -->
    <view wx:if="{{stationInfo.services && stationInfo.services.length > 0}}" class="info-section">
      <view class="section-title">服务项目</view>
      <view class="services-grid">
        <view 
          wx:for="{{stationInfo.services}}" 
          wx:key="index" 
          class="service-item"
        >
          <text class="service-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 评价区域 -->
    <view class="evaluation-section">
      <view class="section-header">
        <view class="section-title">
          用户评价
          <text wx:if="{{evaluationTotal > 0}}" class="evaluation-count">（{{evaluationTotal}}条）</text>
        </view>
        <view class="evaluation-actions">
          <text class="my-evaluation-link" bindtap="viewMyEvaluations">我的评价</text>
          <view class="add-evaluation-btn" bindtap="showEvaluationForm">
            <text class="btn-text">写评价</text>
          </view>
        </view>
      </view>

      <!-- 评价列表 -->
      <view wx:if="{{evaluationsLoading}}" class="evaluations-loading">
        <view class="loading-spinner"></view>
        <text>加载评价中...</text>
      </view>
      
      <view wx:elif="{{evaluations.length === 0}}" class="no-evaluations">
        <image class="empty-icon" src="/assets/images/empty-evaluation.png" mode="aspectFit" />
        <text>暂无评价，快来写第一条评价吧！</text>
      </view>
      
      <view wx:else class="evaluations-list">
        <view 
          wx:for="{{evaluations}}" 
          wx:key="id" 
          class="evaluation-item"
        >
          <view class="evaluation-header">
            <view class="user-info">
              <!-- 显示用户头像，根据API文档处理avatar字段 -->
              <image 
                wx:if="{{item.avatar}}" 
                class="user-avatar" 
                src="{{item.avatar}}" 
                mode="aspectFill"
                lazy-load="{{true}}"
              />
              <view wx:else class="user-avatar-placeholder">
                <text class="avatar-text">{{item.username ? item.username.charAt(0) : (item.userName ? item.userName.charAt(0) : '用')}}</text>
              </view>
              <!-- 根据API文档，使用username字段，兼容userName -->
              <text class="user-name">{{item.username || item.userName || '匿名用户'}}</text>
            </view>
            <view class="rating-display">
              <view class="rating-stars">
                <text wx:for="{{stars}}" wx:for-item="star" wx:key="star" 
                      class="star {{star <= item.rating ? 'star-filled' : 'star-empty'}}">
                  ⭐
                </text>
              </view>
              <text class="rating-number">{{item.rating}}/5</text>
            </view>
          </view>
          
          <view class="evaluation-content">
            <text class="evaluation-text">{{item.content}}</text>
          </view>
          
          <view class="evaluation-footer">
            <text class="evaluation-time">{{item.createTime}}</text>
            <!-- 删除按钮（只有用户自己的评价才显示） -->
            <view wx:if="{{item.canDelete}}" class="evaluation-actions-buttons">
              <view 
                class="delete-evaluation-btn {{deletingEvaluationId === item.id ? 'deleting' : ''}}" 
                data-id="{{item.id}}"
                bindtap="deleteEvaluation"
              >
                <view wx:if="{{deletingEvaluationId === item.id}}" class="delete-loading">
                  <view class="loading-spinner-tiny"></view>
                  <text>删除中</text>
                </view>
                <text wx:else>删除</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 加载更多提示 -->
        <view wx:if="{{evaluationHasMore}}" class="load-more-hint">
          <text>上拉加载更多评价</text>
        </view>
        <view wx:else class="no-more-hint">
          <text>已显示全部评价</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 评价表单弹窗 -->
  <view wx:if="{{showEvaluationForm}}" class="evaluation-modal">
    <view class="modal-overlay" bindtap="hideEvaluationForm"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">评价救助站</text>
        <view class="modal-close" bindtap="hideEvaluationForm">✕</view>
      </view>
      
      <view class="form-content">
        <!-- 救助站信息展示 -->
        <view class="station-info-display">
          <text class="station-name-small">{{stationInfo.name}}</text>
        </view>
        
        <!-- 评分选择 -->
        <view class="form-item">
          <text class="form-label">评分 *</text>
          <view class="rating-selector">
            <view 
              wx:for="{{stars}}" 
              wx:key="item" 
              class="star-btn {{item <= evaluationForm.rating ? 'selected' : ''}}"
              data-rating="{{item}}"
              bindtap="selectRating"
            >
              ⭐
            </view>
            <text class="rating-desc">{{evaluationForm.rating}}星</text>
          </view>
        </view>
        
        <!-- 评价内容 -->
        <view class="form-item">
          <text class="form-label">评价内容 *</text>
          <textarea 
            class="content-input"
            placeholder="请输入您对救助站的评价，分享您的真实体验（最多200字符）"
            value="{{evaluationForm.content}}"
            maxlength="200"
            bindinput="inputEvaluationContent"
            auto-height="{{true}}"
            cursor-spacing="20"
          ></textarea>
          <view class="char-count-container">
            <text class="char-count {{evaluationForm.content.length >= 200 ? 'char-limit' : ''}}">
              {{evaluationForm.content.length}}/200
            </text>
          </view>
        </view>
        
        <!-- 提交说明 -->
        <view class="submit-note">
          <text class="note-text">* 您的评价将帮助其他用户了解救助站，请客观真实地进行评价</text>
        </view>
      </view>
      
      <view class="modal-actions">
        <view class="action-btn cancel-btn" bindtap="hideEvaluationForm">取消</view>
        <view 
          class="action-btn submit-btn {{submittingEvaluation ? 'loading' : ''}} {{evaluationForm.content.trim() === '' ? 'disabled' : ''}}" 
          bindtap="submitEvaluation"
        >
          <view wx:if="{{submittingEvaluation}}" class="submit-loading">
            <view class="loading-spinner-small"></view>
            <text>提交中...</text>
          </view>
          <text wx:else>提交评价</text>
        </view>
      </view>
    </view>
  </view>
</view>