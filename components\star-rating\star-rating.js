Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 评分的值 0-5，支持小数
    value: {
      type: Number,
      value: 0,
      observer: function(newVal) {
        this.updateValue(newVal);
      }
    },
    // 星星大小，单位为rpx
    size: {
      type: Number,
      value: 36
    },
    // 星星颜色
    color: {
      type: String,
      value: '#ffb805'
    },
    // 未选中的星星颜色
    voidColor: {
      type: String,
      value: '#c7c7c7'
    },
    // 是否允许点击进行评分
    disabled: {
      type: Boolean,
      value: false
    },
    // 评分精度，1 或 0.5
    precision: {
      type: Number,
      value: 0.5,
      observer: function(newVal) {
        if (![0.5, 1].includes(newVal)) {
          console.warn('[star-rating] precision must be 0.5 or 1');
          this.setData({
            precision: newVal === 0.5 ? 0.5 : 1
          });
        }
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 转换后的评分数组，用于展示每个星星的填充程度
    valueArray: [0, 0, 0, 0, 0]
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached: function() {
      this.updateValue(this.data.value);
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 更新评分值
     * @param {number} value - 评分值
     */
    updateValue: function(value) {
      if (value < 0) value = 0;
      if (value > 5) value = 5;
      
      const valueArray = this.data.valueArray;
      const precision = this.data.precision;
      
      for (let i = 0; i < 5; i++) {
        if (value >= i + 1) {
          valueArray[i] = 1;
        } else if (value > i) {
          const decimal = value - i;
          valueArray[i] = precision === 0.5 
            ? (decimal >= 0.5 ? 0.5 : 0)
            : decimal;
        } else {
          valueArray[i] = 0;
        }
      }
      
      this.setData({ valueArray });
    },
    
    /**
     * 点击星星进行评分
     * @param {Object} e - 事件对象
     */
    onSelect: function(e) {
      if (this.data.disabled) return;
      
      const index = e.currentTarget.dataset.index;
      const precision = this.data.precision;
      
      // 获取点击位置
      const { clientX } = e.changedTouches[0];
      const { left, width } = e.currentTarget.getBoundingClientRect();
      const percent = (clientX - left) / width;
      
      // 根据点击位置和精度计算评分
      let value = index + 1;
      if (precision === 0.5) {
        value = percent <= 0.5 ? index + 0.5 : index + 1;
      }
      
      this.updateValue(value);
      
      // 触发 change 事件
      this.triggerEvent('change', { value });
    }
  }
})