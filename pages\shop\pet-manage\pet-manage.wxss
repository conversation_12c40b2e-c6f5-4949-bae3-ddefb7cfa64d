/* pages/shop/pet-manage/pet-manage.wxss */

/* 页面容器 */
.container {
  padding: 20rpx 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 26rpx;
  color: #666;
}

/* 数据加载容器 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 60rpx;
  text-align: center;
}

/* 表单容器 */
.form-container {
  margin-bottom: 40rpx;
}

/* 表单分区 */
.form-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 30rpx;
  background-color: #4eaaa8;
  border-radius: 3rpx;
}

/* 图片上传 */
.photo-upload {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}

.photo-preview {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.photo-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.photo-preview:active .photo-mask {
  opacity: 1;
}

.mask-text {
  color: #fff;
  font-size: 24rpx;
}

.photo-placeholder {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.photo-placeholder:active {
  border-color: #4eaaa8;
  background-color: #f0f9f9;
}

.placeholder-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.placeholder-text {
  font-size: 24rpx;
  color: #999;
}

/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.item-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.required {
  color: #ff4757;
}

.item-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  font-size: 26rpx;
  background-color: #fff;
  box-sizing: border-box;
  transition: border-color 0.3s ease;
}

.item-input:focus {
  border-color: #4eaaa8;
}

.input-with-unit {
  display: flex;
  align-items: center;
  position: relative;
}

.input-with-unit .item-input {
  padding-right: 60rpx;
}

.input-unit {
  position: absolute;
  right: 20rpx;
  font-size: 26rpx;
  color: #666;
}

/* 性别选择器 */
.gender-selector {
  display: flex;
  gap: 20rpx;
}

.gender-option {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #666;
  background-color: #fff;
  transition: all 0.3s ease;
}

.gender-option.active {
  border-color: #4eaaa8;
  background-color: #4eaaa8;
  color: #fff;
}

.gender-option:active {
  transform: scale(0.98);
}

/* 提交区域 */
.submit-container {
  padding: 20rpx 0;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background-color: #4eaaa8;
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.submit-btn.disabled {
  background-color: #ccc;
  color: #999;
}

.submit-btn:not(.disabled):active {
  background-color: #3d8c8a;
  transform: scale(0.98);
}

/* 表单提示 */
.form-tip {
  text-align: center;
  margin-top: 20rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
}

/* 加载动画 */
.loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #4eaaa8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
  margin-top: 20rpx;
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background-color: #fff;
  padding: 40rpx 60rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.loading-content .loading-icon {
  width: 60rpx;
  height: 60rpx;
  border-width: 6rpx;
}

.loading-content .loading-text {
  font-size: 28rpx;
  color: #333;
  margin-top: 30rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .container {
    padding: 15rpx 20rpx;
  }
  
  .form-section {
    padding: 24rpx;
    margin-bottom: 15rpx;
  }
  
  .photo-preview, .photo-placeholder {
    width: 180rpx;
    height: 180rpx;
  }
}